<?php
/**
 * نظام Cache للعمليات الأمنية - تحسين الأداء
 */

/**
 * Cache للعمليات الأمنية
 */
class SecurityCache
{
    private static $cache = [];
    private static $cache_dir = null;
    
    /**
     * تهيئة مجلد Cache
     */
    private static function initCacheDir()
    {
        if (self::$cache_dir === null) {
            self::$cache_dir = BASE_PATH . '/storage/cache/security/';
            if (!is_dir(self::$cache_dir)) {
                mkdir(self::$cache_dir, 0755, true);
            }
        }
    }
    
    /**
     * حفظ في Cache
     *
     * @param string $key المفتاح
     * @param mixed $value القيمة
     * @param int $ttl مدة البقاء بالثواني
     * @return bool
     */
    public static function set($key, $value, $ttl = 3600)
    {
        // Cache في الذاكرة للسرعة
        self::$cache[$key] = [
            'value' => $value,
            'expires' => time() + $ttl
        ];
        
        // Cache في الملف للاستمرارية
        self::initCacheDir();
        $cache_file = self::$cache_dir . md5($key) . '.cache';
        
        $cache_data = [
            'value' => $value,
            'expires' => time() + $ttl,
            'created' => time()
        ];
        
        return file_put_contents($cache_file, serialize($cache_data)) !== false;
    }
    
    /**
     * الحصول من Cache
     *
     * @param string $key المفتاح
     * @return mixed|null
     */
    public static function get($key)
    {
        // فحص Cache الذاكرة أولاً
        if (isset(self::$cache[$key])) {
            if (self::$cache[$key]['expires'] > time()) {
                return self::$cache[$key]['value'];
            } else {
                unset(self::$cache[$key]);
            }
        }
        
        // فحص Cache الملف
        self::initCacheDir();
        $cache_file = self::$cache_dir . md5($key) . '.cache';
        
        if (file_exists($cache_file)) {
            $cache_data = unserialize(file_get_contents($cache_file));
            
            if ($cache_data && $cache_data['expires'] > time()) {
                // إعادة تحميل في ذاكرة للسرعة
                self::$cache[$key] = [
                    'value' => $cache_data['value'],
                    'expires' => $cache_data['expires']
                ];
                
                return $cache_data['value'];
            } else {
                // حذف Cache منتهي الصلاحية
                unlink($cache_file);
            }
        }
        
        return null;
    }
    
    /**
     * حذف من Cache
     *
     * @param string $key المفتاح
     * @return bool
     */
    public static function delete($key)
    {
        unset(self::$cache[$key]);
        
        self::initCacheDir();
        $cache_file = self::$cache_dir . md5($key) . '.cache';
        
        if (file_exists($cache_file)) {
            return unlink($cache_file);
        }
        
        return true;
    }
    
    /**
     * تنظيف Cache منتهي الصلاحية
     *
     * @return int عدد الملفات المحذوفة
     */
    public static function cleanup()
    {
        self::initCacheDir();
        $deleted = 0;
        $current_time = time();
        
        $files = glob(self::$cache_dir . '*.cache');
        foreach ($files as $file) {
            $cache_data = unserialize(file_get_contents($file));
            
            if (!$cache_data || $cache_data['expires'] <= $current_time) {
                unlink($file);
                $deleted++;
            }
        }
        
        // تنظيف cache الذاكرة
        foreach (self::$cache as $key => $data) {
            if ($data['expires'] <= $current_time) {
                unset(self::$cache[$key]);
            }
        }
        
        return $deleted;
    }
}

/**
 * دوال مساعدة سريعة للـ Cache
 */

/**
 * حفظ في cache أمني
 *
 * @param string $key المفتاح
 * @param mixed $value القيمة
 * @param int $ttl مدة البقاء
 * @return bool
 */
function security_cache_set($key, $value, $ttl = 3600)
{
    return SecurityCache::set($key, $value, $ttl);
}

/**
 * الحصول من cache أمني
 *
 * @param string $key المفتاح
 * @return mixed|null
 */
function security_cache_get($key)
{
    return SecurityCache::get($key);
}

/**
 * حذف من cache أمني
 *
 * @param string $key المفتاح
 * @return bool
 */
function security_cache_delete($key)
{
    return SecurityCache::delete($key);
}

/**
 * تشفير مع cache
 *
 * @param string $data البيانات
 * @param string $key مفتاح التشفير
 * @return string
 */
function encrypt_data_cached($data, $key = null)
{
    $cache_key = 'encrypt_' . md5($data . $key);
    
    // فحص Cache أولاً
    $cached = security_cache_get($cache_key);
    if ($cached !== null) {
        return $cached;
    }
    
    // تشفير جديد
    $encrypted = encrypt_data($data, $key);
    
    // حفظ في Cache لمدة ساعة
    security_cache_set($cache_key, $encrypted, 3600);
    
    return $encrypted;
}

/**
 * hash كلمة مرور مع cache
 *
 * @param string $password كلمة المرور
 * @param array $options خيارات
 * @return string
 */
function hash_password_cached($password, $options = [])
{
    $cache_key = 'hash_' . md5($password . serialize($options));
    
    // فحص Cache أولاً
    $cached = security_cache_get($cache_key);
    if ($cached !== null) {
        return $cached;
    }
    
    // hash جديد
    $hashed = hash_password($password, $options);
    
    // حفظ في Cache لمدة 24 ساعة
    security_cache_set($cache_key, $hashed, 86400);
    
    return $hashed;
}

/**
 * فحص قوة كلمة المرور مع cache
 *
 * @param string $password كلمة المرور
 * @return array
 */
function check_password_strength_cached($password)
{
    $cache_key = 'strength_' . md5($password);
    
    // فحص Cache أولاً
    $cached = security_cache_get($cache_key);
    if ($cached !== null) {
        return $cached;
    }
    
    // فحص جديد
    $result = check_password_strength($password);
    
    // حفظ في Cache لمدة ساعة
    security_cache_set($cache_key, $result, 3600);
    
    return $result;
}

/**
 * تنظيف cache أمني تلقائي
 */
function security_cache_auto_cleanup()
{
    // تنظيف كل ساعة
    $last_cleanup_file = BASE_PATH . '/storage/cache/security/.last_cleanup';
    
    if (!file_exists($last_cleanup_file) || 
        (time() - filemtime($last_cleanup_file)) > 3600) {
        
        $deleted = SecurityCache::cleanup();
        file_put_contents($last_cleanup_file, time());
        
        return $deleted;
    }
    
    return 0;
}

// تنظيف تلقائي عند التحميل
register_shutdown_function('security_cache_auto_cleanup');
?>
