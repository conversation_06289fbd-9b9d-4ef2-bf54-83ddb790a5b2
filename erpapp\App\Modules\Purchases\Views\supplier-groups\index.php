<?php
$title = 'مجموعات الموردين';
$breadcrumb = [
    ['title' => 'الرئيسية', 'url' => '/'],
    ['title' => 'المشتريات', 'url' => '/purchases'],
    ['title' => 'مجموعات الموردين', 'url' => '']
];
?>

<div class="container-fluid">
    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <?php foreach ($breadcrumb as $item): ?>
                            <?php if ($item['url']): ?>
                                <li class="breadcrumb-item"><a href="<?= base_url($item['url']) ?>"><?= $item['title'] ?></a></li>
                            <?php else: ?>
                                <li class="breadcrumb-item active"><?= $item['title'] ?></li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </ol>
                </div>
                <h4 class="page-title"><?= $title ?></h4>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row">
        <div class="col-md-6 col-xl-3">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate" title="إجمالي المجموعات">إجمالي المجموعات</h5>
                            <h3 class="my-2 py-1"><?= $stats['total_groups'] ?></h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <div id="campaign-sent-chart" data-colors="#727cf5"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-sm-5">
                            <a href="<?= base_url('purchases/supplier-groups/create') ?>" class="btn btn-danger mb-2">
                                <i class="mdi mdi-plus-circle me-2"></i> إضافة مجموعة جديدة
                            </a>
                        </div>
                        <div class="col-sm-7">
                            <div class="text-sm-end">
                                <form method="GET" class="d-inline-block">
                                    <div class="input-group">
                                        <input type="text" class="form-control" name="search" 
                                               placeholder="البحث في المجموعات..." 
                                               value="<?= htmlspecialchars($filters['search']) ?>">
                                        <button class="btn btn-primary" type="submit">
                                            <i class="mdi mdi-magnify"></i>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Table -->
                    <div class="table-responsive">
                        <table class="table table-centered table-nowrap table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>الرقم</th>
                                    <th>اسم المجموعة</th>
                                    <th>الاسم بالإنجليزية</th>
                                    <th>المجموعة الافتراضية</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th style="width: 125px;">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($supplierGroups)): ?>
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="mdi mdi-folder-open-outline mdi-48px"></i>
                                                <p class="mt-2">لا توجد مجموعات موردين</p>
                                                <a href="<?= base_url('purchases/supplier-groups/create') ?>" class="btn btn-primary btn-sm">
                                                    إضافة مجموعة جديدة
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($supplierGroups as $group): ?>
                                        <tr>
                                            <td><?= $group['group_number'] ?></td>
                                            <td>
                                                <a href="<?= base_url('purchases/supplier-groups/' . $group['group_number']) ?>" 
                                                   class="text-body fw-bold">
                                                    <?= htmlspecialchars($group['name_ar']) ?>
                                                </a>
                                            </td>
                                            <td><?= htmlspecialchars($group['name_en'] ?: '-') ?></td>
                                            <td>
                                                <?php if (isset($group['is_default']) && $group['is_default']): ?>
                                                    <span class="badge bg-success">
                                                        <i class="mdi mdi-check-circle me-1"></i>
                                                        افتراضي
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= date('Y-m-d', strtotime($group['created_at'])) ?></td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('purchases/supplier-groups/' . $group['group_number']) ?>" 
                                                       class="btn btn-primary btn-sm" title="عرض">
                                                        <i class="mdi mdi-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('purchases/supplier-groups/' . $group['group_number'] . '/edit') ?>" 
                                                       class="btn btn-success btn-sm" title="تعديل">
                                                        <i class="mdi mdi-pencil"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-danger btn-sm" 
                                                            onclick="confirmDelete(<?= $group['group_number'] ?>)" title="حذف">
                                                        <i class="mdi mdi-delete"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">تأكيد الحذف</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف هذه المجموعة؟</p>
                <p class="text-muted">لا يمكن التراجع عن هذا الإجراء.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(groupNumber) {
    document.getElementById('deleteForm').action = '<?= base_url('purchases/supplier-groups/') ?>' + groupNumber + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
