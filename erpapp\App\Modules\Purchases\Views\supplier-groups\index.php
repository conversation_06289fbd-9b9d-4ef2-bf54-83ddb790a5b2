<?php
$title = 'مجموعات الموردين';
$breadcrumb = [
    ['title' => 'الرئيسية', 'url' => '/'],
    ['title' => 'المشتريات', 'url' => '/purchases'],
    ['title' => 'مجموعات الموردين', 'url' => '']
];
?>

<div class="container-fluid">
    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <?php foreach ($breadcrumb as $item): ?>
                            <?php if ($item['url']): ?>
                                <li class="breadcrumb-item"><a href="<?= base_url($item['url']) ?>"><?= $item['title'] ?></a></li>
                            <?php else: ?>
                                <li class="breadcrumb-item active"><?= $item['title'] ?></li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </ol>
                </div>
                <h4 class="page-title"><?= $title ?></h4>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row">
        <div class="col-md-6 col-xl-3">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate" title="إجمالي المجموعات">إجمالي المجموعات</h5>
                            <h3 class="my-2 py-1"><?= $stats['total_groups'] ?></h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <div id="campaign-sent-chart" data-colors="#727cf5"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-sm-5">
                            <a href="<?= base_url('purchases/supplier-groups/create') ?>" class="btn btn-danger mb-2">
                                <i class="mdi mdi-plus-circle me-2"></i> إضافة مجموعة جديدة
                            </a>
                        </div>
                        <div class="col-sm-7">
                            <div class="text-sm-end">
                                <form method="GET" class="d-inline-block">
                                    <div class="row g-2">
                                        <div class="col-auto">
                                            <select name="per_page" class="form-select form-select-sm" onchange="this.form.submit()">
                                                <option value="10" <?= $filters['per_page'] == 10 ? 'selected' : '' ?>>10 عناصر</option>
                                                <option value="20" <?= $filters['per_page'] == 20 ? 'selected' : '' ?>>20 عنصر</option>
                                                <option value="50" <?= $filters['per_page'] == 50 ? 'selected' : '' ?>>50 عنصر</option>
                                                <option value="100" <?= $filters['per_page'] == 100 ? 'selected' : '' ?>>100 عنصر</option>
                                            </select>
                                        </div>
                                        <div class="col-auto">
                                            <div class="input-group">
                                                <input type="text" class="form-control form-control-sm" name="search"
                                                       placeholder="البحث في المجموعات..."
                                                       value="<?= htmlspecialchars($filters['search']) ?>">
                                                <button class="btn btn-primary btn-sm" type="submit">
                                                    <i class="mdi mdi-magnify"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <!-- الحفاظ على الصفحة الحالية عند الفلترة -->
                                        <input type="hidden" name="page" value="<?= $pagination['current_page'] ?>">
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Table -->
                    <div class="table-responsive">
                        <table class="table table-centered table-nowrap table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>الرقم</th>
                                    <th>اسم المجموعة</th>
                                    <th>الاسم بالإنجليزية</th>
                                    <th>المجموعة الافتراضية</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th style="width: 125px;">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($supplierGroups)): ?>
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="mdi mdi-folder-open-outline mdi-48px"></i>
                                                <p class="mt-2">لا توجد مجموعات موردين</p>
                                                <a href="<?= base_url('purchases/supplier-groups/create') ?>" class="btn btn-primary btn-sm">
                                                    إضافة مجموعة جديدة
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($supplierGroups as $group): ?>
                                        <tr>
                                            <td><?= $group['group_number'] ?></td>
                                            <td>
                                                <a href="<?= base_url('purchases/supplier-groups/' . $group['group_number']) ?>" 
                                                   class="text-body fw-bold">
                                                    <?= htmlspecialchars($group['name_ar']) ?>
                                                </a>
                                            </td>
                                            <td><?= htmlspecialchars($group['name_en'] ?: '-') ?></td>
                                            <td>
                                                <?php if (isset($group['is_default']) && $group['is_default']): ?>
                                                    <span class="badge bg-success">
                                                        <i class="mdi mdi-check-circle me-1"></i>
                                                        افتراضي
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= date('Y-m-d', strtotime($group['created_at'])) ?></td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('purchases/supplier-groups/' . $group['group_number']) ?>" 
                                                       class="btn btn-primary btn-sm" title="عرض">
                                                        <i class="mdi mdi-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('purchases/supplier-groups/' . $group['group_number'] . '/edit') ?>" 
                                                       class="btn btn-success btn-sm" title="تعديل">
                                                        <i class="mdi mdi-pencil"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-danger btn-sm" 
                                                            onclick="confirmDelete(<?= $group['group_number'] ?>)" title="حذف">
                                                        <i class="mdi mdi-delete"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination Info and Controls -->
                    <?php if ($pagination['total_items'] > 0): ?>
                        <div class="row align-items-center mt-3">
                            <div class="col-md-6">
                                <div class="dataTables_info">
                                    عرض <?= $pagination['start_item'] ?> إلى <?= $pagination['end_item'] ?>
                                    من <?= $pagination['total_items'] ?> عنصر
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="dataTables_paginate paging_simple_numbers float-end">
                                    <?php if ($pagination['total_pages'] > 1): ?>
                                        <ul class="pagination pagination-rounded mb-0">
                                            <!-- Previous Button -->
                                            <li class="page-item <?= !$pagination['has_previous'] ? 'disabled' : '' ?>">
                                                <?php if ($pagination['has_previous']): ?>
                                                    <a class="page-link" href="<?= buildPaginationUrl($pagination['previous_page'], $filters) ?>">
                                                        السابق
                                                    </a>
                                                <?php else: ?>
                                                    <span class="page-link">السابق</span>
                                                <?php endif; ?>
                                            </li>

                                            <!-- Page Numbers -->
                                            <?php
                                            $start = max(1, $pagination['current_page'] - 2);
                                            $end = min($pagination['total_pages'], $pagination['current_page'] + 2);

                                            // إضافة الصفحة الأولى إذا لم تكن ضمن النطاق
                                            if ($start > 1): ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="<?= buildPaginationUrl(1, $filters) ?>">1</a>
                                                </li>
                                                <?php if ($start > 2): ?>
                                                    <li class="page-item disabled">
                                                        <span class="page-link">...</span>
                                                    </li>
                                                <?php endif; ?>
                                            <?php endif; ?>

                                            <!-- الصفحات الحالية -->
                                            <?php for ($i = $start; $i <= $end; $i++): ?>
                                                <li class="page-item <?= $i == $pagination['current_page'] ? 'active' : '' ?>">
                                                    <?php if ($i == $pagination['current_page']): ?>
                                                        <span class="page-link"><?= $i ?></span>
                                                    <?php else: ?>
                                                        <a class="page-link" href="<?= buildPaginationUrl($i, $filters) ?>"><?= $i ?></a>
                                                    <?php endif; ?>
                                                </li>
                                            <?php endfor; ?>

                                            <!-- إضافة الصفحة الأخيرة إذا لم تكن ضمن النطاق -->
                                            <?php if ($end < $pagination['total_pages']): ?>
                                                <?php if ($end < $pagination['total_pages'] - 1): ?>
                                                    <li class="page-item disabled">
                                                        <span class="page-link">...</span>
                                                    </li>
                                                <?php endif; ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="<?= buildPaginationUrl($pagination['total_pages'], $filters) ?>">
                                                        <?= $pagination['total_pages'] ?>
                                                    </a>
                                                </li>
                                            <?php endif; ?>

                                            <!-- Next Button -->
                                            <li class="page-item <?= !$pagination['has_next'] ? 'disabled' : '' ?>">
                                                <?php if ($pagination['has_next']): ?>
                                                    <a class="page-link" href="<?= buildPaginationUrl($pagination['next_page'], $filters) ?>">
                                                        التالي
                                                    </a>
                                                <?php else: ?>
                                                    <span class="page-link">التالي</span>
                                                <?php endif; ?>
                                            </li>
                                        </ul>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">تأكيد الحذف</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف هذه المجموعة؟</p>
                <p class="text-muted">لا يمكن التراجع عن هذا الإجراء.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
/**
 * بناء رابط pagination مع الحفاظ على الفلاتر
 */
function buildPaginationUrl($page, $filters) {
    $params = [
        'page' => $page,
        'per_page' => $filters['per_page'] ?? 20
    ];

    if (!empty($filters['search'])) {
        $params['search'] = $filters['search'];
    }

    return base_url('purchases/supplier-groups') . '?' . http_build_query($params);
}
?>

<script>
function confirmDelete(groupNumber) {
    document.getElementById('deleteForm').action = '<?= base_url('purchases/supplier-groups/') ?>' + groupNumber + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>

<style>
/* تحسين مظهر pagination */
.dataTables_info {
    color: #6c757d;
    font-size: 0.875rem;
    padding-top: 0.5rem;
}

.pagination-rounded .page-link {
    border-radius: 0.375rem !important;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: #6c757d;
}

.pagination-rounded .page-item.active .page-link {
    background-color: #727cf5;
    border-color: #727cf5;
    color: white;
}

.pagination-rounded .page-link:hover {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    color: #495057;
}

.pagination-rounded .page-item.disabled .page-link {
    color: #adb5bd;
    background-color: #fff;
    border-color: #dee2e6;
}

/* تحسين مظهر الجدول */
.table-responsive {
    border-radius: 0.375rem;
}

.table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.table td {
    vertical-align: middle;
}

/* تحسين مظهر الفلاتر */
.form-select-sm {
    font-size: 0.875rem;
}

.btn-group .btn {
    border-radius: 0.25rem;
}

.btn-group .btn + .btn {
    margin-left: 2px;
}
</style>
