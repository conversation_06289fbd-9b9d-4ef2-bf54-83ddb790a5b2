<?php
/**
 * صفحة الموردين الجديدة باستخدام النظام الموحد
 * مثال على كيفية استخدام datatable_helper.php
 */

// إعداد الأعمدة
$columns = [
    [
        'field' => 'entity_number',
        'title' => 'الرقم',
        'type' => 'text',
        'width' => '120px',
        'sortable' => true,
        'data_type' => 'number'
    ],
    [
        'field' => 'G_name_ar',
        'title' => 'اسم المورد',
        'type' => 'link',
        'url' => 'purchases/suppliers/{entity_number}',
        'subtitle_field' => 'G_name_en',
        'sortable' => true,
        'data_type' => 'text'
    ],
    [
        'field' => 'S_company_name',
        'title' => 'اسم الشركة',
        'type' => 'text',
        'sortable' => true,
        'data_type' => 'text'
    ],
    [
        'field' => 'group_name',
        'title' => 'المجموعة',
        'type' => 'badge',
        'sortable' => true,
        'data_type' => 'text'
    ],
    [
        'field' => 'G_phone',
        'title' => 'الهاتف',
        'type' => 'text',
        'sortable' => true,
        'data_type' => 'text'
    ],
    [
        'field' => 'G_status',
        'title' => 'الحالة',
        'type' => 'badge',
        'sortable' => true,
        'data_type' => 'text',
        'status_config' => [
            'classes' => [
                'active' => 'success',
                'inactive' => 'secondary',
                'suspended' => 'warning'
            ],
            'texts' => [
                'active' => 'نشط',
                'inactive' => 'غير نشط',
                'suspended' => 'معلق'
            ]
        ]
    ],
    [
        'field' => 'actions',
        'title' => 'الإجراءات',
        'type' => 'actions',
        'width' => '125px',
        'buttons' => [
            [
                'type' => 'link',
                'url' => 'purchases/suppliers/{entity_number}',
                'class' => 'btn-primary',
                'icon' => 'mdi mdi-eye',
                'title' => 'عرض'
            ],
            [
                'type' => 'link',
                'url' => 'purchases/suppliers/{entity_number}/edit',
                'class' => 'btn-success',
                'icon' => 'mdi mdi-pencil',
                'title' => 'تعديل'
            ],
            [
                'type' => 'button',
                'class' => 'btn-danger',
                'icon' => 'mdi mdi-delete',
                'title' => 'حذف',
                'onclick' => 'confirmDelete({entity_number})'
            ]
        ]
    ]
];

// إعداد الإجراءات
$actions = [
    [
        'type' => 'primary',
        'url' => 'purchases/suppliers/create',
        'icon' => 'mdi mdi-plus-circle',
        'text' => 'إضافة مورد جديد'
    ]
];

// إعداد الفلاتر
$filters_config = [
    [
        'name' => 'search',
        'type' => 'search',
        'label' => 'البحث في الموردين',
        'placeholder' => 'ابحث بالاسم أو اسم الشركة...',
        'icon' => 'mdi mdi-magnify',
        'col_size' => 6,
        'help' => 'البحث في الاسم العربي، الإنجليزي، أو اسم الشركة'
    ],
    [
        'name' => 'status',
        'type' => 'select',
        'label' => 'حالة المورد',
        'placeholder' => 'جميع الحالات',
        'icon' => 'mdi mdi-check-circle',
        'col_size' => 3,
        'options' => [
            'active' => 'نشط',
            'inactive' => 'غير نشط',
            'suspended' => 'معلق'
        ]
    ],
    [
        'name' => 'group_id',
        'type' => 'select',
        'label' => 'مجموعة الموردين',
        'placeholder' => 'جميع المجموعات',
        'icon' => 'mdi mdi-folder-multiple',
        'col_size' => 3,
        'options' => array_column($supplierGroups ?? [], 'name_ar', 'group_number')
    ]
];

// إعداد الإحصائيات
$stats_cards = [
    [
        'title' => 'إجمالي الموردين',
        'value' => $stats['total_suppliers'] ?? 0,
        'icon' => 'mdi mdi-truck',
        'color' => 'muted'
    ],
    [
        'title' => 'الموردين النشطين',
        'value' => $stats['active_suppliers'] ?? 0,
        'icon' => 'mdi mdi-check-circle',
        'color' => 'success'
    ]
];

// إعداد Empty State
$empty_state = [
    'icon' => 'mdi mdi-truck-outline',
    'message' => 'لا توجد موردين',
    'action' => [
        'url' => 'purchases/suppliers/create',
        'text' => 'إضافة مورد جديد'
    ]
];

// إعداد breadcrumb آمن
$safe_breadcrumb = [
    ['title' => 'المشتريات', 'url' => 'purchases'],
    ['title' => 'الموردين', 'active' => true]
];

// استخدام النظام الموحد
render_datatable_page([
    'title' => $title ?? 'الموردين',
    'module' => 'purchases',
    'entity' => 'suppliers',
    'data' => $suppliers ?? [],
    'columns' => $columns,
    'stats' => $stats_cards,
    'pagination' => $pagination ?? [],
    'filters' => $filters ?? [],
    'breadcrumb' => $safe_breadcrumb,
    'actions' => $actions,
    'filters_config' => $filters_config,
    'empty_state' => $empty_state
]);
?>
