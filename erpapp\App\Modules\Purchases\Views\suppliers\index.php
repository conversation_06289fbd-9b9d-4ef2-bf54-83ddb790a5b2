<?php
$title = 'الموردين';
$breadcrumb = [
    ['title' => 'الرئيسية', 'url' => '/'],
    ['title' => 'المشتريات', 'url' => '/purchases'],
    ['title' => 'الموردين', 'url' => '']
];

// مساعد الفلاتر محمل تلقائياً من loader.php
?>

<div class="container-fluid">
    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <?php foreach ($breadcrumb as $item): ?>
                            <?php if ($item['url']): ?>
                                <li class="breadcrumb-item"><a href="<?= base_url($item['url']) ?>"><?= $item['title'] ?></a></li>
                            <?php else: ?>
                                <li class="breadcrumb-item active"><?= $item['title'] ?></li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </ol>
                </div>
                <h4 class="page-title"><?= $title ?></h4>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row">
        <div class="col-md-6 col-xl-3">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate">إجمالي الموردين</h5>
                            <h3 class="my-2 py-1"><?= $stats['total_suppliers'] ?></h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="mdi mdi-truck text-muted" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-xl-3">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate">الموردين النشطين</h5>
                            <h3 class="my-2 py-1"><?= $stats['active_suppliers'] ?></h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="mdi mdi-check-circle text-success" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-sm-5">
                            <a href="<?= base_url('purchases/suppliers/create') ?>" class="btn btn-danger mb-2">
                                <i class="mdi mdi-plus-circle me-2"></i> إضافة مورد جديد
                            </a>
                        </div>
                        <div class="col-sm-7">
                            <div class="text-sm-end">
                                <div class="row g-2">
                                    <!-- عدد العناصر لكل صفحة -->
                                    <div class="col-auto">
                                        <form method="POST" action="<?= base_url('purchases/suppliers/apply-filters') ?>" class="d-inline-block">
                                            <select class="form-select form-select-sm" name="per_page" onchange="this.form.submit()">
                                                <option value="10" <?= $filters['per_page'] == 10 ? 'selected' : '' ?>>10 عناصر</option>
                                                <option value="20" <?= $filters['per_page'] == 20 ? 'selected' : '' ?>>20 عنصر</option>
                                                <option value="50" <?= $filters['per_page'] == 50 ? 'selected' : '' ?>>50 عنصر</option>
                                                <option value="100" <?= $filters['per_page'] == 100 ? 'selected' : '' ?>>100 عنصر</option>
                                            </select>
                                            <!-- الحفاظ على الفلاتر الحالية -->
                                            <input type="hidden" name="search" value="<?= htmlspecialchars($filters['search']) ?>">
                                            <input type="hidden" name="status" value="<?= htmlspecialchars($filters['status']) ?>">
                                            <input type="hidden" name="group_id" value="<?= htmlspecialchars($filters['group_id']) ?>">
                                        </form>
                                    </div>

                                    <!-- زر الفلاتر -->
                                    <div class="col-auto">
                                        <button type="button" class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#filtersModal">
                                            <i class="mdi mdi-filter-variant me-1"></i> فلاتر
                                            <?php
                                            $activeFilters = count_saved_filters('suppliers');
                                            if ($activeFilters > 0): ?>
                                                <span class="badge bg-danger ms-1"><?= $activeFilters ?></span>
                                            <?php endif; ?>
                                        </button>
                                    </div>

                                    <!-- زر مسح الفلاتر -->
                                    <?php if ($activeFilters > 0): ?>
                                        <div class="col-auto">
                                            <a href="<?= base_url('purchases/suppliers/clear-filters') ?>" class="btn btn-outline-secondary btn-sm">
                                                <i class="mdi mdi-filter-remove me-1"></i> مسح الفلاتر
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Table -->
                    <div class="table-responsive">
                        <table class="table table-centered table-nowrap table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>الرقم</th>
                                    <th>اسم المورد</th>
                                    <th>اسم الشركة</th>
                                    <th>المجموعة</th>
                                    <th>الهاتف</th>
                                    <th>الحالة</th>
                                    <th style="width: 125px;">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($suppliers)): ?>
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="mdi mdi-truck-outline mdi-48px"></i>
                                                <p class="mt-2">لا توجد موردين</p>
                                                <a href="<?= base_url('purchases/suppliers/create') ?>" class="btn btn-primary btn-sm">
                                                    إضافة مورد جديد
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($suppliers as $supplier): ?>
                                        <tr>
                                            <td><?= $supplier['entity_number'] ?></td>
                                            <td>
                                                <a href="<?= base_url('purchases/suppliers/' . $supplier['entity_number']) ?>" 
                                                   class="text-body fw-bold">
                                                    <?= htmlspecialchars($supplier['G_name_ar']) ?>
                                                </a>
                                                <?php if ($supplier['G_name_en']): ?>
                                                    <br><small class="text-muted"><?= htmlspecialchars($supplier['G_name_en']) ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= htmlspecialchars($supplier['S_company_name'] ?: '-') ?></td>
                                            <td><?= htmlspecialchars($supplier['group_name'] ?: '-') ?></td>
                                            <td><?= htmlspecialchars($supplier['G_phone'] ?: '-') ?></td>
                                            <td>
                                                <?php
                                                $statusClass = [
                                                    'active' => 'success',
                                                    'inactive' => 'secondary',
                                                    'suspended' => 'warning'
                                                ];
                                                $statusText = [
                                                    'active' => 'نشط',
                                                    'inactive' => 'غير نشط',
                                                    'suspended' => 'معلق'
                                                ];
                                                ?>
                                                <span class="badge bg-<?= $statusClass[$supplier['G_status']] ?>">
                                                    <?= $statusText[$supplier['G_status']] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('purchases/suppliers/' . $supplier['entity_number']) ?>" 
                                                       class="btn btn-primary btn-sm" title="عرض">
                                                        <i class="mdi mdi-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('purchases/suppliers/' . $supplier['entity_number'] . '/edit') ?>" 
                                                       class="btn btn-success btn-sm" title="تعديل">
                                                        <i class="mdi mdi-pencil"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-danger btn-sm" 
                                                            onclick="confirmDelete(<?= $supplier['entity_number'] ?>)" title="حذف">
                                                        <i class="mdi mdi-delete"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination Info and Controls -->
                    <?php if ($pagination['total_items'] > 0): ?>
                        <div class="row align-items-center mt-3">
                            <div class="col-md-6">
                                <div class="dataTables_info">
                                    عرض <?= $pagination['start_item'] ?> إلى <?= $pagination['end_item'] ?>
                                    من <?= $pagination['total_items'] ?> عنصر
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="dataTables_paginate paging_simple_numbers float-end">
                                    <?php if ($pagination['total_pages'] > 1): ?>
                                        <ul class="pagination pagination-rounded mb-0">
                                            <!-- Previous Button -->
                                            <li class="page-item <?= !$pagination['has_previous'] ? 'disabled' : '' ?>">
                                                <?php if ($pagination['has_previous']): ?>
                                                    <a class="page-link" href="<?= buildPaginationUrl($pagination['previous_page'], $filters) ?>">
                                                        السابق
                                                    </a>
                                                <?php else: ?>
                                                    <span class="page-link">السابق</span>
                                                <?php endif; ?>
                                            </li>

                                            <!-- Page Numbers -->
                                            <?php
                                            $start = max(1, $pagination['current_page'] - 2);
                                            $end = min($pagination['total_pages'], $pagination['current_page'] + 2);

                                            // إضافة الصفحة الأولى إذا لم تكن ضمن النطاق
                                            if ($start > 1): ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="<?= buildPaginationUrl(1, $filters) ?>">1</a>
                                                </li>
                                                <?php if ($start > 2): ?>
                                                    <li class="page-item disabled">
                                                        <span class="page-link">...</span>
                                                    </li>
                                                <?php endif; ?>
                                            <?php endif; ?>

                                            <!-- الصفحات الحالية -->
                                            <?php for ($i = $start; $i <= $end; $i++): ?>
                                                <li class="page-item <?= $i == $pagination['current_page'] ? 'active' : '' ?>">
                                                    <?php if ($i == $pagination['current_page']): ?>
                                                        <span class="page-link"><?= $i ?></span>
                                                    <?php else: ?>
                                                        <a class="page-link" href="<?= buildPaginationUrl($i, $filters) ?>"><?= $i ?></a>
                                                    <?php endif; ?>
                                                </li>
                                            <?php endfor; ?>

                                            <!-- إضافة الصفحة الأخيرة إذا لم تكن ضمن النطاق -->
                                            <?php if ($end < $pagination['total_pages']): ?>
                                                <?php if ($end < $pagination['total_pages'] - 1): ?>
                                                    <li class="page-item disabled">
                                                        <span class="page-link">...</span>
                                                    </li>
                                                <?php endif; ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="<?= buildPaginationUrl($pagination['total_pages'], $filters) ?>">
                                                        <?= $pagination['total_pages'] ?>
                                                    </a>
                                                </li>
                                            <?php endif; ?>

                                            <!-- Next Button -->
                                            <li class="page-item <?= !$pagination['has_next'] ? 'disabled' : '' ?>">
                                                <?php if ($pagination['has_next']): ?>
                                                    <a class="page-link" href="<?= buildPaginationUrl($pagination['next_page'], $filters) ?>">
                                                        التالي
                                                    </a>
                                                <?php else: ?>
                                                    <span class="page-link">التالي</span>
                                                <?php endif; ?>
                                            </li>
                                        </ul>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters Modal -->
<div class="modal fade" id="filtersModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-light">
                <h5 class="modal-title">
                    <i class="mdi mdi-filter-variant me-2"></i>
                    فلاتر البحث المتقدم
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="filtersForm" method="POST" action="<?= base_url('purchases/suppliers/apply-filters') ?>">
                <div class="modal-body">
                    <div class="row g-3">
                        <!-- البحث -->
                        <div class="col-md-6">
                            <label for="search" class="form-label">
                                <i class="mdi mdi-magnify me-1"></i>
                                البحث في الموردين
                            </label>
                            <input type="text" class="form-control" id="search" name="search"
                                   placeholder="ابحث بالاسم أو اسم الشركة..."
                                   value="<?= htmlspecialchars($filters['search']) ?>">
                            <div class="form-text">البحث في الاسم العربي، الإنجليزي، أو اسم الشركة</div>
                        </div>

                        <!-- الحالة -->
                        <div class="col-md-6">
                            <label for="status" class="form-label">
                                <i class="mdi mdi-check-circle me-1"></i>
                                حالة المورد
                            </label>
                            <select class="form-select" id="status" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="active" <?= $filters['status'] === 'active' ? 'selected' : '' ?>>
                                    <span class="text-success">●</span> نشط
                                </option>
                                <option value="inactive" <?= $filters['status'] === 'inactive' ? 'selected' : '' ?>>
                                    <span class="text-secondary">●</span> غير نشط
                                </option>
                                <option value="suspended" <?= $filters['status'] === 'suspended' ? 'selected' : '' ?>>
                                    <span class="text-warning">●</span> معلق
                                </option>
                            </select>
                        </div>

                        <!-- المجموعة -->
                        <div class="col-md-6">
                            <label for="group_id" class="form-label">
                                <i class="mdi mdi-folder-multiple me-1"></i>
                                مجموعة الموردين
                            </label>
                            <select class="form-select" id="group_id" name="group_id">
                                <option value="">جميع المجموعات</option>
                                <?php foreach ($supplierGroups as $group): ?>
                                    <option value="<?= $group['group_number'] ?>"
                                            <?= $filters['group_id'] == $group['group_number'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($group['name_ar']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- معلومات إضافية -->
                        <div class="col-md-6">
                            <label class="form-label">
                                <i class="mdi mdi-information me-1"></i>
                                معلومات الفلترة
                            </label>
                            <div class="bg-light p-3 rounded">
                                <small class="text-muted">
                                    <strong>إجمالي الموردين:</strong> <?= $stats['total_suppliers'] ?? 0 ?><br>
                                    <strong>الموردين النشطين:</strong> <?= $stats['active_suppliers'] ?? 0 ?><br>
                                    <strong>النتائج الحالية:</strong> <?= $pagination['total_items'] ?><br>
                                    <?php if (has_active_filters($filters)): ?>
                                        <span class="text-primary">
                                            <i class="mdi mdi-filter-check me-1"></i>
                                            <strong>الفلاتر محفوظة تلقائياً</strong>
                                        </span>
                                    <?php endif; ?>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer bg-light">
                    <div class="row w-100">
                        <div class="col-6">
                            <button type="button" class="btn btn-outline-secondary w-100" onclick="clearAllFilters()">
                                <i class="mdi mdi-filter-remove me-1"></i>
                                مسح جميع الفلاتر
                            </button>
                        </div>
                        <div class="col-6">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="mdi mdi-filter-check me-1"></i>
                                تطبيق الفلاتر
                            </button>
                        </div>
                    </div>
                </div>
                <!-- الحفاظ على عدد العناصر لكل صفحة -->
                <input type="hidden" id="per_page_hidden" name="per_page" value="<?= $filters['per_page'] ?>">
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">تأكيد الحذف</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف هذا المورد؟</p>
                <p class="text-muted">لا يمكن التراجع عن هذا الإجراء.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
/**
 * بناء رابط pagination نظيف (متوافق مع سياستنا)
 * يستخدم POST form لحفظ رقم الصفحة في قاعدة البيانات
 */
function buildPaginationUrl($page, $filters) {
    // إرجاع JavaScript لإرسال POST form بدلاً من GET
    return "javascript:goToPage($page)";
}
?>

<script>
function confirmDelete(supplierNumber) {
    document.getElementById('deleteForm').action = '<?= base_url('purchases/suppliers/') ?>' + supplierNumber + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

// دالة تطبيق الفلاتر - الآن تستخدم form submission عادي
// لا حاجة لـ JavaScript معقد

// دالة مسح جميع الفلاتر
function clearAllFilters() {
    if (confirm('هل أنت متأكد من مسح جميع الفلاتر؟')) {
        window.location.href = '<?= base_url('purchases/suppliers/clear-filters') ?>';
    }
}

// دالة مسح الفلاتر في النافذة فقط (بدون حفظ)
function clearFilters() {
    document.getElementById('search').value = '';
    document.getElementById('status').value = '';
    document.getElementById('group_id').value = '';
}

// دالة الانتقال لصفحة معينة (متوافقة مع سياستنا)
function goToPage(page) {
    // إنشاء form مؤقت لإرسال رقم الصفحة عبر POST
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '<?= base_url('purchases/suppliers/apply-filters') ?>';

    // إضافة رقم الصفحة
    const pageInput = document.createElement('input');
    pageInput.type = 'hidden';
    pageInput.name = 'current_page';
    pageInput.value = page;
    form.appendChild(pageInput);

    // إضافة الفلاتر الحالية للحفاظ عليها
    const currentFilters = <?= json_encode($filters) ?>;
    for (const [key, value] of Object.entries(currentFilters)) {
        if (key !== 'current_page') {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = key;
            input.value = value;
            form.appendChild(input);
        }
    }

    // إرسال النموذج
    document.body.appendChild(form);
    form.submit();
}

// دالة تغيير عدد العناصر - الآن تستخدم form submission عادي
// لا حاجة لـ JavaScript معقد

// تحسين تجربة المستخدم للفلاتر
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات بصرية للفلاتر النشطة
    const filterButton = document.querySelector('[data-bs-target="#filtersModal"]');
    const activeFiltersCount = <?= count_saved_filters('suppliers') ?>;

    if (activeFiltersCount > 0) {
        filterButton.classList.add('btn-primary');
        filterButton.classList.remove('btn-outline-primary');
    }

    // تنظيف URL من معاملات pagination (متوافق مع سياستنا)
    // إزالة أي معاملات من URL للحفاظ على نظافة الروابط
    if (window.location.search) {
        window.history.replaceState({}, '', window.location.pathname);
    }

    // تحسين عرض النتائج عند فتح modal الفلاتر
    const filtersModal = document.getElementById('filtersModal');
    filtersModal.addEventListener('shown.bs.modal', function() {
        document.getElementById('search').focus();
    });

    // إضافة تأثير hover للأزرار
    const buttons = document.querySelectorAll('.btn-sm');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-1px)';
            this.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    });
});
</script>

<style>
/* تحسين مظهر pagination */
.dataTables_info {
    color: #6c757d;
    font-size: 0.875rem;
    padding-top: 0.5rem;
}

.pagination-rounded .page-link {
    border-radius: 0.375rem !important;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: #6c757d;
}

.pagination-rounded .page-item.active .page-link {
    background-color: #727cf5;
    border-color: #727cf5;
    color: white;
}

.pagination-rounded .page-link:hover {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    color: #495057;
}

.pagination-rounded .page-item.disabled .page-link {
    color: #adb5bd;
    background-color: #fff;
    border-color: #dee2e6;
}

/* تحسين مظهر الجدول */
.table-responsive {
    border-radius: 0.375rem;
}

.table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.table td {
    vertical-align: middle;
}

/* تحسين مظهر الفلاتر */
.form-select-sm {
    font-size: 0.875rem;
}

.btn-group .btn {
    border-radius: 0.25rem;
}

.btn-group .btn + .btn {
    margin-left: 2px;
}

/* تحسين مظهر نافذة الفلاتر */
.modal-header.bg-light {
    border-bottom: 2px solid #e9ecef;
}

.modal-footer.bg-light {
    border-top: 2px solid #e9ecef;
}

.modal-title i {
    color: #727cf5;
}

.form-label i {
    color: #6c757d;
}

/* تحسين مظهر زر الفلاتر */
.btn-outline-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(114, 124, 245, 0.3);
}

.btn-primary {
    background-color: #727cf5;
    border-color: #727cf5;
}

.btn-primary:hover {
    background-color: #5a67d8;
    border-color: #5a67d8;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(114, 124, 245, 0.4);
}

/* تحسين مظهر badge العدد */
.badge.bg-danger {
    font-size: 0.7rem;
    padding: 0.25em 0.5em;
}

/* تحسين مظهر معلومات الفلترة */
.bg-light.p-3.rounded {
    border: 1px solid #e9ecef;
    background-color: #f8f9fa !important;
}

/* تأثيرات انتقالية */
.btn {
    transition: all 0.2s ease-in-out;
}

.modal-content {
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* تحسين مظهر الحقول */
.form-control:focus,
.form-select:focus {
    border-color: #727cf5;
    box-shadow: 0 0 0 0.2rem rgba(114, 124, 245, 0.25);
}

/* تحسين مظهر النص المساعد */
.form-text {
    font-size: 0.8rem;
    color: #6c757d;
}
</style>
