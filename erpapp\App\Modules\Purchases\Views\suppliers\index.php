<?php
$title = 'الموردين';
$breadcrumb = [
    ['title' => 'الرئيسية', 'url' => '/'],
    ['title' => 'المشتريات', 'url' => '/purchases'],
    ['title' => 'الموردين', 'url' => '']
];
?>

<div class="container-fluid">
    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <?php foreach ($breadcrumb as $item): ?>
                            <?php if ($item['url']): ?>
                                <li class="breadcrumb-item"><a href="<?= base_url($item['url']) ?>"><?= $item['title'] ?></a></li>
                            <?php else: ?>
                                <li class="breadcrumb-item active"><?= $item['title'] ?></li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </ol>
                </div>
                <h4 class="page-title"><?= $title ?></h4>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row">
        <div class="col-md-6 col-xl-3">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate">إجمالي الموردين</h5>
                            <h3 class="my-2 py-1"><?= $stats['total_suppliers'] ?></h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="mdi mdi-truck text-muted" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-xl-3">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate">الموردين النشطين</h5>
                            <h3 class="my-2 py-1"><?= $stats['active_suppliers'] ?></h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="mdi mdi-check-circle text-success" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-sm-5">
                            <a href="<?= base_url('purchases/suppliers/create') ?>" class="btn btn-danger mb-2">
                                <i class="mdi mdi-plus-circle me-2"></i> إضافة مورد جديد
                            </a>
                        </div>
                        <div class="col-sm-7">
                            <div class="text-sm-end">
                                <form method="GET" class="d-inline-block">
                                    <div class="row g-2">
                                        <div class="col-auto">
                                            <select name="status" class="form-select form-select-sm">
                                                <option value="">جميع الحالات</option>
                                                <option value="active" <?= $filters['status'] === 'active' ? 'selected' : '' ?>>نشط</option>
                                                <option value="inactive" <?= $filters['status'] === 'inactive' ? 'selected' : '' ?>>غير نشط</option>
                                                <option value="suspended" <?= $filters['status'] === 'suspended' ? 'selected' : '' ?>>معلق</option>
                                            </select>
                                        </div>
                                        <div class="col-auto">
                                            <select name="group_id" class="form-select form-select-sm">
                                                <option value="">جميع المجموعات</option>
                                                <?php foreach ($supplierGroups as $group): ?>
                                                    <option value="<?= $group['group_number'] ?>" 
                                                            <?= $filters['group_id'] == $group['group_number'] ? 'selected' : '' ?>>
                                                        <?= htmlspecialchars($group['name_ar']) ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <div class="col-auto">
                                            <div class="input-group">
                                                <input type="text" class="form-control form-control-sm" name="search" 
                                                       placeholder="البحث في الموردين..." 
                                                       value="<?= htmlspecialchars($filters['search']) ?>">
                                                <button class="btn btn-primary btn-sm" type="submit">
                                                    <i class="mdi mdi-magnify"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Table -->
                    <div class="table-responsive">
                        <table class="table table-centered table-nowrap table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>الرقم</th>
                                    <th>اسم المورد</th>
                                    <th>اسم الشركة</th>
                                    <th>المجموعة</th>
                                    <th>الهاتف</th>
                                    <th>الحالة</th>
                                    <th style="width: 125px;">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($suppliers)): ?>
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="mdi mdi-truck-outline mdi-48px"></i>
                                                <p class="mt-2">لا توجد موردين</p>
                                                <a href="<?= base_url('purchases/suppliers/create') ?>" class="btn btn-primary btn-sm">
                                                    إضافة مورد جديد
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($suppliers as $supplier): ?>
                                        <tr>
                                            <td><?= $supplier['entity_number'] ?></td>
                                            <td>
                                                <a href="<?= base_url('purchases/suppliers/' . $supplier['entity_number']) ?>" 
                                                   class="text-body fw-bold">
                                                    <?= htmlspecialchars($supplier['G_name_ar']) ?>
                                                </a>
                                                <?php if ($supplier['G_name_en']): ?>
                                                    <br><small class="text-muted"><?= htmlspecialchars($supplier['G_name_en']) ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= htmlspecialchars($supplier['S_company_name'] ?: '-') ?></td>
                                            <td><?= htmlspecialchars($supplier['group_name'] ?: '-') ?></td>
                                            <td><?= htmlspecialchars($supplier['G_phone'] ?: '-') ?></td>
                                            <td>
                                                <?php
                                                $statusClass = [
                                                    'active' => 'success',
                                                    'inactive' => 'secondary',
                                                    'suspended' => 'warning'
                                                ];
                                                $statusText = [
                                                    'active' => 'نشط',
                                                    'inactive' => 'غير نشط',
                                                    'suspended' => 'معلق'
                                                ];
                                                ?>
                                                <span class="badge bg-<?= $statusClass[$supplier['G_status']] ?>">
                                                    <?= $statusText[$supplier['G_status']] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('purchases/suppliers/' . $supplier['entity_number']) ?>" 
                                                       class="btn btn-primary btn-sm" title="عرض">
                                                        <i class="mdi mdi-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('purchases/suppliers/' . $supplier['entity_number'] . '/edit') ?>" 
                                                       class="btn btn-success btn-sm" title="تعديل">
                                                        <i class="mdi mdi-pencil"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-danger btn-sm" 
                                                            onclick="confirmDelete(<?= $supplier['entity_number'] ?>)" title="حذف">
                                                        <i class="mdi mdi-delete"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">تأكيد الحذف</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف هذا المورد؟</p>
                <p class="text-muted">لا يمكن التراجع عن هذا الإجراء.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(supplierNumber) {
    document.getElementById('deleteForm').action = '<?= base_url('purchases/suppliers/') ?>' + supplierNumber + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
