<?php
$title = 'الموردين';
$breadcrumb = [
    ['title' => 'الرئيسية', 'url' => '/'],
    ['title' => 'المشتريات', 'url' => '/purchases'],
    ['title' => 'الموردين', 'url' => '']
];
?>

<div class="container-fluid">
    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <?php foreach ($breadcrumb as $item): ?>
                            <?php if ($item['url']): ?>
                                <li class="breadcrumb-item"><a href="<?= base_url($item['url']) ?>"><?= $item['title'] ?></a></li>
                            <?php else: ?>
                                <li class="breadcrumb-item active"><?= $item['title'] ?></li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </ol>
                </div>
                <h4 class="page-title"><?= $title ?></h4>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row">
        <div class="col-md-6 col-xl-3">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate">إجمالي الموردين</h5>
                            <h3 class="my-2 py-1"><?= $stats['total_suppliers'] ?></h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="mdi mdi-truck text-muted" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-xl-3">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate">الموردين النشطين</h5>
                            <h3 class="my-2 py-1"><?= $stats['active_suppliers'] ?></h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="mdi mdi-check-circle text-success" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-sm-5">
                            <a href="<?= base_url('purchases/suppliers/create') ?>" class="btn btn-danger mb-2">
                                <i class="mdi mdi-plus-circle me-2"></i> إضافة مورد جديد
                            </a>
                        </div>
                        <div class="col-sm-7">
                            <div class="text-sm-end">
                                <div class="d-inline-flex gap-2">
                                    <input type="text" class="form-control" id="quickSearch"
                                           placeholder="البحث السريع..." style="width: 250px;">
                                    <button type="button" class="btn btn-outline-primary" id="filterBtn">
                                        <i class="mdi mdi-filter-variant me-1"></i> فلترة
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" id="clearFiltersBtn" style="display: none;">
                                        <i class="mdi mdi-filter-remove me-1"></i> مسح
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Table -->
                    <div class="table-responsive">
                        <table class="table table-centered table-nowrap table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>الرقم</th>
                                    <th>اسم المورد</th>
                                    <th>اسم الشركة</th>
                                    <th>المجموعة</th>
                                    <th>الهاتف</th>
                                    <th>الحالة</th>
                                    <th style="width: 125px;">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($suppliers)): ?>
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="mdi mdi-truck-outline mdi-48px"></i>
                                                <p class="mt-2">لا توجد موردين</p>
                                                <a href="<?= base_url('purchases/suppliers/create') ?>" class="btn btn-primary btn-sm">
                                                    إضافة مورد جديد
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($suppliers as $supplier): ?>
                                        <tr>
                                            <td><?= $supplier['entity_number'] ?></td>
                                            <td>
                                                <a href="<?= base_url('purchases/suppliers/' . $supplier['entity_number']) ?>" 
                                                   class="text-body fw-bold">
                                                    <?= htmlspecialchars($supplier['G_name_ar']) ?>
                                                </a>
                                                <?php if ($supplier['G_name_en']): ?>
                                                    <br><small class="text-muted"><?= htmlspecialchars($supplier['G_name_en']) ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= htmlspecialchars($supplier['S_company_name'] ?: '-') ?></td>
                                            <td><?= htmlspecialchars($supplier['group_name'] ?: '-') ?></td>
                                            <td><?= htmlspecialchars($supplier['G_phone'] ?: '-') ?></td>
                                            <td>
                                                <?php
                                                $statusClass = [
                                                    'active' => 'success',
                                                    'inactive' => 'secondary',
                                                    'suspended' => 'warning'
                                                ];
                                                $statusText = [
                                                    'active' => 'نشط',
                                                    'inactive' => 'غير نشط',
                                                    'suspended' => 'معلق'
                                                ];
                                                ?>
                                                <span class="badge bg-<?= $statusClass[$supplier['G_status']] ?>">
                                                    <?= $statusText[$supplier['G_status']] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('purchases/suppliers/' . $supplier['entity_number']) ?>" 
                                                       class="btn btn-primary btn-sm" title="عرض">
                                                        <i class="mdi mdi-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('purchases/suppliers/' . $supplier['entity_number'] . '/edit') ?>" 
                                                       class="btn btn-success btn-sm" title="تعديل">
                                                        <i class="mdi mdi-pencil"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-danger btn-sm" 
                                                            onclick="confirmDelete(<?= $supplier['entity_number'] ?>)" title="حذف">
                                                        <i class="mdi mdi-delete"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filter Modal -->
<div class="modal fade" id="filterModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">
                    <i class="mdi mdi-filter-variant me-2"></i>
                    فلترة الموردين
                </h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="filterForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="filter_search" class="form-label">البحث في النص</label>
                                <input type="text" class="form-control" id="filter_search"
                                       placeholder="البحث في اسم المورد أو الشركة...">
                                <div class="form-text">البحث في الاسم العربي والإنجليزي واسم الشركة</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="filter_status" class="form-label">حالة المورد</label>
                                <select class="form-select" id="filter_status">
                                    <option value="">جميع الحالات</option>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                    <option value="suspended">معلق</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="filter_group" class="form-label">المجموعة</label>
                                <select class="form-select" id="filter_group">
                                    <option value="">جميع المجموعات</option>
                                    <?php foreach ($supplierGroups as $group): ?>
                                        <option value="<?= $group['group_number'] ?>">
                                            <?= htmlspecialchars($group['name_ar']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="filter_phone" class="form-label">البحث في الهاتف</label>
                                <input type="text" class="form-control" id="filter_phone"
                                       placeholder="رقم الهاتف...">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="filter_date_from" class="form-label">تاريخ الإنشاء من</label>
                                <input type="date" class="form-control" id="filter_date_from">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="filter_date_to" class="form-label">تاريخ الإنشاء إلى</label>
                                <input type="date" class="form-control" id="filter_date_to">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-secondary" id="resetFiltersBtn">
                    <i class="mdi mdi-refresh me-1"></i> إعادة تعيين
                </button>
                <button type="button" class="btn btn-primary" id="applyFiltersBtn">
                    <i class="mdi mdi-check me-1"></i> تطبيق الفلاتر
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">تأكيد الحذف</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف هذا المورد؟</p>
                <p class="text-muted">لا يمكن التراجع عن هذا الإجراء.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Advanced Filtering System for Suppliers
class SupplierFilter {
    constructor() {
        this.storageKey = 'suppliers_filters';
        this.originalData = <?= json_encode($suppliers) ?>;
        this.filteredData = [...this.originalData];
        this.init();
    }

    init() {
        // Load saved filters
        this.loadFilters();

        // Event listeners
        document.getElementById('filterBtn').addEventListener('click', () => {
            new bootstrap.Modal(document.getElementById('filterModal')).show();
        });

        document.getElementById('clearFiltersBtn').addEventListener('click', () => {
            this.clearFilters();
        });

        document.getElementById('applyFiltersBtn').addEventListener('click', () => {
            this.applyFilters();
        });

        document.getElementById('resetFiltersBtn').addEventListener('click', () => {
            this.resetFilters();
        });

        // Quick search
        document.getElementById('quickSearch').addEventListener('input', (e) => {
            this.quickSearch(e.target.value);
        });

        // Apply initial filters
        this.applyFilters();
    }

    loadFilters() {
        const saved = localStorage.getItem(this.storageKey);
        if (saved) {
            const filters = JSON.parse(saved);
            document.getElementById('filter_search').value = filters.search || '';
            document.getElementById('filter_status').value = filters.status || '';
            document.getElementById('filter_group').value = filters.group || '';
            document.getElementById('filter_phone').value = filters.phone || '';
            document.getElementById('filter_date_from').value = filters.date_from || '';
            document.getElementById('filter_date_to').value = filters.date_to || '';
            document.getElementById('quickSearch').value = filters.quick_search || '';
        }
    }

    saveFilters() {
        const filters = {
            search: document.getElementById('filter_search').value,
            status: document.getElementById('filter_status').value,
            group: document.getElementById('filter_group').value,
            phone: document.getElementById('filter_phone').value,
            date_from: document.getElementById('filter_date_from').value,
            date_to: document.getElementById('filter_date_to').value,
            quick_search: document.getElementById('quickSearch').value
        };
        localStorage.setItem(this.storageKey, JSON.stringify(filters));
    }

    applyFilters() {
        this.saveFilters();

        let filtered = [...this.originalData];

        // Search filter
        const searchTerm = document.getElementById('filter_search').value.toLowerCase();
        if (searchTerm) {
            filtered = filtered.filter(item =>
                item.G_name_ar.toLowerCase().includes(searchTerm) ||
                (item.G_name_en && item.G_name_en.toLowerCase().includes(searchTerm)) ||
                (item.S_company_name && item.S_company_name.toLowerCase().includes(searchTerm))
            );
        }

        // Quick search filter
        const quickSearchTerm = document.getElementById('quickSearch').value.toLowerCase();
        if (quickSearchTerm) {
            filtered = filtered.filter(item =>
                item.G_name_ar.toLowerCase().includes(quickSearchTerm) ||
                (item.G_name_en && item.G_name_en.toLowerCase().includes(quickSearchTerm)) ||
                (item.S_company_name && item.S_company_name.toLowerCase().includes(quickSearchTerm))
            );
        }

        // Status filter
        const statusFilter = document.getElementById('filter_status').value;
        if (statusFilter) {
            filtered = filtered.filter(item => item.G_status === statusFilter);
        }

        // Group filter
        const groupFilter = document.getElementById('filter_group').value;
        if (groupFilter) {
            filtered = filtered.filter(item => item.group_number == groupFilter);
        }

        // Phone filter
        const phoneFilter = document.getElementById('filter_phone').value;
        if (phoneFilter) {
            filtered = filtered.filter(item =>
                item.G_phone && item.G_phone.includes(phoneFilter)
            );
        }

        // Date filters
        const dateFrom = document.getElementById('filter_date_from').value;
        const dateTo = document.getElementById('filter_date_to').value;

        if (dateFrom) {
            filtered = filtered.filter(item =>
                item.created_at >= dateFrom
            );
        }

        if (dateTo) {
            filtered = filtered.filter(item =>
                item.created_at <= dateTo + ' 23:59:59'
            );
        }

        this.filteredData = filtered;
        this.renderTable();
        this.updateFilterButton();

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('filterModal'));
        if (modal) modal.hide();
    }

    quickSearch(term) {
        document.getElementById('filter_search').value = '';
        this.applyFilters();
    }

    clearFilters() {
        localStorage.removeItem(this.storageKey);
        document.getElementById('filter_search').value = '';
        document.getElementById('filter_status').value = '';
        document.getElementById('filter_group').value = '';
        document.getElementById('filter_phone').value = '';
        document.getElementById('filter_date_from').value = '';
        document.getElementById('filter_date_to').value = '';
        document.getElementById('quickSearch').value = '';
        this.applyFilters();
    }

    resetFilters() {
        document.getElementById('filter_search').value = '';
        document.getElementById('filter_status').value = '';
        document.getElementById('filter_group').value = '';
        document.getElementById('filter_phone').value = '';
        document.getElementById('filter_date_from').value = '';
        document.getElementById('filter_date_to').value = '';
    }

    updateFilterButton() {
        const hasFilters = this.hasActiveFilters();
        const clearBtn = document.getElementById('clearFiltersBtn');
        const filterBtn = document.getElementById('filterBtn');

        if (hasFilters) {
            clearBtn.style.display = 'block';
            filterBtn.classList.add('btn-primary');
            filterBtn.classList.remove('btn-outline-primary');
        } else {
            clearBtn.style.display = 'none';
            filterBtn.classList.remove('btn-primary');
            filterBtn.classList.add('btn-outline-primary');
        }
    }

    hasActiveFilters() {
        return document.getElementById('filter_search').value ||
               document.getElementById('filter_status').value ||
               document.getElementById('filter_group').value ||
               document.getElementById('filter_phone').value ||
               document.getElementById('filter_date_from').value ||
               document.getElementById('filter_date_to').value ||
               document.getElementById('quickSearch').value;
    }

    renderTable() {
        const tbody = document.querySelector('table tbody');

        if (this.filteredData.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-4">
                        <div class="text-muted">
                            <i class="mdi mdi-filter-remove-outline mdi-48px"></i>
                            <p class="mt-2">لا توجد نتائج تطابق الفلاتر المحددة</p>
                            <button type="button" class="btn btn-primary btn-sm" onclick="filter.clearFilters()">
                                مسح الفلاتر
                            </button>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        const statusClass = {
            'active': 'success',
            'inactive': 'secondary',
            'suspended': 'warning'
        };

        const statusText = {
            'active': 'نشط',
            'inactive': 'غير نشط',
            'suspended': 'معلق'
        };

        tbody.innerHTML = this.filteredData.map(supplier => `
            <tr>
                <td>${supplier.entity_number}</td>
                <td>
                    <a href="<?= base_url('purchases/suppliers/') ?>${supplier.entity_number}"
                       class="text-body fw-bold">
                        ${supplier.G_name_ar}
                    </a>
                    ${supplier.G_name_en ? `<br><small class="text-muted">${supplier.G_name_en}</small>` : ''}
                </td>
                <td>${supplier.S_company_name || '-'}</td>
                <td>${supplier.group_name || '-'}</td>
                <td>${supplier.G_phone || '-'}</td>
                <td>
                    <span class="badge bg-${statusClass[supplier.G_status]}">
                        ${statusText[supplier.G_status]}
                    </span>
                </td>
                <td>
                    <div class="btn-group" role="group">
                        <a href="<?= base_url('purchases/suppliers/') ?>${supplier.entity_number}"
                           class="btn btn-primary btn-sm" title="عرض">
                            <i class="mdi mdi-eye"></i>
                        </a>
                        <a href="<?= base_url('purchases/suppliers/') ?>${supplier.entity_number}/edit"
                           class="btn btn-success btn-sm" title="تعديل">
                            <i class="mdi mdi-pencil"></i>
                        </a>
                        <button type="button" class="btn btn-danger btn-sm"
                                onclick="confirmDelete(${supplier.entity_number})" title="حذف">
                            <i class="mdi mdi-delete"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }
}

// Initialize filter system
const filter = new SupplierFilter();

// Delete confirmation
function confirmDelete(supplierNumber) {
    document.getElementById('deleteForm').action = '<?= base_url('purchases/suppliers/') ?>' + supplierNumber + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
