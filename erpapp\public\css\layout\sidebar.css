             /* ===== MOBILE SIDEBAR CLOSE - HIDDEN BY DEFAULT ===== */
        .mobile-sidebar-close {
            display: none !important; /* مخفي بشكل افتراضي على جميع الأجهزة */
        }

        body.dark-theme .sidebar {
            background-color: var(--dark-sidebar-bg);
            border-color: var(--dark-border-color);
        }

        body.dark-theme .sidebar-brand {
            color: var(--light-color);
        }

        body.dark-theme .sidebar-menu-link {
            color: #aaa;
        }

        body.dark-theme .sidebar-menu-link:hover,
        body.dark-theme .sidebar-menu-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: #fff;
        }

        body.dark-theme .sidebar-divider {
            background-color: var(--dark-border-color);
        }

          /* Sidebar Styles - Modern & Interactive */
        .sidebar {
            width: var(--sidebar-width);
            background: var(--light-sidebar-bg);
            box-shadow: var(--box-shadow);
            transition: all var(--transition-normal) var(--transition-bezier);
            z-index: 1000;
            height: 100vh;
            position: fixed;
            overflow-y: auto;
            overflow-x: hidden;
            scrollbar-width: thin;
            scrollbar-color: var(--light-scrollbar-thumb) var(--light-scrollbar-track);
        }

        .sidebar::-webkit-scrollbar {
            width: 5px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: var(--light-scrollbar-track);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background-color: var(--light-scrollbar-thumb);
            border-radius: var(--border-radius-full);
        }

        .sidebar.collapsed {
            width: var(--sidebar-collapsed-width);
        }

        body.dark-theme .sidebar {
            background: var(--dark-sidebar-bg);
            scrollbar-color: var(--dark-scrollbar-thumb) var(--dark-scrollbar-track);
        }

        body.dark-theme .sidebar::-webkit-scrollbar-track {
            background: var(--dark-scrollbar-track);
        }

        body.dark-theme .sidebar::-webkit-scrollbar-thumb {
            background-color: var(--dark-scrollbar-thumb);
        }

        .sidebar-header {
            padding: var(--spacing-5) var(--spacing-6);
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid var(--light-border-color);
            height: var(--topbar-height);
            position: sticky;
            top: 0;
            background: inherit;
            z-index: 10;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        body.dark-theme .sidebar-header {
            border-color: var(--dark-border-color);
        }

        .sidebar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary-color);
            text-decoration: none;
            display: flex;
            align-items: center;
            white-space: nowrap;
            overflow: hidden;
            transition: all var(--transition-normal) var(--transition-bezier);
        }

        .sidebar-brand:hover {
            transform: translateY(-1px);
        }

        .sidebar-brand i {
            margin-right: var(--spacing-3);
            font-size: 1.25rem;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 2.25rem;
            height: 2.25rem;
            border-radius: var(--border-radius);
            background: var(--primary-gradient);
            color: white;
            box-shadow: var(--box-shadow-sm);
            transition: all var(--transition-normal) var(--transition-bezier);
        }

        .sidebar-brand:hover i {
            transform: scale(1.05);
            box-shadow: var(--box-shadow);
        }

        .rtl .sidebar-brand i {
            margin-right: 0;
            margin-left: var(--spacing-3);
        }

        .sidebar-toggle {
            background: none;
            border: none;
            color: var(--gray-700);
            font-size: 1.25rem;
            cursor: pointer;
            transition: all var(--transition-normal) var(--transition-bezier);
            width: 2.25rem;
            height: 2.25rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--border-radius);
            position: relative;
            overflow: hidden;
        }

        .sidebar-toggle::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: var(--light-hover-bg);
            border-radius: var(--border-radius);
            transform: scale(0);
            transition: transform var(--transition-fast) var(--transition-bezier);
        }

        .sidebar-toggle:hover::before {
            transform: scale(1);
        }

        .sidebar-toggle i {
            position: relative;
            z-index: 1;
        }

        .sidebar-toggle:hover {
            color: var(--primary-color);
        }

        body.dark-theme .sidebar-toggle {
            color: var(--gray-400);
        }

        body.dark-theme .sidebar-toggle:hover {
            color: var(--primary-light);
        }

        .sidebar-menu {
            padding: var(--spacing-5) 0;
            list-style: none;
            margin: 0;
        }

        .sidebar-menu-item {
            margin-bottom: var(--spacing-1);
            position: relative;
        }

        .sidebar-menu-item:last-child {
            margin-bottom: 0;
        }

        .sidebar-menu-link {
            display: flex;
            align-items: center;
            padding: var(--spacing-3) var(--spacing-6);
            color: var(--gray-700);
            text-decoration: none;
            transition: all var(--transition-normal) var(--transition-bezier);
            border-radius: var(--border-radius);
            margin: 0 var(--spacing-3);
            position: relative;
            overflow: hidden;
        }

        body.dark-theme .sidebar-menu-link {
            color: var(--gray-400);
        }

        .sidebar-menu-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: var(--light-hover-bg);
            transform: translateX(-100%);
            transition: transform var(--transition-normal) var(--transition-bezier);
            z-index: 0;
        }

        body.dark-theme .sidebar-menu-link::before {
            background-color: var(--dark-hover-bg);
        }

        .sidebar-menu-link:hover::before {
            transform: translateX(0);
        }

        .sidebar-menu-link:hover {
            color: var(--primary-color);
        }

        body.dark-theme .sidebar-menu-link:hover {
            color: var(--primary-light);
        }

        .sidebar-menu-link.active {
            color: var(--primary-color);
            font-weight: 500;
        }

        body.dark-theme .sidebar-menu-link.active {
            color: var(--primary-light);
        }

        .sidebar-menu-link.active::after {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 4px;
            background: var(--primary-gradient);
            border-radius: 0 var(--border-radius-full) var(--border-radius-full) 0;
            box-shadow: var(--box-shadow-sm);
        }

        .rtl .sidebar-menu-link.active::after {
            left: auto;
            right: 0;
            border-radius: var(--border-radius-full) 0 0 var(--border-radius-full);
        }

        /* أنماط القائمة المنسدلة - هادئة وأنيقة */
        .sidebar-menu-item .collapse {
            transition: all 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            padding-left: 0;
            padding-right: 0;
            margin-top: 0.375rem;
            margin-bottom: 0.125rem;
            overflow: hidden;
            background: rgba(248, 250, 252, 0.6);
            border-radius: 8px;
            border-left: 2px solid rgba(99, 102, 241, 0.1);
            position: relative;
        }

        /* منع الوميض عند تحديث الصفحة */
        .sidebar-menu-item .collapse:not(.show) {
            height: 0 !important;
            opacity: 0 !important;
            overflow: hidden !important;
            margin-top: 0 !important;
            margin-bottom: 0 !important;
            padding-top: 0 !important;
            padding-bottom: 0 !important;
            transform: translateY(-8px) !important;
        }

        .sidebar-menu-item .collapse.show {
            height: auto !important;
            opacity: 1 !important;
            overflow: visible !important;
            border-left-color: rgba(99, 102, 241, 0.2);
            transform: translateY(0) !important;
        }

        .rtl .sidebar-menu-item .collapse {
            border-left: none;
            border-right: 2px solid rgba(99, 102, 241, 0.1);
        }

        .rtl .sidebar-menu-item .collapse.show {
            border-right-color: rgba(99, 102, 241, 0.2);
        }

        /* القوائم الفرعية - بسيطة وأنيقة */
        .sidebar-menu-item .collapse .sidebar-menu-link {
            padding: 0.625rem 0.875rem 0.625rem 2.75rem;
            font-size: 0.85rem;
            font-weight: 400;
            color: var(--gray-600);
            transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            border-radius: 6px;
            margin: 0.125rem 0.625rem;
            position: relative;
            background: transparent;
            display: flex;
            align-items: center;
            min-height: 2.25rem;
            opacity: 0.9;
        }

        .rtl .sidebar-menu-item .collapse .sidebar-menu-link {
            padding: 0.625rem 2.75rem 0.625rem 0.875rem;
        }

        /* تأثيرات hover هادئة */
        .sidebar-menu-item .collapse .sidebar-menu-link:hover {
            background: rgba(99, 102, 241, 0.06);
            color: var(--primary-color);
            transform: translateX(3px);
            opacity: 1;
        }

        .rtl .sidebar-menu-item .collapse .sidebar-menu-link:hover {
            transform: translateX(-3px);
        }

        /* الحالة النشطة بسيطة */
        .sidebar-menu-item .collapse .sidebar-menu-link.active {
            background: rgba(99, 102, 241, 0.1);
            color: var(--primary-color);
            font-weight: 500;
            opacity: 1;
        }

        .sidebar-menu-item .collapse .sidebar-menu-link.active::before {
            content: '';
            position: absolute;
            left: -0.625rem;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 50%;
            background: var(--primary-color);
            border-radius: 0 2px 2px 0;
            opacity: 0.8;
        }

        .rtl .sidebar-menu-item .collapse .sidebar-menu-link.active::before {
            left: auto;
            right: -0.625rem;
            border-radius: 2px 0 0 2px;
        }

        /* أيقونات القوائم الفرعية - بسيطة */
        .sidebar-menu-item .collapse .sidebar-menu-link .sidebar-menu-icon {
            width: 1.25rem;
            height: 1.25rem;
            font-size: 0.8rem;
            margin-right: 0.625rem;
            opacity: 0.6;
            transition: all 0.2s ease;
            background: transparent;
            border-radius: 4px;
        }

        .rtl .sidebar-menu-item .collapse .sidebar-menu-link .sidebar-menu-icon {
            margin-right: 0;
            margin-left: 0.625rem;
        }

        .sidebar-menu-item .collapse .sidebar-menu-link:hover .sidebar-menu-icon,
        .sidebar-menu-item .collapse .sidebar-menu-link.active .sidebar-menu-icon {
            opacity: 0.9;
        }

        /* إخفاء القوائم الفرعية عند تصغير السايدبار */
        .sidebar.collapsed .sidebar-menu-item .collapse {
            display: none !important;
        }

        /* دعم الثيم الداكن - هادئ */
        body.dark-theme .sidebar-menu-item .collapse {
            background: rgba(30, 41, 59, 0.4);
            border-left-color: rgba(99, 102, 241, 0.15);
        }

        body.dark-theme .sidebar-menu-item .collapse.show {
            border-left-color: rgba(99, 102, 241, 0.25);
        }

        body.dark-theme .sidebar-menu-item .collapse .sidebar-menu-link {
            color: var(--gray-300);
        }

        body.dark-theme .sidebar-menu-item .collapse .sidebar-menu-link:hover {
            background: rgba(99, 102, 241, 0.08);
            color: var(--primary-light);
        }

        body.dark-theme .sidebar-menu-item .collapse .sidebar-menu-link.active {
            background: rgba(99, 102, 241, 0.15);
            color: var(--primary-light);
        }

        /* أيقونة القائمة المنسدلة */
        .sidebar-dropdown-icon {
            font-size: 0.8rem;
            margin-left: auto;
            transition: transform 0.3s ease;
            opacity: 0.7;
        }

        .rtl .sidebar-dropdown-icon {
            margin-left: 0;
            margin-right: auto;
        }

        [aria-expanded="true"] .sidebar-dropdown-icon {
            transform: rotate(180deg);
        }

        .sidebar.collapsed .sidebar-dropdown-icon {
            display: none;
        }

        .sidebar-menu-icon {
            font-size: 1.1rem;
            width: 2.25rem;
            height: 2.25rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--border-radius);
            transition: all var(--transition-normal) var(--transition-bezier);
            position: relative;
            z-index: 1;
        }

        .sidebar-menu-link.active .sidebar-menu-icon {
            background: var(--primary-gradient);
            color: white;
            box-shadow: var(--box-shadow-sm);
        }

        .sidebar-menu-text {
            margin-left: var(--spacing-3);
            white-space: nowrap;
            transition: all var(--transition-normal) var(--transition-bezier);
            position: relative;
            z-index: 1;
        }

        .rtl .sidebar-menu-text {
            margin-left: 0;
            margin-right: var(--spacing-3);
        }

        .sidebar.collapsed .sidebar-menu-text,
        .sidebar.collapsed .sidebar-brand-text {
            opacity: 0;
            transform: translateX(-20px);
            width: 0;
        }

        .rtl .sidebar.collapsed .sidebar-menu-text,
        .rtl .sidebar.collapsed .sidebar-brand-text {
            transform: translateX(20px);
        }

        /* نوافذ منبثقة للسايدبار المصغر */
        .sidebar.collapsed .sidebar-menu-item {
            position: relative;
        }

        .sidebar.collapsed .sidebar-menu-link {
            position: relative;
        }

        .sidebar-divider {
            height: 1px;
            background: linear-gradient(to right, transparent, var(--light-border-color), transparent);
            margin: var(--spacing-4) var(--spacing-6);
            opacity: 0.7;
        }

        body.dark-theme .sidebar-divider {
            background: linear-gradient(to right, transparent, var(--dark-border-color), transparent);
        }

        /* Sidebar Badge */
        .sidebar-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 20px;
            height: 20px;
            padding: 0 6px;
            font-size: 0.75rem;
            font-weight: 600;
            color: white;
            background-color: var(--primary-color);
            border-radius: var(--border-radius-full);
            margin-left: var(--spacing-2);
            position: relative;
            z-index: 1;
        }

        .rtl .sidebar-badge {
            margin-left: 0;
            margin-right: var(--spacing-2);
        }

        .sidebar-badge.badge-secondary {
            background-color: var(--secondary-color);
        }

        .sidebar-badge.badge-success {
            background-color: var(--success-color);
        }

        .sidebar-badge.badge-danger {
            background-color: var(--danger-color);
        }

        .sidebar-badge.badge-warning {
            background-color: var(--warning-color);
        }

        .sidebar-badge.badge-info {
            background-color: var(--info-color);
        }

        .sidebar.collapsed .sidebar-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            margin-left: 0;
        }

        .rtl .sidebar.collapsed .sidebar-badge {
            right: auto;
            left: 8px;
            margin-right: 0;
        }


         /* Responsive Styles */
        @media (max-width: 992px) {
            .sidebar {
                width: 85%; /* زيادة العرض ليأخذ معظم الشاشة */
                max-width: 320px;
                transform: translateX(-100%);
                z-index: 1050;
                position: fixed;
                top: 0;
                left: 0;
                height: 100vh;
                box-shadow: var(--box-shadow-lg);
                transition: all 0.3s var(--transition-bezier);
            }

            .rtl .sidebar {
                left: auto;
                right: 0;
                transform: translateX(100%);
            }

            .sidebar.mobile-show {
                transform: translateX(0);
            }

            .content {
                width: 100%;
                margin-left: 0;
            }

            .rtl .content {
                margin-right: 0;
            }

            .content.expanded {
                width: 100%;
                margin-left: 0;
            }

            .rtl .content.expanded {
                margin-right: 0;
            }

            /* إظهار النص في السايدبار على الأجهزة المحمولة */
            .sidebar-menu-text, .sidebar-brand-text {
                display: block;
                opacity: 1;
                width: auto;
                transform: translateX(0);
            }

            /* تحسين مظهر السايدبار على الأجهزة المحمولة */
            .sidebar-backdrop {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 1040;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s var(--transition-bezier);
                backdrop-filter: blur(3px);
                -webkit-backdrop-filter: blur(3px);
            }

            .sidebar-backdrop.show {
                opacity: 1;
                visibility: visible;
            }

            /* زر إغلاق السايدبار على الأجهزة المحمولة */
            .mobile-sidebar-close {
                display: flex !important; /* يظهر فقط داخل media query للأجهزة الصغيرة */
                align-items: center;
                justify-content: center;
                width: 40px;
                height: 40px;
                border-radius: var(--border-radius);
                background-color: transparent;
                color: var(--gray-700);
                font-size: 1.25rem;
                cursor: pointer;
                transition: all 0.3s var(--transition-bezier);
                border: none;
                padding: 0;
                margin-left: auto;
                position: relative;
                overflow: hidden;
            }

            .rtl .mobile-sidebar-close {
                margin-left: 0;
                margin-right: auto;
            }

            .mobile-sidebar-close::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: var(--light-hover-bg);
                border-radius: var(--border-radius);
                transform: scale(0);
                transition: transform 0.2s var(--transition-bezier);
            }

            .mobile-sidebar-close:hover::before {
                transform: scale(1);
            }

            .mobile-sidebar-close i {
                position: relative;
                z-index: 1;
            }

            body.dark-theme .mobile-sidebar-close {
                color: var(--gray-400);
            }

            body.dark-theme .mobile-sidebar-close:hover {
                background-color: var(--dark-hover-bg);
                color: var(--primary-light);
            }
        }

        /* ===== ENSURE MOBILE CLOSE IS HIDDEN ON LARGE SCREENS ===== */
        @media (min-width: 993px) {
            .mobile-sidebar-close {
                display: none !important; /* مخفي تماماً على الأجهزة الكبيرة */
            }
        }