<?php
/**
 * اختبار الجدول مع Font Awesome Icons
 */

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>الجدول مع أيقونات Font Awesome الجميلة</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>";
echo "<link href='public/css/modules/purchases.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";

echo "<div class='container-fluid mt-4'>";
echo "<h2><i class='fas fa-table text-primary'></i> الجدول مع أيقونات Font Awesome الجميلة!</h2>";

echo "<div class='alert alert-success mb-4'>";
echo "<h5><i class='fas fa-magic'></i> الأيقونات الذكية الجديدة:</h5>";
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<ul>";
echo "<li><strong><i class='fas fa-sort'></i> أيقونة الفرز العادية:</strong> fa-sort</li>";
echo "<li><strong><i class='fas fa-sort-up text-primary'></i> الفرز التصاعدي:</strong> fa-sort-up</li>";
echo "<li><strong><i class='fas fa-sort-down text-primary'></i> الفرز التنازلي:</strong> fa-sort-down</li>";
echo "</ul>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<ul>";
echo "<li><strong>🎯 تظهر عند التمرير</strong> - على العمود</li>";
echo "<li><strong>👻 تختفي عند الإزاحة</strong> - تلقائياً</li>";
echo "<li><strong>📌 تثبت عند الفرز</strong> - النشط</li>";
echo "<li><strong>🎨 ألوان متدرجة</strong> - رمادي → أزرق</li>";
echo "</ul>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='card'>";
echo "<div class='card-body p-0'>";
echo "<div class='table-responsive'>";
echo "<table class='table table-hover mb-0'>";
echo "<thead>";
echo "<tr>";
echo "<th data-sortable='true' data-column='0'>الإجراء</th>";
echo "<th data-sortable='true' data-column='1'>الحالة</th>";
echo "<th data-sortable='true' data-column='2'>المبلغ</th>";
echo "<th data-sortable='true' data-column='3'>التاريخ</th>";
echo "<th data-sortable='true' data-column='4'>الطلب</th>";
echo "<th data-sortable='true' data-column='5'>العميل</th>";
echo "<th data-sortable='true' data-column='6'>الفاتورة</th>";
echo "</tr>";
echo "</thead>";
echo "<tbody>";

// بيانات تجريبية
$data = [
    ['⋯ ⬇', 'Due', '1,210.00 ر.س', 'February, 2024 08', '#4844326', 'أحمد محمد', 'INV-#4844326'],
    ['⋯ ⬇', 'Refunded', '1,210.00 ر.س', 'February, 2024 24', '#4844342', 'سارة أحمد', 'INV-#4844342'],
    ['⋯ ⬇', 'Cancelled', '1,210.00 ر.س', 'February, 2024 15', '#4844333', 'محمد علي', 'INV-#4844333'],
    ['⋯ ⬇', 'Paid', '2,210.00 ر.س', 'February, 2024 16', '#4144334', 'فاطمة خالد', 'INV-#4144334'],
    ['⋯ ⬇', 'Paid', '1,740.00 ر.س', 'February, 2024 10', '#3544328', 'عبدالله سعد', 'INV-#3544328'],
    ['⋯ ⬇', 'Paid', '1,210.00 ر.س', 'February, 2024 12', '#3744330', 'نورا حسن', 'INV-#3744330'],
    ['⋯ ⬇', 'Paid', '1,210.00 ر.س', 'February, 2024 07', '#6944324', 'خالد عمر', 'INV-#6944324'],
    ['⋯ ⬇', 'Due', '1,210.00 ر.س', 'February, 2024 22', '#4744340', 'ليلى محمود', 'INV-#4744340'],
];

foreach ($data as $row) {
    echo "<tr>";
    echo "<td>{$row[0]}</td>";
    
    // الحالة مع الألوان
    $statusClass = '';
    switch ($row[1]) {
        case 'Paid':
            $statusClass = 'bg-success';
            break;
        case 'Due':
            $statusClass = 'bg-warning';
            break;
        case 'Cancelled':
            $statusClass = 'bg-danger';
            break;
        case 'Refunded':
            $statusClass = 'bg-info';
            break;
    }
    echo "<td><span class='badge {$statusClass}'>{$row[1]}</span></td>";
    
    echo "<td class='text-end'>{$row[2]}</td>";
    echo "<td class='text-muted'>{$row[3]}</td>";
    echo "<td><a href='#'>{$row[4]}</a></td>";
    echo "<td>{$row[5]}</td>";
    echo "<td><a href='#'>{$row[6]}</a></td>";
    echo "</tr>";
}

echo "</tbody>";
echo "</table>";
echo "</div>";
echo "</div>";
echo "</div>";

// Pagination
echo "<div class='d-flex justify-content-between align-items-center mt-3'>";
echo "<div class='datatable-info'>";
echo "عرض <strong>1</strong> إلى <strong>8</strong> من <strong>23</strong> عنصر";
echo "</div>";
echo "<nav>";
echo "<ul class='pagination'>";
echo "<li class='page-item disabled'>";
echo "<a class='page-link' href='#'><i class='fas fa-chevron-right'></i></a>";
echo "</li>";
echo "<li class='page-item'><a class='page-link' href='#'>3</a></li>";
echo "<li class='page-item'><a class='page-link' href='#'>2</a></li>";
echo "<li class='page-item active'><a class='page-link' href='#'>1</a></li>";
echo "<li class='page-item'><a class='page-link' href='#'><i class='fas fa-chevron-left'></i></a></li>";
echo "</ul>";
echo "</nav>";
echo "<div class='text-muted small'>";
echo "عرض جميع العناصر من 23-8";
echo "</div>";
echo "</div>";

echo "<div class='alert alert-info mt-4'>";
echo "<h5><i class='fas fa-lightbulb'></i> كيفية اختبار الأيقونات الذكية:</h5>";
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h6><i class='fas fa-mouse-pointer'></i> التفاعل:</h6>";
echo "<ol>";
echo "<li><strong>مرر المؤشر</strong> على أي عمود في الجدول</li>";
echo "<li><strong>ستظهر الأيقونة</strong> <i class='fas fa-sort'></i> على اليمين</li>";
echo "<li><strong>أزح المؤشر</strong> - ستختفي الأيقونة</li>";
echo "<li><strong>اضغط للفرز</strong> - ستثبت الأيقونة</li>";
echo "</ol>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<h6><i class='fas fa-palette'></i> الألوان:</h6>";
echo "<ul>";
echo "<li><i class='fas fa-sort text-muted'></i> <strong>رمادي</strong> - عند التمرير</li>";
echo "<li><i class='fas fa-sort-up text-primary'></i> <strong>أزرق</strong> - فرز تصاعدي</li>";
echo "<li><i class='fas fa-sort-down text-primary'></i> <strong>أزرق</strong> - فرز تنازلي</li>";
echo "<li><i class='fas fa-eye-slash'></i> <strong>مخفي</strong> - الحالة العادية</li>";
echo "</ul>";
echo "</div>";
echo "</div>";
echo "<div class='alert alert-warning'>";
echo "<i class='fas fa-hand-point-up'></i> <strong>جرب الآن:</strong> مرر المؤشر على أعمدة الجدول أعلاه لترى الأيقونات تظهر وتختفي!";
echo "</div>";
echo "</div>";

echo "<div class='text-center mt-4'>";
echo "<a href='purchases/suppliers' class='btn btn-primary me-2'>";
echo "<i class='fas fa-users'></i> اختبار مع الموردين";
echo "</a>";
echo "<a href='purchases/supplier-groups' class='btn btn-success'>";
echo "<i class='fas fa-layer-group'></i> اختبار مع المجموعات";
echo "</a>";
echo "</div>";

echo "</div>";

echo "<script>";
echo "// إضافة أيقونات Font Awesome للفرز";
echo "document.querySelectorAll('th[data-sortable=\"true\"]').forEach((th, index) => {";
echo "    th.style.cursor = 'pointer';";
echo "    th.style.position = 'relative';";
echo "    th.style.paddingRight = '40px';";
echo "    th.innerHTML += ' <span class=\"sort-icon\"><i class=\"fas fa-sort text-muted\"></i></span>';";
echo "});";

echo "// محاكاة الفرز للاختبار";
echo "document.querySelectorAll('th[data-sortable=\"true\"]').forEach(th => {";
echo "    th.addEventListener('click', function() {";
echo "        // إزالة الفرز من جميع الأعمدة";
echo "        document.querySelectorAll('th[data-sortable=\"true\"]').forEach(header => {";
echo "            header.classList.remove('sorted-asc', 'sorted-desc');";
echo "            const icon = header.querySelector('.sort-icon i');";
echo "            if (icon) icon.className = 'fas fa-sort text-muted';";
echo "        });";
echo "        ";
echo "        // إضافة الفرز للعمود الحالي";
echo "        const icon = this.querySelector('.sort-icon i');";
echo "        if (icon.classList.contains('fa-sort')) {";
echo "            icon.className = 'fas fa-sort-up text-primary';";
echo "            this.classList.add('sorted-asc');";
echo "        } else if (icon.classList.contains('fa-sort-up')) {";
echo "            icon.className = 'fas fa-sort-down text-primary';";
echo "            this.classList.remove('sorted-asc');";
echo "            this.classList.add('sorted-desc');";
echo "        } else {";
echo "            icon.className = 'fas fa-sort text-muted';";
echo "            this.classList.remove('sorted-desc');";
echo "        }";
echo "    });";
echo "});";
echo "</script>";

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>";
echo "</body>";
echo "</html>";
?>
