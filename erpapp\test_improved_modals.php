<?php
/**
 * اختبار النوافذ المحسنة
 */

// تحديد APP_URL
define('APP_URL', 'http://localhost/erpapp');

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار النوافذ المحسنة</title>";

// CSS Files
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>";
echo "<link href='" . rtrim(APP_URL, '/') . "/public/css/core/variables.css' rel='stylesheet'>";
echo "<link href='" . rtrim(APP_URL, '/') . "/public/css/core/base.css' rel='stylesheet'>";
echo "<link href='" . rtrim(APP_URL, '/') . "/public/css/components/buttons.css' rel='stylesheet'>";
echo "<link href='" . rtrim(APP_URL, '/') . "/public/css/components/forms.css' rel='stylesheet'>";
echo "<link href='" . rtrim(APP_URL, '/') . "/public/css/components/modals.css' rel='stylesheet'>";

echo "</head>";
echo "<body class='dark-theme'>";

echo "<div class='container-fluid mt-4'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-magic text-primary'></i> النوافذ المحسنة الجديدة</h1>";

echo "<div class='alert alert-success mb-4'>";
echo "<h5><i class='fas fa-check-circle'></i> التحسينات الجديدة المطبقة:</h5>";
echo "<ul class='mb-0'>";
echo "<li>🎨 <strong>تصميم حديث ومتطور</strong> - header بـ gradient جميل</li>";
echo "<li>🔄 <strong>animations محسنة</strong> - تأثيرات ناعمة وسريعة</li>";
echo "<li>🎯 <strong>زر إغلاق محسن</strong> - يعمل بشكل مثالي دائماً</li>";
echo "<li>🌟 <strong>backdrop محسن</strong> - blur أقوى وأجمل</li>";
echo "<li>📱 <strong>responsive مثالي</strong> - يعمل على جميع الأجهزة</li>";
echo "<li>🎭 <strong>ثيم داكن محسن</strong> - ألوان متناسقة</li>";
echo "<li>⚡ <strong>أداء أسرع</strong> - تحميل وإغلاق سريع</li>";
echo "<li>🔧 <strong>إصلاح مشاكل الإغلاق</strong> - يعمل من أول مرة</li>";
echo "</ul>";
echo "</div>";

// أزرار الاختبار
echo "<div class='row mb-4'>";

echo "<div class='col-md-3 mb-3'>";
echo "<button class='btn btn-primary w-100' data-toggle='modal' data-target='#filterModal'>";
echo "<i class='fas fa-filter'></i> نافذة فلاتر";
echo "</button>";
echo "</div>";

echo "<div class='col-md-3 mb-3'>";
echo "<button class='btn btn-success w-100' onclick='testConfirmModal()'>";
echo "<i class='fas fa-question-circle'></i> نافذة تأكيد";
echo "</button>";
echo "</div>";

echo "<div class='col-md-3 mb-3'>";
echo "<button class='btn btn-warning w-100' onclick='testLoadingModal()'>";
echo "<i class='fas fa-spinner'></i> نافذة تحميل";
echo "</button>";
echo "</div>";

echo "<div class='col-md-3 mb-3'>";
echo "<button class='btn btn-info w-100' data-toggle='modal' data-target='#largeModal'>";
echo "<i class='fas fa-expand'></i> نافذة كبيرة";
echo "</button>";
echo "</div>";

echo "</div>";

// نافذة فلاتر
echo "<div class='modal filter-modal' id='filterModal' tabindex='-1' style='display: none;'>";
echo "<div class='modal-dialog'>";
echo "<div class='modal-content'>";
echo "<div class='modal-header'>";
echo "<h5 class='modal-title'><i class='fas fa-filter'></i> فلاتر البحث المتقدمة</h5>";
echo "<button type='button' class='modal-close' data-dismiss='modal'>";
echo "<i class='fas fa-times'></i>";
echo "</button>";
echo "</div>";
echo "<div class='modal-body'>";
echo "<div class='form-row'>";
echo "<div class='form-group'>";
echo "<label class='form-label'>البحث في النص</label>";
echo "<input type='text' class='form-control' placeholder='ابحث عن أي شيء...'>";
echo "</div>";
echo "<div class='form-group'>";
echo "<label class='form-label'>الحالة</label>";
echo "<select class='form-select'>";
echo "<option>جميع الحالات</option>";
echo "<option>نشط</option>";
echo "<option>غير نشط</option>";
echo "<option>معلق</option>";
echo "</select>";
echo "</div>";
echo "</div>";
echo "<div class='form-row'>";
echo "<div class='form-group'>";
echo "<label class='form-label'>التاريخ من</label>";
echo "<input type='date' class='form-control'>";
echo "</div>";
echo "<div class='form-group'>";
echo "<label class='form-label'>التاريخ إلى</label>";
echo "<input type='date' class='form-control'>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "<div class='modal-footer'>";
echo "<button type='button' class='btn btn-secondary' data-dismiss='modal'>إلغاء</button>";
echo "<button type='button' class='btn btn-primary'>تطبيق الفلاتر</button>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

// نافذة كبيرة
echo "<div class='modal modal-lg' id='largeModal' tabindex='-1' style='display: none;'>";
echo "<div class='modal-dialog'>";
echo "<div class='modal-content'>";
echo "<div class='modal-header'>";
echo "<h5 class='modal-title'><i class='fas fa-expand'></i> نافذة كبيرة للمحتوى المفصل</h5>";
echo "<button type='button' class='modal-close' data-dismiss='modal'>";
echo "<i class='fas fa-times'></i>";
echo "</button>";
echo "</div>";
echo "<div class='modal-body'>";
echo "<h6>محتوى مفصل</h6>";
echo "<p>هذه نافذة كبيرة تحتوي على محتوى مفصل ومعلومات شاملة. يمكن استخدامها لعرض التفاصيل الكاملة أو النماذج المعقدة.</p>";

echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<div class='form-group'>";
echo "<label class='form-label'>الاسم الكامل</label>";
echo "<input type='text' class='form-control' placeholder='أدخل الاسم الكامل'>";
echo "</div>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<div class='form-group'>";
echo "<label class='form-label'>البريد الإلكتروني</label>";
echo "<input type='email' class='form-control' placeholder='أدخل البريد الإلكتروني'>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='form-group'>";
echo "<label class='form-label'>الوصف</label>";
echo "<textarea class='form-control' rows='4' placeholder='أدخل وصف مفصل...'></textarea>";
echo "</div>";

echo "</div>";
echo "<div class='modal-footer'>";
echo "<button type='button' class='btn btn-secondary' data-dismiss='modal'>إلغاء</button>";
echo "<button type='button' class='btn btn-success'>حفظ البيانات</button>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='alert alert-info'>";
echo "<h5><i class='fas fa-info-circle'></i> تعليمات الاختبار المحسنة:</h5>";
echo "<ul class='mb-0'>";
echo "<li>🖱️ <strong>انقر خارج النافذة</strong> - يجب أن تغلق فوراً</li>";
echo "<li>⌨️ <strong>اضغط Escape</strong> - إغلاق سريع</li>";
echo "<li>❌ <strong>انقر على زر X</strong> - يعمل بشكل مثالي الآن</li>";
echo "<li>👀 <strong>لاحظ التصميم الجديد</strong> - header ملون وجميل</li>";
echo "<li>✨ <strong>شاهد الـ animations</strong> - ناعمة وسريعة</li>";
echo "<li>🔍 <strong>اختبر جميع الطرق</strong> - كلها تعمل بشكل مثالي</li>";
echo "<li>📱 <strong>جرب على الموبايل</strong> - responsive محسن</li>";
echo "</ul>";
echo "</div>";

echo "</div>";

// JavaScript
echo "<script src='" . rtrim(APP_URL, '/') . "/public/js/components/dropdown.js'></script>";
echo "<script src='" . rtrim(APP_URL, '/') . "/public/js/components/modals.js'></script>";

echo "<script>";
echo "function testConfirmModal() {";
echo "    confirmModal({";
echo "        title: 'تأكيد العملية',";
echo "        message: 'هل أنت متأكد من تنفيذ هذه العملية؟ لا يمكن التراجع عنها.',";
echo "        icon: 'warning',";
echo "        confirmText: 'نعم، تنفيذ',";
echo "        cancelText: 'إلغاء',";
echo "        confirmClass: 'btn-danger',";
echo "        onConfirm: function() {";
echo "            alert('تم تنفيذ العملية بنجاح! 🎉');";
echo "        }";
echo "    });";
echo "}";

echo "function testLoadingModal() {";
echo "    const loading = loadingModal('جاري معالجة البيانات...');";
echo "    setTimeout(() => {";
echo "        closeAllModals();";
echo "        alert('تمت المعالجة بنجاح! ✅');";
echo "    }, 3000);";
echo "}";

echo "// تسجيل الأحداث";
echo "document.addEventListener('modal:opened', function(e) {";
echo "    console.log('🎉 تم فتح النافذة:', e.detail.modal.id);";
echo "});";

echo "document.addEventListener('modal:closed', function(e) {";
echo "    console.log('✅ تم إغلاق النافذة:', e.detail.modal.id);";
echo "});";

echo "console.log('🚀 صفحة اختبار النوافذ المحسنة جاهزة');";
echo "</script>";

echo "</body>";
echo "</html>";
?>
