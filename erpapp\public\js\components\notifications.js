/**
 * مكون الإشعارات - إدارة وعرض الإشعارات
 * Notifications Component - Notification management and display
 */

(function() {
    'use strict';

    // انتظار تحميل DOM
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initNotifications);
    } else {
        initNotifications();
    }

    function initNotifications() {
        console.log('🚀 بدء تهيئة مكون الإشعارات...');

        initToastrNotifications();
        initBrowserNotifications();
        initRealTimeNotifications();
        initNotificationActions();

        console.log('✅ تم تهيئة مكون الإشعارات بنجاح');
    }

    // ===== إشعارات Toastr ===== //
    function initToastrNotifications() {
        if (typeof toastr === 'undefined') {
            console.warn('Toastr library not loaded');
            return;
        }

        // إعدادات Toastr المحسنة
        toastr.options = {
            closeButton: true,
            debug: false,
            newestOnTop: true,
            progressBar: true,
            positionClass: window.APP_CONFIG?.IS_RTL ? 'toast-top-left' : 'toast-top-right',
            preventDuplicates: true,
            onclick: null,
            showDuration: '300',
            hideDuration: '1000',
            timeOut: '5000',
            extendedTimeOut: '1000',
            showEasing: 'swing',
            hideEasing: 'linear',
            showMethod: 'fadeIn',
            hideMethod: 'fadeOut',
            rtl: window.APP_CONFIG?.IS_RTL || false
        };

        // تخصيص أنماط الإشعارات
        toastr.options.iconClasses = {
            error: 'toast-error',
            info: 'toast-info',
            success: 'toast-success',
            warning: 'toast-warning'
        };
    }

    // ===== إشعارات المتصفح ===== //
    function initBrowserNotifications() {
        // طلب إذن الإشعارات
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission().then(function(permission) {
                if (permission === 'granted') {
                    console.log('تم منح إذن الإشعارات');
                }
            });
        }
    }

    // ===== الإشعارات الفورية ===== //
    function initRealTimeNotifications() {
        // تم تعطيل الإشعارات الفورية مؤقتاً حتى يتم إنشاء API endpoints
        // محاكاة الإشعارات الفورية (يمكن استبدالها بـ WebSocket أو Server-Sent Events)
        // if (window.APP_CONFIG?.USER_ID) {
        //     setInterval(checkForNewNotifications, 30000); // كل 30 ثانية
        // }
        console.log('📡 نظام الإشعارات الفورية معطل مؤقتاً - سيتم تفعيله عند إنشاء API endpoints');
    }

    // ===== إجراءات الإشعارات ===== //
    function initNotificationActions() {
        // زر الإشعارات في الشريط العلوي
        const notificationBtn = document.getElementById('notifications-btn');
        if (notificationBtn) {
            notificationBtn.addEventListener('click', function() {
                toggleNotificationPanel();
            });
        }

        // إجراءات الإشعارات الفردية
        document.addEventListener('click', function(e) {
            if (e.target.matches('[data-notification-action]')) {
                const action = e.target.dataset.notificationAction;
                const notificationId = e.target.dataset.notificationId;
                handleNotificationAction(action, notificationId, e.target);
            }
        });
    }

    // ===== وظائف الإشعارات ===== //

    // إظهار إشعار نجاح
    window.showSuccess = function(message, title = 'نجح', options = {}) {
        if (typeof toastr !== 'undefined') {
            const config = Object.assign({
                timeOut: 4000,
                iconClass: 'toast-success'
            }, options);
            
            return toastr.success(message, title, config);
        } else {
            console.log('Success:', title, message);
        }
    };

    // إظهار إشعار خطأ
    window.showError = function(message, title = 'خطأ', options = {}) {
        if (typeof toastr !== 'undefined') {
            const config = Object.assign({
                timeOut: 6000,
                iconClass: 'toast-error'
            }, options);
            
            return toastr.error(message, title, config);
        } else {
            console.error('Error:', title, message);
        }
    };

    // إظهار إشعار تحذير
    window.showWarning = function(message, title = 'تحذير', options = {}) {
        if (typeof toastr !== 'undefined') {
            const config = Object.assign({
                timeOut: 5000,
                iconClass: 'toast-warning'
            }, options);
            
            return toastr.warning(message, title, config);
        } else {
            console.warn('Warning:', title, message);
        }
    };

    // إظهار إشعار معلومات
    window.showInfo = function(message, title = 'معلومات', options = {}) {
        if (typeof toastr !== 'undefined') {
            const config = Object.assign({
                timeOut: 4000,
                iconClass: 'toast-info'
            }, options);
            
            return toastr.info(message, title, config);
        } else {
            console.info('Info:', title, message);
        }
    };

    // إظهار إشعار مخصص
    window.showCustomNotification = function(type, message, title, options = {}) {
        const types = {
            success: showSuccess,
            error: showError,
            warning: showWarning,
            info: showInfo
        };

        if (types[type]) {
            return types[type](message, title, options);
        } else {
            console.warn('Unknown notification type:', type);
        }
    };

    // إشعار المتصفح
    window.showBrowserNotification = function(title, options = {}) {
        if ('Notification' in window && Notification.permission === 'granted') {
            const config = Object.assign({
                icon: '/public/images/logo.png',
                badge: '/public/images/badge.png',
                dir: window.APP_CONFIG?.IS_RTL ? 'rtl' : 'ltr',
                lang: window.APP_CONFIG?.LANG || 'ar'
            }, options);

            const notification = new Notification(title, config);
            
            // إغلاق تلقائي بعد 5 ثوان
            setTimeout(() => {
                notification.close();
            }, 5000);

            return notification;
        }
    };

    // إشعار مع إجراء
    window.showActionNotification = function(message, actions = [], options = {}) {
        const config = Object.assign({
            timeOut: 0, // لا ينتهي تلقائياً
            extendedTimeOut: 0,
            closeButton: true,
            tapToDismiss: false
        }, options);

        let actionButtons = '';
        actions.forEach((action, index) => {
            actionButtons += `
                <button type="button" class="btn btn-sm ${action.class || 'btn-primary'} me-2" 
                        onclick="${action.callback || ''}" 
                        data-dismiss="toast">
                    ${action.text}
                </button>
            `;
        });

        const messageWithActions = `
            <div>${message}</div>
            <div class="mt-2">${actionButtons}</div>
        `;

        return toastr.info(messageWithActions, options.title || 'إجراء مطلوب', config);
    };

    // ===== وظائف مساعدة ===== //

    function checkForNewNotifications() {
        // تم تعطيل فحص الإشعارات مؤقتاً حتى يتم إنشاء API endpoints
        console.log('📡 فحص الإشعارات معطل مؤقتاً - يحتاج إلى API endpoints');

        // محاكاة فحص الإشعارات الجديدة
        // if (window.sendAjaxRequest) {
        //     sendAjaxRequest('/api/notifications/check')
        //         .then(data => {
        //             if (data && data.notifications) {
        //                 data.notifications.forEach(notification => {
        //                     displayNotification(notification);
        //                 });
        //
        //                 updateNotificationBadge(data.total_count);
        //             }
        //         })
        //         .catch(error => {
        //             console.error('Error checking notifications:', error);
        //         });
        // }
    }

    function displayNotification(notification) {
        const type = notification.type || 'info';
        const message = notification.message || '';
        const title = notification.title || '';

        // إظهار إشعار Toastr
        showCustomNotification(type, message, title);

        // إظهار إشعار المتصفح للإشعارات المهمة
        if (notification.important && document.hidden) {
            showBrowserNotification(title, {
                body: message,
                tag: notification.id
            });
        }
    }

    function updateNotificationBadge(count) {
        const badge = document.querySelector('.topbar-action-badge');
        if (badge) {
            if (count > 0) {
                badge.textContent = count > 99 ? '99+' : count;
                badge.style.display = 'flex';
            } else {
                badge.style.display = 'none';
            }
        }
    }

    function toggleNotificationPanel() {
        // تبديل لوحة الإشعارات
        let panel = document.getElementById('notification-panel');
        
        if (!panel) {
            panel = createNotificationPanel();
            document.body.appendChild(panel);
        }

        panel.classList.toggle('show');
        
        if (panel.classList.contains('show')) {
            loadNotifications();
        }
    }

    function createNotificationPanel() {
        const panel = document.createElement('div');
        panel.id = 'notification-panel';
        panel.className = 'notification-panel';
        panel.innerHTML = `
            <div class="notification-header">
                <h5>الإشعارات</h5>
                <button type="button" class="btn-close" onclick="closeNotificationPanel()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="notification-body">
                <div class="notification-loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    جاري التحميل...
                </div>
            </div>
            <div class="notification-footer">
                <a href="/notifications" class="btn btn-sm btn-primary">
                    عرض جميع الإشعارات
                </a>
            </div>
        `;
        
        return panel;
    }

    function loadNotifications() {
        const panel = document.getElementById('notification-panel');
        const body = panel.querySelector('.notification-body');

        // تم تعطيل تحميل الإشعارات مؤقتاً حتى يتم إنشاء API endpoints
        body.innerHTML = '<div class="text-center text-muted">نظام الإشعارات قيد التطوير</div>';
        console.log('📡 تحميل الإشعارات معطل مؤقتاً - يحتاج إلى API endpoints');

        // if (window.sendAjaxRequest) {
        //     sendAjaxRequest('/api/notifications/recent')
        //         .then(data => {
        //             if (data && data.notifications) {
        //                 displayNotificationList(data.notifications, body);
        //             }
        //         })
        //         .catch(error => {
        //             body.innerHTML = '<div class="text-center text-muted">حدث خطأ في تحميل الإشعارات</div>';
        //         });
        // }
    }

    function displayNotificationList(notifications, container) {
        if (notifications.length === 0) {
            container.innerHTML = '<div class="text-center text-muted">لا توجد إشعارات جديدة</div>';
            return;
        }

        const notificationHTML = notifications.map(notification => `
            <div class="notification-item ${notification.read ? 'read' : 'unread'}" 
                 data-notification-id="${notification.id}">
                <div class="notification-icon ${notification.type}">
                    <i class="fas fa-${getNotificationIcon(notification.type)}"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-title">${notification.title}</div>
                    <div class="notification-message">${notification.message}</div>
                    <div class="notification-time">${formatNotificationTime(notification.created_at)}</div>
                </div>
                <div class="notification-actions">
                    ${!notification.read ? `
                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                data-notification-action="mark-read" 
                                data-notification-id="${notification.id}">
                            تم القراءة
                        </button>
                    ` : ''}
                </div>
            </div>
        `).join('');

        container.innerHTML = notificationHTML;
    }

    function getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle',
            message: 'envelope',
            system: 'cog'
        };
        
        return icons[type] || 'bell';
    }

    function formatNotificationTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;
        
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);
        
        if (minutes < 1) return 'الآن';
        if (minutes < 60) return `منذ ${minutes} دقيقة`;
        if (hours < 24) return `منذ ${hours} ساعة`;
        if (days < 7) return `منذ ${days} يوم`;
        
        return date.toLocaleDateString('ar-SA');
    }

    function handleNotificationAction(action, notificationId, element) {
        switch (action) {
            case 'mark-read':
                markNotificationAsRead(notificationId, element);
                break;
            case 'delete':
                deleteNotification(notificationId, element);
                break;
            default:
                console.warn('Unknown notification action:', action);
        }
    }

    function markNotificationAsRead(notificationId, element) {
        // تم تعطيل إجراءات الإشعارات مؤقتاً حتى يتم إنشاء API endpoints
        console.log('📡 إجراءات الإشعارات معطلة مؤقتاً - تحتاج إلى API endpoints');
        showInfo('نظام الإشعارات قيد التطوير');

        // if (window.sendAjaxRequest) {
        //     sendAjaxRequest(`/api/notifications/${notificationId}/read`, {
        //         method: 'POST'
        //     }).then(data => {
        //         if (data.success) {
        //             const notificationItem = element.closest('.notification-item');
        //             notificationItem.classList.remove('unread');
        //             notificationItem.classList.add('read');
        //             element.remove();
        //
        //             // تحديث العداد
        //             updateNotificationBadge(data.unread_count);
        //         }
        //     });
        // }
    }

    function deleteNotification(notificationId, element) {
        // تم تعطيل إجراءات الإشعارات مؤقتاً حتى يتم إنشاء API endpoints
        console.log('📡 إجراءات الإشعارات معطلة مؤقتاً - تحتاج إلى API endpoints');
        showInfo('نظام الإشعارات قيد التطوير');

        // if (confirm('هل أنت متأكد من حذف هذا الإشعار؟')) {
        //     if (window.sendAjaxRequest) {
        //         sendAjaxRequest(`/api/notifications/${notificationId}`, {
        //             method: 'DELETE'
        //         }).then(data => {
        //             if (data.success) {
        //                 const notificationItem = element.closest('.notification-item');
        //                 notificationItem.remove();
        //                 showSuccess('تم حذف الإشعار');
        //             }
        //         });
        //     }
        // }
    }

    // إغلاق لوحة الإشعارات
    window.closeNotificationPanel = function() {
        const panel = document.getElementById('notification-panel');
        if (panel) {
            panel.classList.remove('show');
        }
    };

    // إغلاق اللوحة عند النقر خارجها
    document.addEventListener('click', function(e) {
        const panel = document.getElementById('notification-panel');
        const notificationBtn = document.getElementById('notifications-btn');
        
        if (panel && panel.classList.contains('show') && 
            !panel.contains(e.target) && 
            !notificationBtn.contains(e.target)) {
            closeNotificationPanel();
        }
    });

})();
