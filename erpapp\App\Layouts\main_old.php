<!DOCTYPE html>
<html lang="<?= current_lang() ?>" dir="<?= current_lang() == 'ar' ? 'rtl' : 'ltr' ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? APP_NAME ?> - <?= APP_NAME ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&family=Poppins:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Bootstrap CSS -->
    <?php if (current_lang() == 'ar'): ?>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <?php else: ?>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <?php endif; ?>

    <!-- jQuery (required for Toastr) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

    <!-- Toastr CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/css/toastr.min.css">

    <!-- Toastr JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>

    <!-- Dynamic CSS Loading -->
    <?php load_css_files(); ?>

    <!-- RTL Support -->
    <?php load_rtl_css(); ?>

    <!-- Page-specific CSS -->
    <?php if (isset($page_css)): ?>
        <?php foreach ($page_css as $css): ?>
            <link rel="stylesheet" href="<?= rtrim(APP_URL, '/') ?>/public/css/<?= $css ?>">
        <?php endforeach; ?>
    <?php endif; ?>

    <!-- Inline CSS -->
    <?php if (isset($inline_css)): ?>
        <style><?= $inline_css ?></style>
    <?php endif; ?>
    <!-- Custom CSS -->
    <style>


        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }


    </style>
</head>
<body class="<?= current_lang() == 'ar' ? 'rtl' : '' ?> <?= is_logged_in() && current_user()['theme'] == 'dark' ? 'dark-theme' : '' ?>">
    <!-- شريط التمرير المخصص -->
    <div id="custom-scrollbar">
        <div id="scrollbar-thumb"></div>
    </div>

    <!-- زر العودة إلى الأعلى -->
    <button id="scroll-to-top" class="scroll-to-top" aria-label="<?= __('العودة إلى الأعلى') ?>">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- نافذة اختصارات لوحة المفاتيح -->
    <div id="keyboard-shortcuts-modal" class="modal-overlay">
        <div class="keyboard-shortcuts-container">
            <div class="keyboard-shortcuts-header">
                <h3><?= __('اختصارات لوحة المفاتيح') ?></h3>
                <button id="close-keyboard-shortcuts" class="close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="keyboard-shortcuts-body">
                <div class="shortcuts-section">
                    <h4><?= __('عام') ?></h4>
                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>?</kbd>
                        </div>
                        <div class="shortcut-description"><?= __('عرض/إخفاء دليل الاختصارات') ?></div>
                    </div>
                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>Alt</kbd> + <kbd>H</kbd>
                        </div>
                        <div class="shortcut-description"><?= __('الذهاب إلى الصفحة الرئيسية') ?></div>
                    </div>
                </div>

                <div class="shortcuts-section">
                    <h4><?= __('الواجهة') ?></h4>
                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>Alt</kbd> + <kbd>T</kbd>
                        </div>
                        <div class="shortcut-description"><?= __('تبديل الثيم (فاتح/داكن)') ?></div>
                    </div>
                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>Alt</kbd> + <kbd>L</kbd>
                        </div>
                        <div class="shortcut-description"><?= __('تبديل اللغة (العربية/الإنجليزية)') ?></div>
                    </div>
                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>Alt</kbd> + <kbd>S</kbd>
                        </div>
                        <div class="shortcut-description"><?= __('تصغير/توسيع القائمة الجانبية') ?></div>
                    </div>
                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>Alt</kbd> + <kbd>C</kbd>
                        </div>
                        <div class="shortcut-description"><?= __('تغيير عرض المحتوى') ?></div>
                    </div>
                </div>

                <div class="shortcuts-section">
                    <h4><?= __('التنقل') ?></h4>
                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>Alt</kbd> + <kbd>P</kbd>
                        </div>
                        <div class="shortcut-description"><?= __('الذهاب إلى الملف الشخصي') ?></div>
                    </div>
                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>Alt</kbd> + <kbd>N</kbd>
                        </div>
                        <div class="shortcut-description"><?= __('الذهاب إلى الإشعارات') ?></div>
                    </div>
                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>Alt</kbd> + <kbd>M</kbd>
                        </div>
                        <div class="shortcut-description"><?= __('الذهاب إلى الرسائل') ?></div>
                    </div>
                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>Alt</kbd> + <kbd>I</kbd>
                        </div>
                        <div class="shortcut-description"><?= __('الذهاب إلى الإعدادات') ?></div>
                    </div>
                </div>

                <div class="shortcuts-section">
                    <h4><?= __('أخرى') ?></h4>

                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>Esc</kbd>
                        </div>
                        <div class="shortcut-description"><?= __('إغلاق النوافذ المنبثقة') ?></div>
                    </div>
                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>Alt</kbd> + <kbd>↑</kbd>
                        </div>
                        <div class="shortcut-description"><?= __('العودة إلى أعلى الصفحة') ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if (is_logged_in()): ?>
        <!-- Logged in layout -->
        <div class="wrapper">
            <?php
            // تحديد نوع الصفحة
            $pageType = getCurrentPageType();

            // عرض السايدبار المناسب
            if ($pageType === 'module') {
                include BASE_PATH . '/App/Layouts/sidebar_modules.php';
            } else {
                include BASE_PATH . '/App/Layouts/sidebar_system.php';
            }
            ?>

            <!-- Content -->
            <div class="content <?= is_logged_in() && current_user()['Content_Mode'] == 'small' ? 'small-width' : '' ?> <?= is_logged_in() && current_user()['sidebar_mode'] == 'hide' ? 'expanded' : '' ?>" id="content">
                <!-- Topbar -->
                <!-- Sidebar Backdrop for Mobile -->
                <div class="sidebar-backdrop" id="sidebarBackdrop"></div>

                <!-- ===== TOPBAR - MODERN & PROFESSIONAL ===== -->
                <header class="topbar" role="banner">
                    <!-- Mobile Sidebar Toggle -->
                    <button class="mobile-sidebar-toggle"
                            id="mobileSidebarToggle"
                            type="button"
                            aria-label="<?= __('فتح القائمة الجانبية') ?>"
                            title="<?= __('فتح القائمة الجانبية') ?>">
                        <i class="fas fa-bars" aria-hidden="true"></i>
                    </button>

                    <!-- Topbar Actions Container -->
                    <div class="topbar-actions" role="toolbar" aria-label="<?= __('أدوات التطبيق') ?>">

                        <!-- Notifications Action -->
                        <div class="topbar-action"
                             id="notifications-btn"
                             role="button"
                             tabindex="0"
                             aria-label="<?= __('الإشعارات') ?> (3 <?= __('جديد') ?>)"
                             title="<?= __('الإشعارات') ?>"
                             data-action="notifications">
                            <i class="fas fa-bell" aria-hidden="true"></i>
                            <span class="topbar-action-badge" aria-label="3 <?= __('إشعارات جديدة') ?>">3</span>
                        </div>

                        <!-- Messages Action -->
                        <div class="topbar-action"
                             id="messages-btn"
                             role="button"
                             tabindex="0"
                             aria-label="<?= __('الرسائل') ?> (5 <?= __('جديد') ?>)"
                             title="<?= __('الرسائل') ?>"
                             data-action="messages">
                            <i class="fas fa-envelope" aria-hidden="true"></i>
                            <span class="topbar-action-badge" aria-label="5 <?= __('رسائل جديدة') ?>">5</span>
                        </div>

                        <!-- Quick Settings Action -->
                        <div class="topbar-action"
                             id="quick-settings-btn"
                             role="button"
                             tabindex="0"
                             aria-label="<?= __('الإعدادات السريعة') ?>"
                             title="<?= __('الإعدادات السريعة') ?>"
                             data-action="quick-settings">
                            <i class="fas fa-cog" aria-hidden="true"></i>
                        </div>

                        <!-- Keyboard Shortcuts Action -->
                        <div class="topbar-action"
                             id="keyboard-shortcuts-btn"
                             role="button"
                             tabindex="0"
                             aria-label="<?= __('اختصارات لوحة المفاتيح') ?>"
                             title="<?= __('اختصارات لوحة المفاتيح') ?> (Alt + /)"
                             data-action="keyboard-shortcuts">
                            <i class="fas fa-keyboard" aria-hidden="true"></i>
                        </div>

                        <!-- User Profile Dropdown -->
                        <div class="dropdown" id="user-dropdown">
                            <div class="topbar-user"
                                 role="button"
                                 tabindex="0"
                                 aria-expanded="false"
                                 aria-haspopup="true"
                                 aria-label="<?= __('قائمة المستخدم') ?>: <?= current_user()['FirstName'] ?> <?= current_user()['LastName'] ?>"
                                 data-bs-toggle="dropdown">

                                <!-- User Avatar -->
                                <div class="topbar-user-avatar">
                                    <?php if (!empty(current_user()['ProfilePicture']) && file_exists(BASE_PATH . '/' . current_user()['ProfilePicture'])): ?>
                                        <img src="<?= base_url(current_user()['ProfilePicture']) ?>"
                                             alt="<?= __('صورة المستخدم') ?>: <?= current_user()['FirstName'] ?> <?= current_user()['LastName'] ?>"
                                             loading="lazy">
                                    <?php else: ?>
                                        <img src="https://ui-avatars.com/api/?name=<?= urlencode(current_user()['FirstName']) ?>+<?= urlencode(current_user()['LastName']) ?>&background=2196F3&color=fff&size=128&font-size=0.6"
                                             alt="<?= __('صورة المستخدم') ?>: <?= current_user()['FirstName'] ?> <?= current_user()['LastName'] ?>"
                                             loading="lazy">
                                    <?php endif; ?>
                                </div>

                                <!-- User Info -->
                                <div class="topbar-user-info">
                                    <span class="topbar-user-name"><?= htmlspecialchars(current_user()['FirstName'] . ' ' . current_user()['LastName']) ?></span>
                                </div>

                                <!-- Dropdown Arrow -->
                                <i class="fas fa-chevron-down topbar-user-dropdown" aria-hidden="true"></i>
                            </div>
                            <!-- User Dropdown Menu -->
                            <div class="dropdown-menu dropdown-menu-end"
                                 role="menu"
                                 aria-labelledby="user-dropdown"
                                 data-bs-popper="static">

                                <!-- User Info Header -->
                                <div class="dropdown-header" role="presentation">
                                    <i class="fas fa-user-circle me-2" aria-hidden="true"></i>
                                    <?= __('إعدادات المستخدم') ?>
                                </div>

                                <!-- Profile Link -->
                                <a class="dropdown-item"
                                   href="<?= base_url('profile') ?>"
                                   role="menuitem"
                                   aria-label="<?= __('عرض الملف الشخصي') ?>">
                                    <i class="fas fa-user" aria-hidden="true"></i>
                                    <span><?= __('الملف الشخصي') ?></span>
                                </a>

                                <!-- Account Settings -->
                                <a class="dropdown-item"
                                   href="<?= base_url('settings/account') ?>"
                                   role="menuitem"
                                   aria-label="<?= __('إعدادات الحساب') ?>">
                                    <i class="fas fa-user-cog" aria-hidden="true"></i>
                                    <span><?= __('إعدادات الحساب') ?></span>
                                </a>

                                <!-- Theme Toggle -->
                                <?php if (current_user()['theme'] == 'light'): ?>
                                    <a class="dropdown-item"
                                       href="<?= base_url('settings/theme/dark') ?>"
                                       data-toggle="theme"
                                       data-theme="dark"
                                       role="menuitem"
                                       aria-label="<?= __('تفعيل الوضع الداكن') ?>">
                                        <i class="fas fa-moon" aria-hidden="true"></i>
                                        <span><?= __('الوضع الداكن') ?></span>
                                    </a>
                                <?php else: ?>
                                    <a class="dropdown-item"
                                       href="<?= base_url('settings/theme/light') ?>"
                                       data-toggle="theme"
                                       data-theme="light"
                                       role="menuitem"
                                       aria-label="<?= __('تفعيل الوضع الفاتح') ?>">
                                        <i class="fas fa-sun" aria-hidden="true"></i>
                                        <span><?= __('الوضع الفاتح') ?></span>
                                    </a>
                                <?php endif; ?>

                                <!-- Language Toggle -->
                                <?php if (current_user()['language'] == 'العربية'): ?>
                                    <a class="dropdown-item language-switch"
                                       href="<?= base_url('language/en') ?>"
                                       data-lang="en"
                                       role="menuitem"
                                       aria-label="<?= __('تغيير اللغة إلى الإنجليزية') ?>">
                                        <i class="fas fa-language" aria-hidden="true"></i>
                                        <span>English</span>
                                    </a>
                                <?php else: ?>
                                    <a class="dropdown-item language-switch"
                                       href="<?= base_url('language/ar') ?>"
                                       data-lang="ar"
                                       role="menuitem"
                                       aria-label="<?= __('تغيير اللغة إلى العربية') ?>">
                                        <i class="fas fa-language" aria-hidden="true"></i>
                                        <span>العربية</span>
                                    </a>
                                <?php endif; ?>

                                <!-- Divider -->
                                <div class="dropdown-divider" role="separator"></div>

                                <!-- Interface Settings Header -->
                                <div class="dropdown-header" role="presentation">
                                    <i class="fas fa-cogs me-2" aria-hidden="true"></i>
                                    <?= __('إعدادات الواجهة') ?>
                                </div>

                                <!-- Sidebar Mode Toggle -->
                                <?php if (current_user()['sidebar_mode'] == 'show'): ?>
                                    <a class="dropdown-item"
                                       href="<?= base_url('settings/sidebar/hide') ?>"
                                       data-toggle="sidebar-mode"
                                       data-mode="hide"
                                       role="menuitem"
                                       aria-label="<?= __('تصغير القائمة الجانبية') ?>">
                                        <i class="fas fa-angle-double-left" aria-hidden="true"></i>
                                        <span><?= __('تصغير القائمة الجانبية') ?></span>
                                    </a>
                                <?php else: ?>
                                    <a class="dropdown-item"
                                       href="<?= base_url('settings/sidebar/show') ?>"
                                       data-toggle="sidebar-mode"
                                       data-mode="show"
                                       role="menuitem"
                                       aria-label="<?= __('توسيع القائمة الجانبية') ?>">
                                        <i class="fas fa-angle-double-right" aria-hidden="true"></i>
                                        <span><?= __('توسيع القائمة الجانبية') ?></span>
                                    </a>
                                <?php endif; ?>

                                <!-- Content Mode Toggle -->
                                <?php if (current_user()['Content_Mode'] == 'large'): ?>
                                    <a class="dropdown-item"
                                       href="<?= base_url('settings/content/small') ?>"
                                       data-toggle="content-mode"
                                       data-mode="small"
                                       role="menuitem"
                                       aria-label="<?= __('تقليص المحتوى') ?>">
                                        <i class="fas fa-compress" aria-hidden="true"></i>
                                        <span><?= __('تقليص المحتوى') ?></span>
                                    </a>
                                <?php else: ?>
                                    <a class="dropdown-item"
                                       href="<?= base_url('settings/content/large') ?>"
                                       data-toggle="content-mode"
                                       data-mode="large"
                                       role="menuitem"
                                       aria-label="<?= __('توسيع المحتوى') ?>">
                                        <i class="fas fa-expand" aria-hidden="true"></i>
                                        <span><?= __('توسيع المحتوى') ?></span>
                                    </a>
                                <?php endif; ?>

                                <!-- Dashboard Toggle -->
                                <?php
                                // تحديد نوع الصفحة الحالية
                                $currentPageType = getCurrentPageType();
                                ?>
                                <?php if ($currentPageType === 'module'): ?>
                                    <a class="dropdown-item"
                                       href="<?= base_url('dashboard') ?>"
                                       role="menuitem"
                                       aria-label="<?= __('التبديل إلى لوحة تحكم النظام') ?>">
                                        <i class="fas fa-cogs" aria-hidden="true"></i>
                                        <span><?= __('لوحة تحكم النظام') ?></span>
                                    </a>
                                <?php else: ?>
                                    <a class="dropdown-item"
                                       href="<?= base_url('home') ?>"
                                       role="menuitem"
                                       aria-label="<?= __('التبديل إلى لوحة تحكم الوحدات') ?>">
                                        <i class="fas fa-cubes" aria-hidden="true"></i>
                                        <span><?= __('لوحة تحكم الوحدات') ?></span>
                                    </a>
                                <?php endif; ?>

                                <!-- Notifications Settings -->
                                <a class="dropdown-item"
                                   href="<?= base_url('settings/notifications') ?>"
                                   role="menuitem"
                                   aria-label="<?= __('إعدادات الإشعارات') ?>">
                                    <i class="fas fa-bell" aria-hidden="true"></i>
                                    <span><?= __('إعدادات الإشعارات') ?></span>
                                </a>

                                <!-- Divider -->
                                <div class="dropdown-divider" role="separator"></div>

                                <!-- Help & Support -->
                                <a class="dropdown-item"
                                   href="<?= base_url('help') ?>"
                                   role="menuitem"
                                   aria-label="<?= __('المساعدة والدعم') ?>">
                                    <i class="fas fa-question-circle" aria-hidden="true"></i>
                                    <span><?= __('المساعدة والدعم') ?></span>
                                </a>

                                <!-- Logout -->
                                <a class="dropdown-item dropdown-item-with-icon-right"
                                   href="<?= base_url('logout') ?>"
                                   role="menuitem"
                                   aria-label="<?= __('تسجيل الخروج من النظام') ?>">
                                    <i class="fas fa-sign-out-alt" aria-hidden="true"></i>
                                    <span><?= __('تسجيل الخروج') ?></span>
                                    <i class="fas fa-arrow-right dropdown-item-icon-right" aria-hidden="true"></i>
                                </a>

                                <!-- Footer -->
                                <div class="dropdown-footer" role="presentation">
                                    <small>
                                        <i class="fas fa-info-circle me-1" aria-hidden="true"></i>
                                        <?= APP_NAME ?> v<?= APP_VERSION ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </header>

                <!-- Main Content -->
                <main class="main-content">
                    <?php
    // استخدام Toastr بدلاً من display_flash
    $success = flash('success');
    if ($success) {
        echo '<script>
            document.addEventListener("DOMContentLoaded", function() {
                if (typeof toastr !== "undefined") {
                    toastr.success("' . addslashes($success['message']) . '", "' . __('نجاح') . '");
                }
            });
        </script>';
    }

    $error = flash('error');
    if ($error) {
        echo '<script>
            document.addEventListener("DOMContentLoaded", function() {
                if (typeof toastr !== "undefined") {
                    toastr.error("' . addslashes($error['message']) . '", "' . __('خطأ') . '");
                }
            });
        </script>';
    }
    ?>

                    <?= $content ?>
                </main>
            </div>
        </div>
    <?php else: ?>
        <!-- Guest layout -->
        <?= $content ?>
    <?php endif; ?>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = document.getElementById('sidebar');
            const content = document.getElementById('content');
            const sidebarToggle = document.getElementById('sidebarToggle');

            // دالة لإرسال طلب AJAX
            function sendAjaxRequest(url, callback) {
                const xhr = new XMLHttpRequest();
                xhr.open('GET', url, true);
                xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4 && xhr.status === 200) {
                        const response = JSON.parse(xhr.responseText);
                        if (callback && typeof callback === 'function') {
                            callback(response);
                        }
                    }
                };
                xhr.send();
            }

            // تبديل الثيم
            const themeToggles = document.querySelectorAll('[data-toggle="theme"]');
            themeToggles.forEach(function(toggle) {
                toggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    const theme = this.getAttribute('data-theme');
                    const url = '<?= base_url('settings/theme/') ?>' + theme;

                    // تطبيق التغيير فوراً
                    if (theme === 'dark') {
                        document.body.classList.add('dark-theme');
                        this.setAttribute('data-theme', 'light');
                        this.innerHTML = '<i class="fas fa-sun me-2"></i> <?= __('الوضع الفاتح') ?>';
                    } else {
                        document.body.classList.remove('dark-theme');
                        this.setAttribute('data-theme', 'dark');
                        this.innerHTML = '<i class="fas fa-moon me-2"></i> <?= __('الوضع الداكن') ?>';
                    }

                    // تحديث الإعدادات في قاعدة البيانات
                    sendAjaxRequest(url);
                });
            });

            // تبديل القائمة الجانبية
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function(e) {
                    e.preventDefault();

                    // تبديل حالة القائمة الجانبية
                    sidebar.classList.toggle('collapsed');
                    content.classList.toggle('expanded');

                    // تحديث الإعدادات في قاعدة البيانات
                    const newMode = sidebar.classList.contains('collapsed') ? 'hide' : 'show';
                    const url = '<?= base_url('settings/sidebar/') ?>' + newMode;
                    sendAjaxRequest(url);
                });

                // تفعيل السايدبار على الأجهزة المحمولة
                const mobileSidebarToggle = document.getElementById('mobileSidebarToggle');
                const mobileSidebarClose = document.getElementById('mobileSidebarClose');
                const sidebarBackdrop = document.getElementById('sidebarBackdrop');

                if (mobileSidebarToggle && sidebar && sidebarBackdrop) {
                    // فتح السايدبار عند النقر على زر التبديل
                    mobileSidebarToggle.addEventListener('click', function(e) {
                        e.preventDefault();
                        sidebar.classList.add('mobile-show');
                        sidebarBackdrop.classList.add('show');
                        document.body.style.overflow = 'hidden'; // منع التمرير في الصفحة
                    });

                    // إغلاق السايدبار عند النقر على زر الإغلاق
                    if (mobileSidebarClose) {
                        mobileSidebarClose.addEventListener('click', function(e) {
                            e.preventDefault();
                            sidebar.classList.remove('mobile-show');
                            sidebarBackdrop.classList.remove('show');
                            document.body.style.overflow = ''; // السماح بالتمرير مرة أخرى
                        });
                    }

                    // إغلاق السايدبار عند النقر على الخلفية
                    sidebarBackdrop.addEventListener('click', function() {
                        sidebar.classList.remove('mobile-show');
                        sidebarBackdrop.classList.remove('show');
                        document.body.style.overflow = ''; // السماح بالتمرير مرة أخرى
                    });
                }

                // إظهار/إخفاء زر الإغلاق حسب حجم الشاشة
                const handleResize = () => {
                    if (mobileSidebarClose) {
                        if (window.innerWidth < 992) {
                            mobileSidebarClose.style.display = 'flex';
                            mobileSidebarToggle.style.display = 'flex';
                        } else {
                            mobileSidebarClose.style.display = 'none';
                            mobileSidebarToggle.style.display = 'none';
                            // إغلاق السايدبار على الأجهزة الكبيرة
                            sidebar.classList.remove('mobile-show');
                            sidebarBackdrop.classList.remove('show');
                            document.body.style.overflow = '';
                        }
                    }
                };

                window.addEventListener('resize', handleResize);
                handleResize();
            }

            // تبديل وضع القائمة الجانبية من القائمة المنسدلة
            const sidebarModeToggles = document.querySelectorAll('[data-toggle="sidebar-mode"]');
            sidebarModeToggles.forEach(function(toggle) {
                toggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    const mode = this.getAttribute('data-mode');
                    const url = '<?= base_url('settings/sidebar/') ?>' + mode;

                    // تطبيق التغيير فوراً
                    if (mode === 'hide') {
                        sidebar.classList.add('collapsed');
                        content.classList.add('expanded');
                        this.setAttribute('data-mode', 'show');
                        this.innerHTML = '<i class="fas fa-angle-double-right me-2"></i> <?= __('توسيع القائمة الجانبية') ?>';
                    } else {
                        sidebar.classList.remove('collapsed');
                        content.classList.remove('expanded');
                        this.setAttribute('data-mode', 'hide');
                        this.innerHTML = '<i class="fas fa-angle-double-left me-2"></i> <?= __('تصغير القائمة الجانبية') ?>';
                    }

                    // تحديث الإعدادات في قاعدة البيانات
                    sendAjaxRequest(url);
                });
            });

            // تبديل وضع المحتوى
            const contentModeToggles = document.querySelectorAll('[data-toggle="content-mode"]');
            contentModeToggles.forEach(function(toggle) {
                toggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    const mode = this.getAttribute('data-mode');
                    const url = '<?= base_url('settings/content/') ?>' + mode;

                    // تطبيق التغيير فوراً
                    if (mode === 'small') {
                        content.classList.add('small-width');
                        this.setAttribute('data-mode', 'large');
                        this.innerHTML = '<i class="fas fa-expand me-2"></i> <?= __('توسيع المحتوى') ?>';
                    } else {
                        content.classList.remove('small-width');
                        this.setAttribute('data-mode', 'small');
                        this.innerHTML = '<i class="fas fa-compress me-2"></i> <?= __('تقليص المحتوى') ?>';
                    }

                    // تحديث الإعدادات في قاعدة البيانات
                    sendAjaxRequest(url);
                });
            });

            // تفعيل القوائم المنسدلة بشكل مخصص
            const dropdowns = document.querySelectorAll('.dropdown');
            dropdowns.forEach(function(dropdown) {
                const toggle = dropdown.querySelector('.topbar-user');
                const menu = dropdown.querySelector('.dropdown-menu');

                if (toggle && menu) {
                    // إضافة معالج النقر
                    toggle.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();

                        // إغلاق جميع القوائم المنسدلة الأخرى
                        dropdowns.forEach(function(otherDropdown) {
                            if (otherDropdown !== dropdown && otherDropdown.classList.contains('show')) {
                                otherDropdown.classList.remove('show');
                            }
                        });

                        // تبديل حالة القائمة المنسدلة الحالية
                        dropdown.classList.toggle('show');
                    });

                    // إضافة تأثيرات للعناصر
                    const items = menu.querySelectorAll('.dropdown-item');
                    items.forEach(function(item, index) {
                        // إضافة تأخير للظهور التدريجي
                        item.style.transitionDelay = (index * 0.03) + 's';

                        // إضافة معالج النقر
                        item.addEventListener('click', function() {
                            // إغلاق القائمة المنسدلة بعد النقر
                            setTimeout(function() {
                                dropdown.classList.remove('show');
                            }, 100);
                        });
                    });
                }
            });

            // إغلاق القائمة المنسدلة عند النقر خارجها
            document.addEventListener('click', function(e) {
                dropdowns.forEach(function(dropdown) {
                    if (!dropdown.contains(e.target) && dropdown.classList.contains('show')) {
                        dropdown.classList.remove('show');
                    }
                });
            });

            // معالجة تغيير اللغة وتطبيق التغييرات على شريط التمرير
            const languageSwitches = document.querySelectorAll('.language-switch');
            languageSwitches.forEach(function(langSwitch) {
                langSwitch.addEventListener('click', function(e) {
                    const lang = this.getAttribute('data-lang');

                    // تطبيق التغييرات فوراً قبل إعادة تحميل الصفحة
                    if (lang === 'ar') {
                        document.body.classList.add('rtl');
                        document.documentElement.setAttribute('dir', 'rtl');
                    } else {
                        document.body.classList.remove('rtl');
                        document.documentElement.setAttribute('dir', 'ltr');
                    }

                    // تطبيق تغييرات شريط التمرير
                    applyScrollbarStyles();

                    // إعادة تهيئة شريط التمرير المخصص
                    initCustomScrollbar();
                });
            });

            // دالة لتطبيق أنماط شريط التمرير حسب اللغة
            function applyScrollbarStyles() {
                const isRTL = document.body.classList.contains('rtl');

                if (isRTL) {
                    // تطبيق أنماط للغة العربية (شريط التمرير على اليسار)
                    document.documentElement.style.overflowY = 'auto';
                    document.documentElement.style.direction = 'rtl';
                } else {
                    // تطبيق أنماط للغة الإنجليزية (شريط التمرير على اليمين)
                    document.documentElement.style.overflowY = 'auto';
                    document.documentElement.style.direction = 'ltr';
                }
            }

            // تطبيق أنماط شريط التمرير عند تحميل الصفحة
            applyScrollbarStyles();

            // تهيئة شريط التمرير المخصص
            initCustomScrollbar();

            // تهيئة زر العودة إلى الأعلى
            initScrollToTopButton();

            // تهيئة اختصارات لوحة المفاتيح
            initKeyboardShortcuts();

            // دالة لتهيئة شريط التمرير المخصص
            function initCustomScrollbar() {
                const scrollbarThumb = document.getElementById('scrollbar-thumb');
                const customScrollbar = document.getElementById('custom-scrollbar');

                if (!scrollbarThumb || !customScrollbar) return;

                // حساب نسبة ارتفاع شريط التمرير
                function updateScrollbarThumb() {
                    const windowHeight = window.innerHeight;
                    const documentHeight = document.documentElement.scrollHeight;

                    // التحقق مما إذا كانت هناك حاجة لشريط التمرير
                    if (documentHeight <= windowHeight) {
                        // إخفاء شريط التمرير إذا لم تكن هناك حاجة له
                        customScrollbar.style.display = 'none';
                        return;
                    } else {
                        // إظهار شريط التمرير إذا كانت هناك حاجة له
                        customScrollbar.style.display = 'block';
                    }

                    // حساب نسبة ارتفاع الشريط
                    const scrollThumbHeight = (windowHeight / documentHeight) * windowHeight;

                    // تعيين الحد الأدنى لارتفاع الشريط
                    const minHeight = 30;
                    scrollbarThumb.style.height = Math.max(scrollThumbHeight, minHeight) + 'px';

                    // تحديث موضع الشريط
                    updateScrollbarPosition();
                }

                // تحديث موضع شريط التمرير
                function updateScrollbarPosition() {
                    // التحقق مما إذا كانت هناك حاجة لشريط التمرير
                    const windowHeight = window.innerHeight;
                    const documentHeight = document.documentElement.scrollHeight;

                    if (documentHeight <= windowHeight) {
                        // إخفاء شريط التمرير إذا لم تكن هناك حاجة له
                        customScrollbar.style.display = 'none';
                        return;
                    } else {
                        // إظهار شريط التمرير إذا كانت هناك حاجة له
                        customScrollbar.style.display = 'block';
                    }

                    const scrollPercentage = window.scrollY / (documentHeight - windowHeight);
                    const maxScrollTop = windowHeight - parseFloat(scrollbarThumb.style.height || '30px');
                    const scrollTop = scrollPercentage * maxScrollTop;

                    // تجنب القيم غير الصالحة
                    if (isNaN(scrollTop) || !isFinite(scrollTop)) {
                        return;
                    }

                    scrollbarThumb.style.top = scrollTop + 'px';
                }

                // تحديث شريط التمرير عند التمرير
                window.addEventListener('scroll', updateScrollbarPosition);

                // تحديث شريط التمرير عند تغيير حجم النافذة
                window.addEventListener('resize', updateScrollbarThumb);

                // تحديث شريط التمرير عند تحميل المحتوى (الصور، الإطارات، إلخ)
                window.addEventListener('load', updateScrollbarThumb);

                // تحديث شريط التمرير عند تغيير محتوى الصفحة (AJAX، إضافة عناصر ديناميكية)
                const observer = new MutationObserver(function(mutations) {
                    updateScrollbarThumb();
                });

                // مراقبة التغييرات في محتوى الصفحة
                observer.observe(document.body, {
                    childList: true,
                    subtree: true,
                    attributes: true,
                    attributeFilter: ['style', 'class']
                });

                // تهيئة شريط التمرير عند تحميل الصفحة
                updateScrollbarThumb();

                // تحديث شريط التمرير بعد فترة قصيرة للتأكد من تحميل جميع العناصر
                setTimeout(updateScrollbarThumb, 500);

                // إضافة تفاعل للشريط
                scrollbarThumb.addEventListener('mouseenter', function() {
                    this.style.opacity = '0.9';
                });

                scrollbarThumb.addEventListener('mouseleave', function() {
                    this.style.opacity = '0.7';
                });

                // تعديل موضع الشريط حسب اللغة
                const isRTL = document.body.classList.contains('rtl') || document.documentElement.dir === 'rtl';
                if (isRTL) {
                    scrollbarThumb.style.right = 'auto';
                    scrollbarThumb.style.left = '2px';
                    customScrollbar.style.right = 'auto';
                    customScrollbar.style.left = '0';
                } else {
                    scrollbarThumb.style.left = 'auto';
                    scrollbarThumb.style.right = '2px';
                    customScrollbar.style.left = 'auto';
                    customScrollbar.style.right = '0';
                }

                // جعل شريط التمرير قابل للتفاعل
                scrollbarThumb.style.pointerEvents = 'auto';

                // إضافة وظيفة السحب والإفلات للشريط
                let isDragging = false;
                let startY = 0;
                let startScrollY = 0;

                scrollbarThumb.addEventListener('mousedown', function(e) {
                    isDragging = true;
                    startY = e.clientY;
                    startScrollY = window.scrollY;
                    document.body.style.userSelect = 'none'; // منع تحديد النص أثناء السحب
                });

                document.addEventListener('mousemove', function(e) {
                    if (!isDragging) return;

                    const deltaY = e.clientY - startY;
                    const scrollRatio = document.documentElement.scrollHeight / window.innerHeight;
                    window.scrollTo(0, startScrollY + deltaY * scrollRatio);
                });

                document.addEventListener('mouseup', function() {
                    isDragging = false;
                    document.body.style.userSelect = ''; // إعادة تمكين تحديد النص
                });
            }

         // دالة لتهيئة اختصارات لوحة المفاتيح
function initKeyboardShortcuts() {
  const shortcutsModal    = document.getElementById('keyboard-shortcuts-modal');
  const shortcutsBtn      = document.getElementById('keyboard-shortcuts-btn');
  const closeShortcutsBtn = document.getElementById('close-keyboard-shortcuts');

  if (!shortcutsModal || !shortcutsBtn || !closeShortcutsBtn) return;

  // فتح مودال الاختصارات
  shortcutsBtn.addEventListener('click', () => {
    shortcutsModal.classList.add('show');
  });

  // إغلاق مودال الاختصارات (زر أو خارج المودال)
  closeShortcutsBtn.addEventListener('click', () => shortcutsModal.classList.remove('show'));
  shortcutsModal.addEventListener('click', e => {
    if (e.target === shortcutsModal) shortcutsModal.classList.remove('show');
  });

  // دالة موحدة للتعامل مع ضغطات المفاتيح
  document.addEventListener('keydown', e => {
    const tag = e.target.tagName;
    const editable = e.target.isContentEditable;

    // 1) إغلاق المودال بالـ Escape
    if (e.key === 'Escape' && shortcutsModal.classList.contains('show')) {
      shortcutsModal.classList.remove('show');
      return;
    }

    // 2) فتح/إغلاق المودال بالـ '?' (Shift + '/')
    if (!e.altKey && !e.ctrlKey && !e.metaKey && e.key === '?') {
      e.preventDefault();
      shortcutsModal.classList.toggle('show');
      return;
    }

    // 3) بقية الاختصارات (تجاهل الحقول القابلة للتعديل)
    if (tag === 'INPUT' || tag === 'TEXTAREA' || editable) return;

    // 4) اختصارات Alt + مفتاح (باستخدام e.code للغة مستقلّة)
    if (e.altKey && !e.ctrlKey && !e.metaKey) {
      switch (e.code) {
        case 'KeyT': // Alt + T: تبديل الثيم
          e.preventDefault();
          document.querySelector('[data-toggle="theme"]')?.click();
          break;

        case 'KeyL': // Alt + L: تبديل اللغة
          e.preventDefault();
          document.querySelector('.language-switch')?.click();
          break;

        case 'KeyS': // Alt + S: تصغير/توسيع السايدبار
          e.preventDefault();
          document.getElementById('sidebarToggle')?.click();
          break;

        case 'KeyC': // Alt + C: تغيير عرض المحتوى
          e.preventDefault();
          document.querySelector('[data-toggle="content-mode"]')?.click();
          break;

        case 'KeyH': // Alt + H: الصفحة الرئيسية
          e.preventDefault();
          window.location.href = '<?= base_url('home') ?>';
          break;

        case 'KeyD': // Alt + D: لوحة التحكم (Dashboard)
          e.preventDefault();
          window.location.href = '<?= base_url('dashboard') ?>';
          break;

        case 'KeyP': // Alt + P: الملف الشخصي
          e.preventDefault();
          window.location.href = '<?= base_url('profile') ?>';
          break;

        case 'KeyN': // Alt + N: الإشعارات
          e.preventDefault();
          window.location.href = '<?= base_url('notifications') ?>';
          break;

        case 'KeyM': // Alt + M: الرسائل
          e.preventDefault();
          window.location.href = '<?= base_url('chat') ?>';
          break;

        case 'KeyI': // Alt + I: الإعدادات
          e.preventDefault();
          window.location.href = '<?= base_url('settings') ?>';
          break;

        case 'ArrowUp': // Alt + ↑: العودة لأعلى الصفحة
          e.preventDefault();
          window.scrollTo({ top: 0, behavior: 'smooth' });
          break;
           case 'ArrowDown': // Alt + ↓: الانتقال إلى أسفل الصفحة
    e.preventDefault();
    window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
    break;
      }
    }
  });
}

// استدعاء التهيئة بعد تحميل الـ DOM
document.addEventListener('DOMContentLoaded', initKeyboardShortcuts);


            // دالة لتهيئة زر العودة إلى الأعلى
            function initScrollToTopButton() {
                const scrollToTopBtn = document.getElementById('scroll-to-top');

                if (!scrollToTopBtn) return;

                // تحديد متى يظهر الزر (بعد التمرير لمسافة معينة)
                const toggleScrollToTopButton = () => {
                    // ظهور الزر بعد التمرير لمسافة 300 بكسل
                    if (window.scrollY > 300) {
                        scrollToTopBtn.classList.add('visible');
                    } else {
                        scrollToTopBtn.classList.remove('visible');
                    }
                };

                // إضافة مستمع للتمرير لإظهار/إخفاء الزر
                window.addEventListener('scroll', toggleScrollToTopButton);

                // إضافة مستمع للنقر على الزر للعودة إلى الأعلى
                scrollToTopBtn.addEventListener('click', (e) => {
                    e.preventDefault();

                    // التمرير بسلاسة إلى الأعلى
                    window.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                    });
                });

                // تحديث حالة الزر عند تحميل الصفحة
                toggleScrollToTopButton();

                // تحديث موضع الزر حسب اللغة
                const updateButtonPosition = () => {
                    const isRTL = document.body.classList.contains('rtl') || document.documentElement.dir === 'rtl';

                    if (isRTL) {
                        scrollToTopBtn.style.right = 'auto';
                        scrollToTopBtn.style.left = '30px';
                    } else {
                        scrollToTopBtn.style.left = 'auto';
                        scrollToTopBtn.style.right = '30px';
                    }
                };

                // تحديث موضع الزر عند تغيير اللغة
                const languageSwitches = document.querySelectorAll('.language-switch');
                languageSwitches.forEach(langSwitch => {
                    langSwitch.addEventListener('click', updateButtonPosition);
                });

                // تحديث موضع الزر عند تحميل الصفحة
                updateButtonPosition();
            }

            // إضافة النوافذ المنبثقة للسايدبار المصغر بشكل مشابه للبرامج العالمية
            function initEnterpriseTooltips() {
                // إضافة CSS للنوافذ المنبثقة
                const style = document.createElement('style');
                style.textContent = `
                    .sidebar-popup {
                        position: fixed;
                        background-color: #0f2a45;
                        color: white;
                        border-radius: 4px;
                        z-index: 9999;
                        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
                        opacity: 0;
                        transform: translateX(-10px) translateY(5px);
                        transition: all 0.25s cubic-bezier(0.3, 0, 0.2, 1);
                        display: none;
                        overflow: hidden;
                        min-width: 220px;
                        border: 1px solid rgba(255, 255, 255, 0.08);
                        backdrop-filter: blur(10px);
                        -webkit-backdrop-filter: blur(10px);
                    }

                    .rtl .sidebar-popup {
                        transform: translateX(10px) translateY(5px);
                    }

                    .sidebar-popup.show {
                        opacity: 1;
                        transform: translateX(0) translateY(0);
                    }

                    .sidebar-popup-header {
                        padding: 14px 16px;
                        font-weight: 600;
                        font-size: 0.95rem;
                        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                        display: flex;
                        align-items: center;
                        background-color: rgba(255, 255, 255, 0.03);
                        letter-spacing: 0.01em;
                    }

                    .sidebar-popup-header.clickable {
                        cursor: pointer;
                        transition: background-color 0.2s ease;
                    }

                    .sidebar-popup-header.clickable:hover {
                        background-color: rgba(255, 255, 255, 0.1);
                    }

                    .sidebar-popup-header i {
                        margin-right: 12px;
                        width: 22px;
                        text-align: center;
                        font-size: 1.1rem;
                        color: rgba(255, 255, 255, 0.9);
                    }

                    .rtl .sidebar-popup-header i {
                        margin-right: 0;
                        margin-left: 12px;
                    }

                    .sidebar-popup-item {
                        padding: 12px 16px;
                        display: flex;
                        align-items: center;
                        cursor: pointer;
                        transition: all 0.2s ease;
                        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
                    }

                    .sidebar-popup-item:last-child {
                        border-bottom: none;
                    }

                    .sidebar-popup-item:hover {
                        background-color: rgba(255, 255, 255, 0.08);
                        padding-left: 20px;
                    }

                    .rtl .sidebar-popup-item:hover {
                        padding-left: 16px;
                        padding-right: 20px;
                    }

                    .sidebar-popup-item i {
                        margin-right: 12px;
                        width: 20px;
                        text-align: center;
                        opacity: 0.8;
                        font-size: 0.95rem;
                        transition: all 0.2s ease;
                    }

                    .sidebar-popup-item:hover i {
                        opacity: 1;
                        transform: scale(1.1);
                    }

                    .rtl .sidebar-popup-item i {
                        margin-right: 0;
                        margin-left: 12px;
                    }

                    .sidebar-popup-item span {
                        flex: 1;
                        font-weight: 500;
                        font-size: 0.9rem;
                    }

                    body.dark-theme .sidebar-popup {
                        background-color: #1a202c;
                        border-color: rgba(255, 255, 255, 0.05);
                    }
                `;
                document.head.appendChild(style);

                const sidebar = document.querySelector('.sidebar');
                const sidebarLinks = document.querySelectorAll('.sidebar-menu-link');

                // تحديد ما إذا كانت اللغة من اليمين إلى اليسار
                const isRTL = document.dir === 'rtl' || document.documentElement.lang === 'ar';

                // إنشاء النوافذ المنبثقة لكل رابط
                sidebarLinks.forEach(link => {
                    // تخطي الروابط التي ليس لها عنوان
                    if (!link.getAttribute('data-title')) return;

                    // إنشاء النافذة المنبثقة
                    const popup = document.createElement('div');
                    popup.className = 'sidebar-popup';
                    if (isRTL) popup.classList.add('rtl');
                    document.body.appendChild(popup);

                    // إضافة رأس النافذة المنبثقة
                    const header = document.createElement('div');
                    header.className = 'sidebar-popup-header';

                    // نسخ الأيقونة من الرابط
                    const icon = link.querySelector('i').cloneNode(true);
                    header.appendChild(icon);

                    // إضافة العنوان
                    const title = document.createElement('span');
                    title.textContent = link.getAttribute('data-title');
                    header.appendChild(title);

                    popup.appendChild(header);

                    // إضافة عناصر النافذة المنبثقة إذا كان الرابط له قائمة منسدلة
                    const submenu = link.nextElementSibling && link.nextElementSibling.classList.contains('collapse')
                        ? link.nextElementSibling
                        : null;

                    if (submenu) {
                        // إذا كان هناك قائمة منسدلة، أضف العناصر الفرعية
                        const submenuLinks = submenu.querySelectorAll('.sidebar-menu-link');
                        submenuLinks.forEach(sublink => {
                            const item = document.createElement('div');
                            item.className = 'sidebar-popup-item';

                            // نسخ الأيقونة من الرابط الفرعي
                            const subIcon = sublink.querySelector('i').cloneNode(true);
                            item.appendChild(subIcon);

                            // إضافة العنوان
                            const subTitle = document.createElement('span');
                            subTitle.textContent = sublink.getAttribute('data-title') || sublink.querySelector('.sidebar-menu-text').textContent;
                            item.appendChild(subTitle);

                            // إضافة معالج النقر
                            item.addEventListener('click', () => {
                                window.location.href = sublink.getAttribute('href');
                            });

                            popup.appendChild(item);
                        });
                    } else {
                        // إذا لم يكن هناك قائمة منسدلة، لا تضف عناصر إضافية
                        // فقط اجعل رأس النافذة المنبثقة قابل للنقر
                        header.classList.add('clickable');
                        header.addEventListener('click', () => {
                            if (link.getAttribute('href') && link.getAttribute('href') !== '#') {
                                window.location.href = link.getAttribute('href');
                            }
                        });

                        // لا نضيف سهمًا حسب طلب المستخدم
                    }

                    // إضافة معالجات الأحداث
                    link.addEventListener('mouseenter', function() {
                        if (sidebar.classList.contains('collapsed')) {
                            // إخفاء جميع النوافذ المنبثقة الأخرى
                            document.querySelectorAll('.sidebar-popup').forEach(p => {
                                p.style.display = 'none';
                                p.classList.remove('show');
                            });

                            // تحديد موضع النافذة المنبثقة
                            const rect = this.getBoundingClientRect();

                            // تعديل موضع النافذة المنبثقة لتكون محاذية للرابط
                            popup.style.top = (rect.top - 5) + 'px';

                            if (isRTL) {
                                popup.style.right = (window.innerWidth - rect.left + 10) + 'px';
                                popup.style.left = 'auto';
                            } else {
                                popup.style.left = (rect.right + 10) + 'px';
                                popup.style.right = 'auto';
                            }

                            // إظهار النافذة المنبثقة بتأثير سلس
                            popup.style.display = 'block';
                            requestAnimationFrame(() => {
                                popup.classList.add('show');
                            });
                        }
                    });

                    // إضافة معالج لإخفاء النافذة المنبثقة عند مغادرة الرابط
                    link.addEventListener('mouseleave', function(e) {
                        // تأخير قصير للسماح بالانتقال من الرابط إلى النافذة المنبثقة
                        setTimeout(() => {
                            if (!popup.matches(':hover')) {
                                hidePopup();
                            }
                        }, 50);
                    });

                    // إضافة معالج لإخفاء النافذة المنبثقة عند مغادرة النافذة
                    popup.addEventListener('mouseleave', function(e) {
                        if (e.relatedTarget !== link) {
                            hidePopup();
                        }
                    });

                    // دالة موحدة لإخفاء النافذة المنبثقة
                    function hidePopup() {
                        popup.classList.remove('show');
                        setTimeout(() => {
                            popup.style.display = 'none';
                        }, 200);
                    }
                });
            }

            // تهيئة النوافذ المنبثقة بعد تحميل الصفحة
            if (document.readyState === 'complete') {
                initEnterpriseTooltips();
            } else {
                window.addEventListener('load', initEnterpriseTooltips);
            }
        });
    </script>
</body>
</html>
