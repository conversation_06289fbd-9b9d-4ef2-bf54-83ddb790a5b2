<?php
/**
 * Template موحد للـ Pagination
 */

if ($pagination['total_pages'] <= 1) {
    return;
}
?>

<ul class="pagination pagination-rounded mb-0">
    <!-- Previous Button -->
    <li class="page-item <?= !$pagination['has_previous'] ? 'disabled' : '' ?>">
        <?php if ($pagination['has_previous']): ?>
            <a class="page-link" href="javascript:DataTable.goToPage(<?= $pagination['previous_page'] ?>)">
                السابق
            </a>
        <?php else: ?>
            <span class="page-link">السابق</span>
        <?php endif; ?>
    </li>

    <?php
    // حساب نطاق الصفحات المعروضة
    $start = max(1, $pagination['current_page'] - 2);
    $end = min($pagination['total_pages'], $pagination['current_page'] + 2);

    // إضافة الصفحة الأولى إذا لم تكن ضمن النطاق
    if ($start > 1): ?>
        <li class="page-item">
            <a class="page-link" href="javascript:DataTable.goToPage(1)">1</a>
        </li>
        <?php if ($start > 2): ?>
            <li class="page-item disabled">
                <span class="page-link">...</span>
            </li>
        <?php endif; ?>
    <?php endif; ?>

    <!-- صفحات النطاق الحالي -->
    <?php for ($i = $start; $i <= $end; $i++): ?>
        <li class="page-item <?= $i == $pagination['current_page'] ? 'active' : '' ?>">
            <?php if ($i == $pagination['current_page']): ?>
                <span class="page-link"><?= $i ?></span>
            <?php else: ?>
                <a class="page-link" href="javascript:DataTable.goToPage(<?= $i ?>)"><?= $i ?></a>
            <?php endif; ?>
        </li>
    <?php endfor; ?>

    <!-- إضافة الصفحة الأخيرة إذا لم تكن ضمن النطاق -->
    <?php if ($end < $pagination['total_pages']): ?>
        <?php if ($end < $pagination['total_pages'] - 1): ?>
            <li class="page-item disabled">
                <span class="page-link">...</span>
            </li>
        <?php endif; ?>
        <li class="page-item">
            <a class="page-link" href="javascript:DataTable.goToPage(<?= $pagination['total_pages'] ?>)">
                <?= $pagination['total_pages'] ?>
            </a>
        </li>
    <?php endif; ?>

    <!-- Next Button -->
    <li class="page-item <?= !$pagination['has_next'] ? 'disabled' : '' ?>">
        <?php if ($pagination['has_next']): ?>
            <a class="page-link" href="javascript:DataTable.goToPage(<?= $pagination['next_page'] ?>)">
                التالي
            </a>
        <?php else: ?>
            <span class="page-link">التالي</span>
        <?php endif; ?>
    </li>
</ul>
