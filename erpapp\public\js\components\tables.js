/**
 * مكون الجداول - تحسينات وتفاعلات الجداول
 * Tables Component - Table enhancements and interactions
 */

(function() {
    'use strict';

    // انتظار تحميل DOM
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initTables);
    } else {
        initTables();
    }

    function initTables() {
        console.log('🚀 بدء تهيئة مكون الجداول...');

        initSortableTables();
        initSelectableRows();
        initResponsiveTables();
        initTableActions();

        console.log('✅ تم تهيئة مكون الجداول بنجاح');
    }

    // ===== الجداول القابلة للترتيب ===== //
    function initSortableTables() {
        const sortableHeaders = document.querySelectorAll('th[data-sortable="true"]');
        
        sortableHeaders.forEach(function(header) {
            header.addEventListener('click', function() {
                sortTable(header);
            });

            // إضافة أيقونة الترتيب
            if (!header.querySelector('.sort-icon')) {
                const sortIcon = document.createElement('span');
                sortIcon.className = 'sort-icon';
                sortIcon.innerHTML = '<i class="fas fa-sort text-muted"></i>';
                header.appendChild(sortIcon);
            }
        });
    }

    // ===== الصفوف القابلة للتحديد ===== //
    function initSelectableRows() {
        const selectableRows = document.querySelectorAll('tr[data-selectable="true"]');
        const selectAllCheckbox = document.querySelector('input[data-select-all="true"]');
        
        // تحديد/إلغاء تحديد جميع الصفوف
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('tr[data-selectable="true"] input[type="checkbox"]');
                checkboxes.forEach(function(checkbox) {
                    checkbox.checked = selectAllCheckbox.checked;
                    toggleRowSelection(checkbox.closest('tr'), checkbox.checked);
                });
                updateBulkActions();
            });
        }

        // تحديد الصفوف الفردية
        selectableRows.forEach(function(row) {
            const checkbox = row.querySelector('input[type="checkbox"]');
            if (checkbox) {
                checkbox.addEventListener('change', function() {
                    toggleRowSelection(row, checkbox.checked);
                    updateSelectAllState();
                    updateBulkActions();
                });
            }
        });
    }

    // ===== الجداول المتجاوبة ===== //
    function initResponsiveTables() {
        const tables = document.querySelectorAll('.table-responsive');
        
        tables.forEach(function(tableContainer) {
            const table = tableContainer.querySelector('table');
            if (table) {
                makeTableResponsive(table);
            }
        });
    }

    // ===== إجراءات الجدول ===== //
    function initTableActions() {
        // أزرار الإجراءات
        const actionButtons = document.querySelectorAll('[data-table-action]');
        
        actionButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const action = button.dataset.tableAction;
                const target = button.dataset.target;
                
                handleTableAction(action, target, button);
            });
        });

        // إجراءات الصف
        const rowActions = document.querySelectorAll('[data-row-action]');
        
        rowActions.forEach(function(action) {
            action.addEventListener('click', function(e) {
                e.preventDefault();
                const actionType = action.dataset.rowAction;
                const rowId = action.dataset.rowId;
                
                handleRowAction(actionType, rowId, action);
            });
        });
    }

    // ===== وظائف مساعدة ===== //

    function sortTable(header) {
        const table = header.closest('table');
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        const columnIndex = Array.from(header.parentNode.children).indexOf(header);
        const currentSort = header.dataset.sort || 'none';
        
        // تحديد اتجاه الترتيب الجديد
        let newSort;
        if (currentSort === 'none' || currentSort === 'desc') {
            newSort = 'asc';
        } else {
            newSort = 'desc';
        }

        // إزالة الترتيب من جميع الأعمدة
        const allHeaders = table.querySelectorAll('th[data-sortable="true"]');
        allHeaders.forEach(function(h) {
            h.dataset.sort = 'none';
            h.classList.remove('sorted-asc', 'sorted-desc');
            const icon = h.querySelector('.sort-icon i');
            if (icon) {
                icon.className = 'fas fa-sort text-muted';
            }
        });

        // تطبيق الترتيب الجديد
        header.dataset.sort = newSort;
        header.classList.add('sorted-' + newSort);
        
        const icon = header.querySelector('.sort-icon i');
        if (icon) {
            icon.className = newSort === 'asc' ? 'fas fa-sort-up text-primary' : 'fas fa-sort-down text-primary';
        }

        // ترتيب الصفوف
        rows.sort(function(a, b) {
            const aValue = getCellValue(a, columnIndex);
            const bValue = getCellValue(b, columnIndex);
            
            if (newSort === 'asc') {
                return aValue.localeCompare(bValue, undefined, { numeric: true });
            } else {
                return bValue.localeCompare(aValue, undefined, { numeric: true });
            }
        });

        // إعادة ترتيب الصفوف في الجدول
        rows.forEach(function(row) {
            tbody.appendChild(row);
        });
    }

    function getCellValue(row, columnIndex) {
        const cell = row.children[columnIndex];
        if (!cell) return '';
        
        // البحث عن قيمة البيانات أولاً
        const dataValue = cell.dataset.value;
        if (dataValue) return dataValue;
        
        // استخراج النص من الخلية
        return cell.textContent.trim();
    }

    function toggleRowSelection(row, selected) {
        if (selected) {
            row.classList.add('selected');
        } else {
            row.classList.remove('selected');
        }
    }

    function updateSelectAllState() {
        const selectAllCheckbox = document.querySelector('input[data-select-all="true"]');
        if (!selectAllCheckbox) return;

        const checkboxes = document.querySelectorAll('tr[data-selectable="true"] input[type="checkbox"]');
        const checkedBoxes = document.querySelectorAll('tr[data-selectable="true"] input[type="checkbox"]:checked');
        
        if (checkedBoxes.length === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (checkedBoxes.length === checkboxes.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
            selectAllCheckbox.checked = false;
        }
    }

    function updateBulkActions() {
        const checkedBoxes = document.querySelectorAll('tr[data-selectable="true"] input[type="checkbox"]:checked');
        const bulkActions = document.querySelector('.bulk-actions');
        
        if (bulkActions) {
            if (checkedBoxes.length > 0) {
                bulkActions.style.display = 'block';
                const countElement = bulkActions.querySelector('.selected-count');
                if (countElement) {
                    countElement.textContent = checkedBoxes.length;
                }
            } else {
                bulkActions.style.display = 'none';
            }
        }
    }

    function makeTableResponsive(table) {
        // إضافة تسميات للخلايا على الشاشات الصغيرة
        const headers = table.querySelectorAll('thead th');
        const rows = table.querySelectorAll('tbody tr');
        
        rows.forEach(function(row) {
            const cells = row.querySelectorAll('td');
            cells.forEach(function(cell, index) {
                if (headers[index]) {
                    const label = headers[index].textContent.trim();
                    cell.setAttribute('data-label', label);
                }
            });
        });
    }

    function handleTableAction(action, target, button) {
        switch (action) {
            case 'refresh':
                refreshTable(target);
                break;
            case 'export':
                exportTable(target, button.dataset.format || 'csv');
                break;
            case 'print':
                printTable(target);
                break;
            case 'bulk-delete':
                bulkDeleteRows();
                break;
            default:
                console.warn('Unknown table action:', action);
        }
    }

    function handleRowAction(action, rowId, element) {
        switch (action) {
            case 'edit':
                editRow(rowId);
                break;
            case 'delete':
                deleteRow(rowId, element);
                break;
            case 'view':
                viewRow(rowId);
                break;
            default:
                console.warn('Unknown row action:', action);
        }
    }

    function refreshTable(target) {
        const table = document.querySelector(target);
        if (table) {
            // إضافة مؤشر التحميل
            table.classList.add('table-loading');
            
            // محاكاة إعادة التحميل
            setTimeout(() => {
                table.classList.remove('table-loading');
                showSuccess('تم تحديث الجدول');
            }, 1000);
        }
    }

    function exportTable(target, format) {
        const table = document.querySelector(target);
        if (!table) return;

        if (format === 'csv') {
            exportToCSV(table);
        } else if (format === 'excel') {
            exportToExcel(table);
        }
    }

    function exportToCSV(table) {
        const rows = table.querySelectorAll('tr');
        const csvContent = [];
        
        rows.forEach(function(row) {
            const cells = row.querySelectorAll('th, td');
            const rowData = [];
            
            cells.forEach(function(cell) {
                rowData.push('"' + cell.textContent.trim().replace(/"/g, '""') + '"');
            });
            
            csvContent.push(rowData.join(','));
        });

        const csvString = csvContent.join('\n');
        const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        
        link.href = URL.createObjectURL(blob);
        link.download = 'table_export.csv';
        link.click();
        
        showSuccess('تم تصدير الجدول بنجاح');
    }

    function printTable(target) {
        const table = document.querySelector(target);
        if (!table) return;

        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>طباعة الجدول</title>
                    <style>
                        table { border-collapse: collapse; width: 100%; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                        th { background-color: #f2f2f2; }
                    </style>
                </head>
                <body>
                    ${table.outerHTML}
                </body>
            </html>
        `);
        
        printWindow.document.close();
        printWindow.print();
    }

    function bulkDeleteRows() {
        const checkedBoxes = document.querySelectorAll('tr[data-selectable="true"] input[type="checkbox"]:checked');
        
        if (checkedBoxes.length === 0) {
            showWarning('يرجى تحديد صف واحد على الأقل');
            return;
        }

        if (confirm(`هل أنت متأكد من حذف ${checkedBoxes.length} عنصر؟`)) {
            // تنفيذ الحذف
            checkedBoxes.forEach(function(checkbox) {
                const row = checkbox.closest('tr');
                row.remove();
            });
            
            updateBulkActions();
            showSuccess(`تم حذف ${checkedBoxes.length} عنصر بنجاح`);
        }
    }

    function editRow(rowId) {
        window.location.href = `edit.php?id=${rowId}`;
    }

    function deleteRow(rowId, element) {
        if (confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
            const row = element.closest('tr');
            row.remove();
            showSuccess('تم حذف العنصر بنجاح');
        }
    }

    function viewRow(rowId) {
        window.location.href = `view.php?id=${rowId}`;
    }

    // تصدير الوظائف للاستخدام الخارجي
    window.TableComponent = {
        sortTable: sortTable,
        refreshTable: refreshTable,
        exportTable: exportTable
    };

})();
