<?php

namespace App\Modules\Inventory\Models;

use App\Core\Model;

class Warehouse extends Model
{
    protected $table = 'inventory_warehouses';
    protected $primaryKey = 'warehouse_id';
    
    protected $fillable = [
        'company_id',
        'module_code',
        'warehouse_code',
        'warehouse_name_ar',
        'warehouse_name_en',
        'address',
        'phone',
        'email',
        'manager_name',
        'capacity',
        'current_usage',
        'warehouse_type',
        'is_active',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'capacity' => 'decimal:2',
        'current_usage' => 'decimal:2',
        'is_active' => 'boolean'
    ];

    protected $dates = [
        'created_at',
        'updated_at'
    ];

    // العلاقات
    public function company()
    {
        return $this->belongsTo('App\Models\Company', 'company_id', 'CompanyID');
    }

    public function createdBy()
    {
        return $this->belongsTo('App\Models\User', 'created_by', 'UserID');
    }

    public function updatedBy()
    {
        return $this->belongsTo('App\Models\User', 'updated_by', 'UserID');
    }

    public function locations()
    {
        return $this->hasMany('App\Modules\Inventory\Models\Location', 'warehouse_id', 'warehouse_id');
    }

    public function stock()
    {
        return $this->hasMany('App\Modules\Inventory\Models\Stock', 'warehouse_id', 'warehouse_id');
    }

    public function movements()
    {
        return $this->hasMany('App\Modules\Inventory\Models\Movement', 'warehouse_id', 'warehouse_id');
    }

    public function transfers()
    {
        return $this->hasMany('App\Modules\Inventory\Models\Transfer', 'from_warehouse_id', 'warehouse_id');
    }

    public function transfersTo()
    {
        return $this->hasMany('App\Modules\Inventory\Models\Transfer', 'to_warehouse_id', 'warehouse_id');
    }

    public function audits()
    {
        return $this->hasMany('App\Modules\Inventory\Models\Audit', 'warehouse_id', 'warehouse_id');
    }

    public function adjustments()
    {
        return $this->hasMany('App\Modules\Inventory\Models\Adjustment', 'warehouse_id', 'warehouse_id');
    }

    public function alerts()
    {
        return $this->hasMany('App\Modules\Inventory\Models\Alert', 'warehouse_id', 'warehouse_id');
    }

    public function reorderRequests()
    {
        return $this->hasMany('App\Modules\Inventory\Models\ReorderRequest', 'warehouse_id', 'warehouse_id');
    }

    // الدوال المساعدة
    public function getDisplayName()
    {
        return $this->warehouse_name_ar ?: $this->warehouse_name_en;
    }

    public function getUsagePercentage()
    {
        if (!$this->capacity || $this->capacity == 0) {
            return 0;
        }
        return round(($this->current_usage / $this->capacity) * 100, 2);
    }

    public function getAvailableCapacity()
    {
        if (!$this->capacity) {
            return null;
        }
        return $this->capacity - $this->current_usage;
    }

    public function isNearCapacity($threshold = 80)
    {
        return $this->getUsagePercentage() >= $threshold;
    }

    public function isOverCapacity()
    {
        return $this->current_usage > $this->capacity;
    }

    // النطاقات (Scopes)
    public function scopeActive($query)
    {
        return $query->where('is_active', 1);
    }

    public function scopeByCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('warehouse_type', $type);
    }

    public function scopeSearch($query, $search)
    {
        return $query->where(function($q) use ($search) {
            $q->where('warehouse_code', 'LIKE', "%{$search}%")
              ->orWhere('warehouse_name_ar', 'LIKE', "%{$search}%")
              ->orWhere('warehouse_name_en', 'LIKE', "%{$search}%")
              ->orWhere('address', 'LIKE', "%{$search}%");
        });
    }

    // التحقق من الصحة
    public static function getValidationRules($id = null)
    {
        return [
            'warehouse_code' => 'required|string|max:50|unique:inventory_warehouses,warehouse_code,' . $id . ',warehouse_id,company_id,' . session('company_id'),
            'warehouse_name_ar' => 'required|string|max:100',
            'warehouse_name_en' => 'nullable|string|max:100',
            'address' => 'nullable|string|max:500',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:100',
            'manager_name' => 'nullable|string|max:100',
            'capacity' => 'nullable|numeric|min:0',
            'warehouse_type' => 'required|in:main,branch,virtual,external,temporary',
            'is_active' => 'boolean'
        ];
    }

    public static function getValidationMessages()
    {
        return [
            'warehouse_code.required' => 'رمز المستودع مطلوب',
            'warehouse_code.unique' => 'رمز المستودع موجود مسبقاً',
            'warehouse_name_ar.required' => 'اسم المستودع بالعربية مطلوب',
            'warehouse_type.required' => 'نوع المستودع مطلوب',
            'warehouse_type.in' => 'نوع المستودع غير صحيح',
            'capacity.numeric' => 'السعة يجب أن تكون رقماً',
            'capacity.min' => 'السعة يجب أن تكون أكبر من أو تساوي صفر'
        ];
    }

    // الأحداث
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->company_id = session('company_id');
            $model->module_code = 'inventory';
            $model->created_by = auth()->id();
            $model->current_usage = 0.00;
        });

        static::updating(function ($model) {
            $model->updated_by = auth()->id();
        });
    }

    // دوال إضافية للإحصائيات
    public function getTotalProducts()
    {
        return $this->stock()->distinct('product_id')->count();
    }

    public function getTotalStockValue()
    {
        return $this->stock()->sum(\DB::raw('quantity_on_hand * average_cost'));
    }

    public function getLowStockProducts($threshold = null)
    {
        $query = $this->stock()->with('product');
        
        if ($threshold) {
            return $query->whereRaw('quantity_on_hand <= ?', [$threshold])->get();
        }
        
        return $query->whereRaw('quantity_on_hand <= (SELECT reorder_point FROM inventory_products WHERE inventory_products.product_id = inventory_stock.product_id)')->get();
    }

    public function getOutOfStockProducts()
    {
        return $this->stock()->where('quantity_on_hand', '<=', 0)->with('product')->get();
    }
}
