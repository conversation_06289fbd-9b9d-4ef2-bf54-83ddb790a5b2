<?php
namespace App\Modules\Inventory\Models;

use PDO;
use Exception;

/**
 * Warehouse Model - نموذج المستودعات
 */
class Warehouse
{
    /**
     * Database connection
     */
    protected $db;

    /**
     * Table name
     */
    protected $table = 'inventory_warehouses';

    /**
     * Constructor
     */
    public function __construct()
    {
        global $db;
        $this->db = $db;
    }

    /**
     * الحصول على جميع المستودعات للشركة
     */
    public function getByCompany($company_id, $filters = [])
    {
        $sql = "SELECT * FROM {$this->table} WHERE company_id = ?";
        $params = [$company_id];

        // تطبيق الفلاتر
        if (isset($filters['is_active'])) {
            $sql .= " AND is_active = ?";
            $params[] = $filters['is_active'];
        }

        if (!empty($filters['warehouse_type'])) {
            $sql .= " AND warehouse_type = ?";
            $params[] = $filters['warehouse_type'];
        }

        if (!empty($filters['search'])) {
            $sql .= " AND (warehouse_name_ar LIKE ? OR warehouse_name_en LIKE ? OR warehouse_code LIKE ?)";
            $search = '%' . $filters['search'] . '%';
            $params[] = $search;
            $params[] = $search;
            $params[] = $search;
        }

        $sql .= " ORDER BY warehouse_name_ar";

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على مستودع بالمعرف
     */
    public function getById($warehouse_id, $company_id)
    {
        $sql = "SELECT * FROM {$this->table} WHERE warehouse_id = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$warehouse_id, $company_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على مستودع بالكود
     */
    public function getByCode($warehouse_code, $company_id)
    {
        $sql = "SELECT * FROM {$this->table} WHERE warehouse_code = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$warehouse_code, $company_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * إنشاء مستودع جديد
     */
    public function create($data)
    {
        // التحقق من عدم تكرار الكود
        if ($this->getByCode($data['warehouse_code'], $data['company_id'])) {
            throw new Exception('كود المستودع موجود مسبقاً');
        }

        $sql = "INSERT INTO {$this->table} (
                    company_id, module_code, warehouse_code,
                    warehouse_name_ar, warehouse_name_en, address,
                    phone, email, manager_name, capacity, current_usage,
                    warehouse_type, is_active, created_by, created_at
                ) VALUES (
                    ?, 'inventory', ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW()
                )";

        $stmt = $this->db->prepare($sql);
        $result = $stmt->execute([
            $data['company_id'],
            $data['warehouse_code'],
            $data['warehouse_name_ar'],
            $data['warehouse_name_en'] ?: null,
            $data['address'] ?: null,
            $data['phone'] ?: null,
            $data['email'] ?: null,
            $data['manager_name'] ?: null,
            $data['capacity'] ?: null,
            $data['current_usage'] ?: 0.00,
            $data['warehouse_type'],
            $data['is_active'] ?: 1,
            $data['created_by']
        ]);

        return $result ? $this->db->lastInsertId() : false;
    }

    /**
     * تحديث مستودع
     */
    public function update($warehouse_id, $data, $company_id)
    {
        // التحقق من عدم تكرار الكود (إذا تم تغييره)
        if (isset($data['warehouse_code'])) {
            $existing = $this->getByCode($data['warehouse_code'], $company_id);
            if ($existing && $existing['warehouse_id'] != $warehouse_id) {
                throw new Exception('كود المستودع موجود مسبقاً');
            }
        }

        $sql = "UPDATE {$this->table} SET
                    warehouse_code = ?, warehouse_name_ar = ?, warehouse_name_en = ?,
                    address = ?, phone = ?, email = ?, manager_name = ?,
                    capacity = ?, warehouse_type = ?, is_active = ?,
                    updated_by = ?, updated_at = NOW()
                WHERE warehouse_id = ? AND company_id = ?";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            $data['warehouse_code'],
            $data['warehouse_name_ar'],
            $data['warehouse_name_en'] ?: null,
            $data['address'] ?: null,
            $data['phone'] ?: null,
            $data['email'] ?: null,
            $data['manager_name'] ?: null,
            $data['capacity'] ?: null,
            $data['warehouse_type'],
            $data['is_active'] ?: 1,
            $data['updated_by'],
            $warehouse_id,
            $company_id
        ]);
    }

    /**
     * حذف مستودع
     */
    public function delete($warehouse_id, $company_id)
    {
        // التحقق من عدم وجود مخزون في هذا المستودع
        $sql = "SELECT COUNT(*) FROM inventory_stock WHERE warehouse_id = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$warehouse_id, $company_id]);

        if ($stmt->fetchColumn() > 0) {
            throw new Exception('لا يمكن حذف المستودع لوجود مخزون به');
        }

        // التحقق من عدم وجود حركات مخزون
        $sql = "SELECT COUNT(*) FROM inventory_movements WHERE warehouse_id = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$warehouse_id, $company_id]);

        if ($stmt->fetchColumn() > 0) {
            throw new Exception('لا يمكن حذف المستودع لوجود حركات مخزون مرتبطة به');
        }

        // حذف المستودع
        $sql = "DELETE FROM {$this->table} WHERE warehouse_id = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$warehouse_id, $company_id]);
    }

    /**
     * الحصول على المستودعات النشطة
     */
    public function getActiveWarehouses($company_id)
    {
        $sql = "SELECT * FROM {$this->table}
                WHERE company_id = ? AND is_active = 1
                ORDER BY warehouse_name_ar";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على إحصائيات المستودعات
     */
    public function getStats($company_id)
    {
        $stats = [];

        // إجمالي المستودعات
        $sql = "SELECT COUNT(*) FROM {$this->table} WHERE company_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id]);
        $stats['total_warehouses'] = $stmt->fetchColumn();

        // المستودعات النشطة
        $sql = "SELECT COUNT(*) FROM {$this->table} WHERE company_id = ? AND is_active = 1";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id]);
        $stats['active_warehouses'] = $stmt->fetchColumn();

        return $stats;
    }

    /**
     * الحصول على المستودعات مع تفاصيل المخزون
     */
    public function getWarehousesWithStock($company_id)
    {
        $sql = "SELECT w.*,
                       COUNT(DISTINCT s.product_id) as product_count,
                       COALESCE(SUM(s.quantity_on_hand), 0) as total_quantity,
                       COALESCE(SUM(s.quantity_on_hand * s.average_cost), 0) as total_value
                FROM {$this->table} w
                LEFT JOIN inventory_stock s ON w.warehouse_id = s.warehouse_id
                WHERE w.company_id = ? AND w.is_active = 1
                GROUP BY w.warehouse_id
                ORDER BY w.warehouse_name_ar";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * تحديث الاستخدام الحالي للمستودع
     */
    public function updateCurrentUsage($warehouse_id, $company_id)
    {
        // حساب الاستخدام الحالي من المخزون
        $sql = "SELECT COALESCE(SUM(quantity_on_hand), 0) as total_usage
                FROM inventory_stock
                WHERE warehouse_id = ? AND company_id = ?";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$warehouse_id, $company_id]);
        $usage = $stmt->fetchColumn();

        // تحديث الاستخدام الحالي
        $sql = "UPDATE {$this->table} SET current_usage = ? WHERE warehouse_id = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$usage, $warehouse_id, $company_id]);
    }
}