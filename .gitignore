# Environment files
.env
.env.local
.env.production

# Composer
/vendor/
composer.lock

# Node modules
node_modules/
npm-debug.log
yarn-error.log

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
erpapp/storage/logs/*.log
erpapp/error_log
error_log

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# Database dumps (keep only main schema)
*_data_*.sql
*_backup_*.sql
inventory_*.sql

# Cache
erpapp/storage/cache/*
!erpapp/storage/cache/.gitkeep

# Sessions
erpapp/storage/sessions/*
!erpapp/storage/sessions/.gitkeep

# Uploads
erpapp/public/uploads/*
!erpapp/public/uploads/.gitkeep
!erpapp/public/uploads/company_logos/.gitkeep
!erpapp/public/uploads/profile_pictures/.gitkeep
!erpapp/public/uploads/program_icons/.gitkeep

# Mail storage
erpapp/storage/mail/*
!erpapp/storage/mail/.gitkeep

# Temporary files
*.tmp
*.temp

# Backup files
*.bak
*.backup
*.sql.backup

# PHPUnit
phpunit.xml
.phpunit.result.cache

# Coverage reports
coverage/
clover.xml

# PHPStan
phpstan.neon
.phpstan.cache

# Build files
/build/
/dist/
