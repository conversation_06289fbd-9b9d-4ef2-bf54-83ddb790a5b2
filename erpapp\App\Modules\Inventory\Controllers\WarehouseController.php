<?php
namespace App\Modules\Inventory\Controllers;

use App\Modules\Inventory\Models\Warehouse;
use App\Modules\Inventory\Services\WarehouseService;
use Exception;
use PDO;

/**
 * Warehouse Controller - متحكم المستودعات
 */
class WarehouseController
{
    /**
     * Route parameters
     */
    protected $params = [];

    /**
     * Warehouse model
     */
    protected $warehouseModel;

    /**
     * Warehouse service
     */
    protected $warehouseService;

    /**
     * Constructor
     */
    public function __construct($params = [])
    {
        $this->params = $params;
        $this->warehouseModel = new Warehouse();

        // التحقق من تسجيل الدخول
        if (!is_logged_in()) {
            redirect(base_url('login'));
        }

        // التحقق من وجود شركة حالية
        $user = current_user();
        if (!$user || !$user['current_company_id']) {
            flash('warehouse_error', 'يجب تحديد شركة حالية للوصول إلى المستودعات', 'warning');
            redirect(base_url('companies'));
        }
    }

    /**
     * عرض قائمة المستودعات
     */
    public function index()
    {
        try {
            $user = current_user();
            $company_id = $user['current_company_id'];

            // الحصول على المستودعات مع تفاصيل المخزون
            $warehouses = $this->warehouseModel->getWarehousesWithStock($company_id);

            // الحصول على إحصائيات المستودعات
            $stats = $this->warehouseModel->getStats($company_id);

            // الحصول على معلومات الشركة
            global $db;
            $stmt = $db->prepare("SELECT CompanyName, CompanyNameEN FROM companies WHERE CompanyID = ?");
            $stmt->execute([$company_id]);
            $company = $stmt->fetch(PDO::FETCH_ASSOC);

            $data = [
                'title' => 'إدارة المستودعات',
                'warehouses' => $warehouses,
                'stats' => $stats,
                'company' => $company,
                'company_id' => $company_id,
                'breadcrumb' => [
                    ['title' => 'المخزون', 'url' => base_url('inventory')],
                    ['title' => 'المستودعات', 'active' => true]
                ]
            ];

            view('Inventory::warehouses/index', $data);

        } catch (Exception $e) {
            flash('warehouse_error', 'حدث خطأ أثناء تحميل المستودعات: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory'));
        }
    }

    /**
     * عرض نموذج إضافة مستودع جديد
     */
    public function create()
    {
        try {
            $user = current_user();
            $company_id = $user['current_company_id'];

            // الحصول على معلومات الشركة
            global $db;
            $stmt = $db->prepare("SELECT CompanyName, CompanyNameEN FROM companies WHERE CompanyID = ?");
            $stmt->execute([$company_id]);
            $company = $stmt->fetch(PDO::FETCH_ASSOC);

            $data = [
                'title' => 'إضافة مستودع جديد',
                'company' => $company,
                'company_id' => $company_id,
                'breadcrumb' => [
                    ['title' => 'المخزون', 'url' => base_url('inventory')],
                    ['title' => 'المستودعات', 'url' => base_url('inventory/warehouses')],
                    ['title' => 'إضافة مستودع', 'active' => true]
                ]
            ];

            view('Inventory::warehouses/create', $data);

        } catch (Exception $e) {
            flash('warehouse_error', 'حدث خطأ أثناء تحميل النموذج: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory/warehouses'));
        }
    }

    /**
     * حفظ مستودع جديد
     */
    public function store()
    {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                redirect(base_url('inventory/warehouses/create'));
            }

            $company_id = current_user()['current_company_id'];
            $user_id = current_user_id();

            // التحقق من البيانات
            $warehouseData = $this->validateWarehouseData($_POST);
            $warehouseData['company_id'] = $company_id;
            $warehouseData['created_by'] = $user_id;

            // إنشاء المستودع
            $warehouse_id = $this->warehouseModel->create($warehouseData);

            if ($warehouse_id) {
                flash('warehouse_success', 'تم إضافة المستودع بنجاح', 'success');
                redirect(base_url('inventory/warehouses'));
            } else {
                flash('warehouse_error', 'حدث خطأ أثناء إضافة المستودع', 'danger');
                redirect(base_url('inventory/warehouses/create'));
            }

        } catch (Exception $e) {
            flash('warehouse_error', 'حدث خطأ: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory/warehouses/create'));
        }
    }

    /**
     * عرض تفاصيل مستودع
     */
    public function show()
    {
        try {
            $warehouse_id = $this->params['id'] ?? null;
            $company_id = current_user()['current_company_id'];

            if (!$warehouse_id) {
                flash('warehouse_error', 'المستودع غير موجود', 'danger');
                redirect(base_url('inventory/warehouses'));
            }

            $warehouse = $this->warehouseModel->getById($warehouse_id, $company_id);

            if (!$warehouse) {
                flash('warehouse_error', 'المستودع غير موجود', 'danger');
                redirect(base_url('inventory/warehouses'));
            }

            // الحصول على معلومات الشركة
            global $db;
            $stmt = $db->prepare("SELECT CompanyName, CompanyNameEN FROM companies WHERE CompanyID = ?");
            $stmt->execute([$company_id]);
            $company = $stmt->fetch(PDO::FETCH_ASSOC);

            $data = [
                'title' => $warehouse['warehouse_name_ar'],
                'warehouse' => $warehouse,
                'company' => $company,
                'company_id' => $company_id,
                'breadcrumb' => [
                    ['title' => 'المخزون', 'url' => base_url('inventory')],
                    ['title' => 'المستودعات', 'url' => base_url('inventory/warehouses')],
                    ['title' => $warehouse['warehouse_name_ar'], 'active' => true]
                ]
            ];

            view('Inventory::warehouses/view', $data);

        } catch (Exception $e) {
            flash('warehouse_error', 'حدث خطأ: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory/warehouses'));
        }
    }

    /**
     * عرض نموذج تعديل مستودع
     */
    public function edit()
    {
        try {
            $warehouse_id = $this->params['id'] ?? null;
            $company_id = current_user()['current_company_id'];

            if (!$warehouse_id) {
                flash('warehouse_error', 'المستودع غير موجود', 'danger');
                redirect(base_url('inventory/warehouses'));
            }

            $warehouse = $this->warehouseModel->getById($warehouse_id, $company_id);

            if (!$warehouse) {
                flash('warehouse_error', 'المستودع غير موجود', 'danger');
                redirect(base_url('inventory/warehouses'));
            }

            // الحصول على معلومات الشركة
            global $db;
            $stmt = $db->prepare("SELECT CompanyName, CompanyNameEN FROM companies WHERE CompanyID = ?");
            $stmt->execute([$company_id]);
            $company = $stmt->fetch(PDO::FETCH_ASSOC);

            $data = [
                'title' => 'تعديل المستودع - ' . $warehouse['warehouse_name_ar'],
                'warehouse' => $warehouse,
                'company' => $company,
                'company_id' => $company_id,
                'breadcrumb' => [
                    ['title' => 'المخزون', 'url' => base_url('inventory')],
                    ['title' => 'المستودعات', 'url' => base_url('inventory/warehouses')],
                    ['title' => 'تعديل المستودع', 'active' => true]
                ]
            ];

            view('Inventory::warehouses/edit', $data);

        } catch (Exception $e) {
            flash('warehouse_error', 'حدث خطأ: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory/warehouses'));
        }
    }

    /**
     * تحديث مستودع
     */
    public function update()
    {
        try {
            $warehouse_id = $this->params['id'] ?? null;
            $company_id = current_user()['current_company_id'];
            $user_id = current_user_id();

            if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !$warehouse_id) {
                redirect(base_url('inventory/warehouses'));
            }

            // التحقق من وجود المستودع
            $warehouse = $this->warehouseModel->getById($warehouse_id, $company_id);
            if (!$warehouse) {
                flash('warehouse_error', 'المستودع غير موجود', 'danger');
                redirect(base_url('inventory/warehouses'));
            }

            // التحقق من البيانات
            $warehouseData = $this->validateWarehouseData($_POST);
            $warehouseData['updated_by'] = $user_id;

            // تحديث المستودع
            if ($this->warehouseModel->update($warehouse_id, $warehouseData, $company_id)) {
                flash('warehouse_success', 'تم تحديث المستودع بنجاح', 'success');
                redirect(base_url('inventory/warehouses/' . $warehouse_id));
            } else {
                flash('warehouse_error', 'حدث خطأ أثناء تحديث المستودع', 'danger');
                redirect(base_url('inventory/warehouses/' . $warehouse_id . '/edit'));
            }

        } catch (Exception $e) {
            flash('warehouse_error', 'حدث خطأ: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory/warehouses'));
        }
    }

    /**
     * حذف مستودع
     */
    public function delete()
    {
        try {
            $warehouse_id = $this->params['id'] ?? null;
            $company_id = current_user()['current_company_id'];

            if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !$warehouse_id) {
                redirect(base_url('inventory/warehouses'));
            }

            // التحقق من وجود المستودع
            $warehouse = $this->warehouseModel->getById($warehouse_id, $company_id);
            if (!$warehouse) {
                flash('warehouse_error', 'المستودع غير موجود', 'danger');
                redirect(base_url('inventory/warehouses'));
            }

            // حذف المستودع
            if ($this->warehouseModel->delete($warehouse_id, $company_id)) {
                flash('warehouse_success', 'تم حذف المستودع بنجاح', 'success');
            } else {
                flash('warehouse_error', 'حدث خطأ أثناء حذف المستودع', 'danger');
            }

            redirect(base_url('inventory/warehouses'));

        } catch (Exception $e) {
            flash('warehouse_error', 'حدث خطأ: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory/warehouses'));
        }
    }

    /**
     * التحقق من صحة بيانات المستودع
     */
    private function validateWarehouseData($data)
    {
        $errors = [];

        // التحقق من الحقول المطلوبة
        if (empty($data['warehouse_code'])) {
            $errors[] = 'كود المستودع مطلوب';
        }

        if (empty($data['warehouse_name_ar'])) {
            $errors[] = 'اسم المستودع باللغة العربية مطلوب';
        }

        if (empty($data['warehouse_type'])) {
            $errors[] = 'نوع المستودع مطلوب';
        }

        if (!empty($errors)) {
            throw new Exception(implode(', ', $errors));
        }

        // إعداد البيانات
        return [
            'warehouse_code' => trim($data['warehouse_code']),
            'warehouse_name_ar' => trim($data['warehouse_name_ar']),
            'warehouse_name_en' => trim($data['warehouse_name_en'] ?? ''),
            'address' => trim($data['address'] ?? ''),
            'phone' => trim($data['phone'] ?? ''),
            'email' => trim($data['email'] ?? ''),
            'manager_name' => trim($data['manager_name'] ?? ''),
            'capacity' => !empty($data['capacity']) ? (float)$data['capacity'] : null,
            'current_usage' => 0.00, // تعيين قيمة افتراضية للاستخدام الحالي
            'warehouse_type' => $data['warehouse_type'],
            'is_active' => isset($data['is_active']) ? 1 : 0
        ];
    }
}