<?php
/**
 * Component موحد لجدول البيانات
 */
?>

<table class="table table-centered table-nowrap mb-0">
    <thead class="table-light">
        <tr>
            <?php foreach ($columns as $column): ?>
                <th <?= isset($column['width']) ? 'style="width: ' . $column['width'] . '"' : '' ?>>
                    <?= $column['title'] ?>
                    <?php if (isset($column['sortable']) && $column['sortable']): ?>
                        <i class="mdi mdi-sort ms-1 text-muted"></i>
                    <?php endif; ?>
                </th>
            <?php endforeach; ?>
        </tr>
    </thead>
    <tbody>
        <?php if (empty($data)): ?>
            <tr>
                <td colspan="<?= count($columns) ?>" class="text-center py-4">
                    <div class="empty-state">
                        <?php if (isset($empty_state['icon'])): ?>
                            <i class="<?= $empty_state['icon'] ?>"></i>
                        <?php else: ?>
                            <i class="mdi mdi-database-outline"></i>
                        <?php endif; ?>
                        <p>
                            <?= $empty_state['message'] ?? 'لا توجد بيانات' ?>
                        </p>
                        <?php if (isset($empty_state['action'])): ?>
                            <a href="<?= base_url($empty_state['action']['url']) ?>" 
                               class="btn btn-primary btn-sm">
                                <?= $empty_state['action']['text'] ?>
                            </a>
                        <?php endif; ?>
                    </div>
                </td>
            </tr>
        <?php else: ?>
            <?php foreach ($data as $row): ?>
                <tr>
                    <?php foreach ($columns as $column): ?>
                        <td>
                            <?php
                            $field = $column['field'];
                            $value = $row[$field] ?? '';
                            
                            // تطبيق التنسيق حسب نوع العمود
                            switch ($column['type'] ?? 'text') {
                                case 'link':
                                    $url = str_replace('{id}', $row[$column['id_field'] ?? 'id'], $column['url']);
                                    echo '<a href="' . base_url($url) . '" class="text-body fw-bold">';
                                    echo htmlspecialchars($value);
                                    echo '</a>';
                                    if (isset($column['subtitle_field']) && !empty($row[$column['subtitle_field']])) {
                                        echo '<br><small class="text-muted">' . htmlspecialchars($row[$column['subtitle_field']]) . '</small>';
                                    }
                                    break;
                                    
                                case 'badge':
                                    if (isset($column['badge_classes'][$value])) {
                                        $badge_class = $column['badge_classes'][$value];
                                        $badge_text = $column['badge_texts'][$value] ?? $value;
                                    } else {
                                        // للحالات الديناميكية مثل group_name_ar
                                        $badge_class = $column['badge_classes']['default'] ?? 'secondary';
                                        $badge_text = $value;
                                    }
                                    if (!empty($badge_text)) {
                                        echo '<span class="badge bg-' . $badge_class . '">' . htmlspecialchars($badge_text) . '</span>';
                                    } else {
                                        echo '<span class="text-muted">-</span>';
                                    }
                                    break;
                                    
                                case 'number':
                                    echo number_format($value);
                                    break;
                                    
                                case 'currency':
                                    $currency = $column['currency'] ?? 'ريال';
                                    echo number_format($value, 2) . ' ' . $currency;
                                    break;
                                    
                                case 'date':
                                    if ($value) {
                                        $format = $column['format'] ?? 'Y-m-d';
                                        echo date($format, strtotime($value));
                                    } else {
                                        echo '-';
                                    }
                                    break;
                                    
                                case 'actions':
                                    echo '<div class="btn-group" role="group">';
                                    foreach ($column['buttons'] as $button) {
                                        $url = str_replace('{id}', $row[$button['id_field'] ?? 'id'], $button['url']);
                                        $class = $button['class'] ?? 'btn-primary';
                                        $icon = $button['icon'] ?? '';
                                        $title = $button['title'] ?? '';
                                        $onclick = isset($button['onclick']) ? 
                                            str_replace('{id}', $row[$button['id_field'] ?? 'id'], $button['onclick']) : '';
                                        
                                        if ($button['type'] === 'link') {
                                            echo '<a href="' . base_url($url) . '" class="btn ' . $class . ' btn-sm" title="' . $title . '">';
                                            echo '<i class="' . $icon . '"></i>';
                                            echo '</a>';
                                        } elseif ($button['type'] === 'button') {
                                            echo '<button type="button" class="btn ' . $class . ' btn-sm" title="' . $title . '"';
                                            if ($onclick) echo ' onclick="' . $onclick . '"';
                                            echo '>';
                                            echo '<i class="' . $icon . '"></i>';
                                            echo '</button>';
                                        }
                                    }
                                    echo '</div>';
                                    break;
                                    
                                case 'custom':
                                    // تنفيذ دالة مخصصة
                                    if (isset($column['render_function']) && function_exists($column['render_function'])) {
                                        echo call_user_func($column['render_function'], $value, $row, $column);
                                    } else {
                                        echo htmlspecialchars($value);
                                    }
                                    break;
                                    
                                default: // text
                                    if (empty($value)) {
                                        echo '<span class="text-muted">-</span>';
                                    } else {
                                        echo htmlspecialchars($value);
                                    }
                                    break;
                            }
                            ?>
                        </td>
                    <?php endforeach; ?>
                </tr>
            <?php endforeach; ?>
        <?php endif; ?>
    </tbody>
</table>
