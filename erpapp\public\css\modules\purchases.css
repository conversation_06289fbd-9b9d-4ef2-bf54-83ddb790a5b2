/**
 * Purchases Module - Professional Styling
 * وحدة المشتريات - تصميم احترافي
 */

/* ===== PAGE LAYOUT ===== */
.purchases-page {
    background: var(--light-bg-color);
    min-height: 100vh;
    padding: var(--spacing-6);
    transition: all var(--transition-normal) var(--transition-bezier);
}

.purchases-container {
    max-width: 100%;
    margin: 0 auto;
}

/* ===== PAGE HEADER ===== */
.page-header {
    background: var(--light-card-bg);
    border: 1px solid var(--light-card-border);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-6);
    margin-bottom: var(--spacing-6);
    box-shadow: var(--box-shadow-sm);
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.page-title {
    color: var(--light-text-color);
    font-size: 1.875rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.page-title i {
    color: var(--primary-color);
    font-size: 2rem;
}

.page-subtitle {
    color: var(--light-text-muted);
    font-size: 1rem;
    margin: var(--spacing-2) 0 0 0;
    font-weight: 400;
}

/* ===== BREADCRUMB ===== */
.breadcrumb-container {
    margin-bottom: var(--spacing-4);
}

.breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
    font-size: 0.875rem;
}

.breadcrumb-item {
    color: var(--light-text-muted);
}

.breadcrumb-item.active {
    color: var(--primary-color);
    font-weight: 500;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: var(--gray-400);
    font-weight: 600;
}

/* ===== STATS CARDS ===== */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-8);
}

.stat-card {
    background: var(--light-card-bg);
    border: 1px solid var(--light-card-border);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-6);
    box-shadow: var(--box-shadow-sm);
    transition: all var(--transition-normal) var(--transition-bezier);
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-md);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
}

.stat-card.success::before {
    background: var(--success-gradient);
}

.stat-card.warning::before {
    background: var(--warning-gradient);
}

.stat-card.danger::before {
    background: var(--danger-gradient);
}

.stat-card.info::before {
    background: var(--info-gradient);
}

.stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-4);
}

.stat-title {
    color: var(--light-text-muted);
    font-size: 0.875rem;
    font-weight: 500;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: var(--light-card-bg);
}

.stat-icon.primary {
    background: var(--primary-gradient);
}

.stat-icon.success {
    background: var(--success-gradient);
}

.stat-icon.warning {
    background: var(--warning-gradient);
}

.stat-icon.danger {
    background: var(--danger-gradient);
}

.stat-icon.info {
    background: var(--info-gradient);
}

.stat-value {
    color: var(--light-text-color);
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    line-height: 1;
}

.stat-change {
    font-size: 0.75rem;
    font-weight: 500;
    margin-top: var(--spacing-2);
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--danger-color);
}

.stat-change.neutral {
    color: var(--light-text-muted);
}

/* ===== MAIN CONTENT CARD ===== */
.content-card {
    background: var(--light-card-bg);
    border: 1px solid var(--light-card-border);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-sm);
    overflow: hidden;
}

.content-header {
    padding: var(--spacing-6);
    border-bottom: 1px solid var(--light-card-border);
    background: linear-gradient(135deg, var(--gray-100), var(--light-card-bg));
}

.content-title {
    color: var(--light-text-color);
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.content-title i {
    color: var(--primary-color);
}

.content-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    margin-top: var(--spacing-4);
}

/* ===== TOOLBAR ===== */
.datatable-toolbar {
    padding: var(--spacing-5) var(--spacing-6);
    border-bottom: 1px solid var(--light-card-border);
    background: var(--gray-100);
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--spacing-4);
}

.toolbar-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    flex: 1;
}

.toolbar-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

/* ===== BUTTONS ===== */
.btn-primary {
    background: var(--primary-gradient);
    border: none;
    color: var(--light-card-bg);
    font-weight: 500;
    padding: var(--spacing-3) var(--spacing-5);
    border-radius: var(--border-radius);
    transition: all var(--transition-normal) var(--transition-bezier);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-2);
    text-decoration: none;
    box-shadow: var(--box-shadow-sm);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--box-shadow-md);
    color: var(--light-card-bg);
}

.btn-outline-primary {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    font-weight: 500;
    padding: calc(var(--spacing-3) - 2px) calc(var(--spacing-5) - 2px);
    border-radius: var(--border-radius);
    transition: all var(--transition-normal) var(--transition-bezier);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-2);
    text-decoration: none;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: var(--light-card-bg);
    transform: translateY(-1px);
    box-shadow: var(--box-shadow-sm);
}

.btn-success {
    background: var(--success-gradient);
    border: none;
    color: var(--light-card-bg);
}

.btn-warning {
    background: var(--warning-gradient);
    border: none;
    color: var(--light-card-bg);
}

.btn-danger {
    background: var(--danger-gradient);
    border: none;
    color: var(--light-card-bg);
}

.btn-info {
    background: var(--info-gradient);
    border: none;
    color: var(--light-card-bg);
}

/* ===== FILTER BUTTON ===== */
.filter-btn {
    position: relative;
    background: var(--light-card-bg);
    border: 2px solid var(--light-card-border);
    color: var(--light-text-color);
    padding: var(--spacing-3) var(--spacing-5);
    border-radius: var(--border-radius);
    transition: all var(--transition-normal) var(--transition-bezier);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-2);
    font-weight: 500;
}

.filter-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: var(--light-hover-bg);
}

.filter-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--light-card-bg);
}

.filter-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--danger-color);
    color: var(--light-card-bg);
    font-size: 0.75rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: var(--border-radius-full);
    min-width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--box-shadow-sm);
}

/* ===== CLEAN TABLE DESIGN (مثل الصورة) ===== */
.table-responsive {
    border-radius: 0;
    box-shadow: none;
    overflow: hidden;
    background: #ffffff;
}

.table {
    margin: 0;
    background: #ffffff;
    border: none;
    font-size: 14px;
}

.table th {
    background: #f8f9fa;
    border: none;
    border-bottom: 1px solid #e9ecef;
    font-weight: 500;
    color: #6c757d;
    font-size: 13px;
    text-transform: none;
    letter-spacing: 0;
    padding: 16px 20px;
    position: relative;
    white-space: nowrap;
}

.table th[data-sortable="true"] {
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s ease;
}

.table th[data-sortable="true"]:hover {
    background: #f1f3f4;
    color: #495057;
}

.table td {
    vertical-align: middle;
    padding: 16px 20px;
    border: none;
    border-bottom: 1px solid #f1f3f4;
    color: #495057;
    font-size: 14px;
    white-space: nowrap;
}

.table tbody tr {
    transition: background-color 0.2s ease;
}

.table tbody tr:hover {
    background: #f8f9fa;
}

/* ===== SMART SORT ICONS (إظهار/إخفاء ذكي) ===== */
.sort-icon {
    margin-left: 8px;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    display: inline-block;
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
}

.sort-icon i {
    font-size: 12px;
    color: #6c757d;
    transition: color 0.2s ease;
}

/* إظهار الأيقونة عند التمرير على العمود */
th[data-sortable="true"]:hover .sort-icon {
    opacity: 1;
    visibility: visible;
}

/* تغيير لون الأيقونة عند التمرير */
th[data-sortable="true"]:hover .sort-icon i {
    color: #495057;
}

/* الأيقونة النشطة (عند الفرز) - تبقى ظاهرة دائماً */
th.sorted-asc .sort-icon,
th.sorted-desc .sort-icon {
    opacity: 1 !important;
    visibility: visible !important;
}

/* لون الأيقونة النشطة */
th.sorted-asc .sort-icon i,
th.sorted-desc .sort-icon i {
    color: #007bff !important;
}

/* تحسين موضع العمود القابل للفرز */
th[data-sortable="true"] {
    position: relative;
    padding-right: 40px;
}

/* إخفاء النصوص الافتراضية */
.sort-icon .text-muted {
    color: transparent !important;
}

.sort-icon .text-primary {
    color: #007bff !important;
}

/* تأثير خفيف عند التمرير على العمود */
th[data-sortable="true"]:hover {
    background: #f1f3f4;
    transition: background-color 0.2s ease;
}

/* ===== CLEAN BADGES (مثل الصورة) ===== */
.badge {
    font-size: 12px;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 4px;
    text-transform: none;
    letter-spacing: 0;
    border: none;
}

.badge.bg-success {
    background: #d4edda !important;
    color: #155724 !important;
}

.badge.bg-warning {
    background: #fff3cd !important;
    color: #856404 !important;
}

.badge.bg-danger {
    background: #f8d7da !important;
    color: #721c24 !important;
}

.badge.bg-info {
    background: #d1ecf1 !important;
    color: #0c5460 !important;
}

.badge.bg-secondary {
    background: #e2e3e5 !important;
    color: #383d41 !important;
}

/* حالات خاصة للجدول */
.table .badge {
    font-size: 11px;
    padding: 3px 6px;
}

/* تحسين الروابط */
.table a {
    color: #007bff;
    text-decoration: none;
    font-weight: 500;
}

.table a:hover {
    color: #0056b3;
    text-decoration: underline;
}

/* تحسين الأرقام */
.table .text-end {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 500;
}

/* تحسين التواريخ */
.table .text-muted {
    color: #6c757d !important;
    font-size: 13px;
}

.text-sm-end {
    float: left !important;
}
/* ===== CLEAN PAGINATION (مثل الصورة) ===== */
.pagination {
    margin: 0;
    gap: 4px;
    justify-content: center;
}

.page-link {
    color: #6c757d;
    border: 1px solid #dee2e6;
    padding: 8px 12px;
    border-radius: 4px;
    transition: all 0.2s ease;
    margin: 0;
    font-size: 14px;
    font-weight: 400;
    background: #ffffff;
}

.page-link:hover {
    color: #495057;
    background: #f8f9fa;
    border-color: #dee2e6;
}

.page-item.active .page-link {
    background: #007bff;
    border-color: #007bff;
    color: #ffffff;
    font-weight: 500;
}

.page-item.disabled .page-link {
    color: #adb5bd;
    background: #ffffff;
    border-color: #dee2e6;
    cursor: not-allowed;
}

/* تحسين أزرار التنقل */
.page-link[aria-label="Previous"],
.page-link[aria-label="Next"] {
    padding: 8px 10px;
}

/* تحسين النص السفلي */
.datatable-info {
    color: #6c757d;
    font-size: 13px;
    margin-top: 16px;
    text-align: center;
}

.datatable-info strong {
    color: #495057;
    font-weight: 500;
}

/* ===== FORMS & INPUTS ===== */
.form-control,
.form-select {
 
    border-radius: var(--border-radius);
    padding: var(--spacing-3) var(--spacing-4);
    transition: all var(--transition-normal) var(--transition-bezier);
    background: var(--light-input-bg);
    color: var(--light-text-color);
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    outline: none;
}

.form-label {
    color: var(--light-text-color);
    font-weight: 500;
    margin-bottom: var(--spacing-2);
}



/* ===== EMPTY STATE ===== */
.empty-state {
    text-align: center;
    padding: var(--spacing-16) var(--spacing-8);
    color: var(--light-text-muted);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: var(--spacing-4);
    opacity: 0.5;
    color: var(--gray-400);
}

.empty-state h5 {
    color: var(--light-text-color);
    margin-bottom: var(--spacing-2);
}

.empty-state p {
    color: var(--light-text-muted);
    margin-bottom: var(--spacing-4);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .purchases-page {
        padding: var(--spacing-4);
    }

    .page-header {
        padding: var(--spacing-4);
        margin-bottom: var(--spacing-4);
    }

    .page-title {
        font-size: 1.5rem;
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-2);
    }

    .stats-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
        margin-bottom: var(--spacing-6);
    }

    .datatable-toolbar {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-3);
    }

    .toolbar-left,
    .toolbar-right {
        justify-content: center;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .table th,
    .table td {
        padding: var(--spacing-3);
    }

    .btn-group {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-2);
    }

    .btn-group .btn {
        width: 100%;
    }
}

/* ===== RTL SUPPORT ===== */
[dir="rtl"] .sort-icon {
    margin-left: 0;
    margin-right: var(--spacing-2);
}

[dir="rtl"] .breadcrumb-item + .breadcrumb-item::before {
    content: "‹";
}

[dir="rtl"] .filter-badge {
    right: auto;
    left: -8px;
}

/* ===== ANIMATIONS ===== */
.fade-in {
    animation: fadeIn var(--transition-normal) var(--transition-bezier);
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn var(--transition-normal) var(--transition-bezier);
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ===== LOADING STATES ===== */
.table-loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.table-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--gray-200);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
