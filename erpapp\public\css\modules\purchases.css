/**
 * CSS خاص بوحدة المشتريات
 */

/* تحسينات عامة للجداول */
.table-responsive {
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table td {
    vertical-align: middle;
    padding: 0.75rem;
    border-bottom: 1px solid #dee2e6;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transition: background-color 0.15s ease-in-out;
}

/* تحسينات الأزرار */
.btn-group .btn {
    margin-right: 0.25rem;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.25rem;
}

/* تحسينات البطاقات الإحصائية */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* تحسينات الفلاتر */
.modal-header.bg-light {
    border-bottom: 1px solid #dee2e6;
}

.modal-footer.bg-light {
    border-top: 1px solid #dee2e6;
}

/* تحسينات Pagination */
.pagination {
    margin-bottom: 0;
}

.page-link {
    color: #6c757d;
    border: 1px solid #dee2e6;
    padding: 0.5rem 0.75rem;
}

.page-link:hover {
    color: #495057;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
    color: #fff;
}

/* تحسينات Empty State */
.text-muted i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* تحسينات Breadcrumb */
.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-bottom: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "/";
    color: #6c757d;
}

/* تحسينات الشارات */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
}

/* تحسينات النماذج */
.form-control:focus,
.form-select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-group {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .btn-group .btn {
        margin-right: 0;
        width: 100%;
    }
    
    .page-title-box {
        text-align: center;
    }
    
    .breadcrumb {
        justify-content: center;
    }
}

/* تحسينات RTL */
[dir="rtl"] .btn-group .btn {
    margin-right: 0;
    margin-left: 0.25rem;
}

[dir="rtl"] .btn-group .btn:last-child {
    margin-left: 0;
}

[dir="rtl"] .breadcrumb-item + .breadcrumb-item::before {
    content: "\\";
}

/* تحسينات الألوان */
.text-primary { color: #007bff !important; }
.text-success { color: #28a745 !important; }
.text-warning { color: #ffc107 !important; }
.text-danger { color: #dc3545 !important; }
.text-info { color: #17a2b8 !important; }

.bg-primary { background-color: #007bff !important; }
.bg-success { background-color: #28a745 !important; }
.bg-warning { background-color: #ffc107 !important; }
.bg-danger { background-color: #dc3545 !important; }
.bg-info { background-color: #17a2b8 !important; }
.bg-secondary { background-color: #6c757d !important; }

/* تحسينات الأيقونات */
.mdi {
    font-size: 1rem;
    vertical-align: middle;
}

.mdi-48px {
    font-size: 3rem;
}

/* تحسينات التحميل */
.table-loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
}

/* تحسينات الانتقالات */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسينات إضافية للمظهر */
.card-body {
    padding: 1.5rem;
}

.page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0;
}

.page-title-box {
    padding-bottom: 1.5rem;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid #dee2e6;
}
