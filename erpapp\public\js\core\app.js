/**
 * التطبيق الأساسي - وظائف عامة ومشتركة
 * Core Application - Common functions and utilities
 */

(function() {
    'use strict';

    // انتظار تحميل DOM
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initApp);
    } else {
        initApp();
    }

    function initApp() {
        console.log('🚀 بدء تهيئة التطبيق الأساسي...');

        // تهيئة المكونات الأساسية
        initThemeSystem();
        initLanguageSystem();
        initSidebarSystem();
        initScrollSystem();
        initKeyboardShortcuts();
        initDropdownSystem();
        initSidebarTooltips();
        initSidebarCollapse();
        initAjaxHelpers();
        initToastrConfig();

        console.log('✅ تم تهيئة التطبيق الأساسي بنجاح');
    }

    // ===== نظام الثيم ===== //
    function initThemeSystem() {
        const themeToggles = document.querySelectorAll('[data-toggle="theme"]');

        themeToggles.forEach(function(toggle) {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                const theme = this.getAttribute('data-theme');
                const url = window.APP_CONFIG.APP_URL + '/settings/theme/' + theme;

                // تطبيق التغيير فوراً
                if (theme === 'dark') {
                    document.body.classList.add('dark-theme');
                    this.setAttribute('data-theme', 'light');
                    this.innerHTML = '<i class="fas fa-sun me-2"></i> الوضع الفاتح';
                } else {
                    document.body.classList.remove('dark-theme');
                    this.setAttribute('data-theme', 'dark');
                    this.innerHTML = '<i class="fas fa-moon me-2"></i> الوضع الداكن';
                }

                // تحديث الإعدادات في قاعدة البيانات
                sendAjaxRequest(url);
            });
        });
    }

    // ===== نظام اللغة ===== //
    function initLanguageSystem() {
        const languageSwitches = document.querySelectorAll('.language-switch');
        
        languageSwitches.forEach(function(langSwitch) {
            langSwitch.addEventListener('click', function(e) {
                const lang = this.getAttribute('data-lang');
                
                // تطبيق التغييرات فوراً
                if (lang === 'ar') {
                    document.body.classList.add('rtl');
                    document.documentElement.setAttribute('dir', 'rtl');
                } else {
                    document.body.classList.remove('rtl');
                    document.documentElement.setAttribute('dir', 'ltr');
                }
            });
        });
    }

    // ===== نظام الشريط الجانبي ===== //
    function initSidebarSystem() {
        const sidebar = document.getElementById('sidebar');
        const content = document.getElementById('content');
        const sidebarToggle = document.getElementById('sidebarToggle');
        const mobileSidebarToggle = document.getElementById('mobileSidebarToggle');
        const mobileSidebarClose = document.getElementById('mobileSidebarClose');
        const sidebarBackdrop = document.getElementById('sidebarBackdrop');

        // تبديل الشريط الجانبي على الديسكتوب
        if (sidebarToggle && sidebar && content) {
            sidebarToggle.addEventListener('click', function(e) {
                e.preventDefault();

                // تبديل حالة القائمة الجانبية
                sidebar.classList.toggle('collapsed');
                content.classList.toggle('expanded');

                // تحديث الإعدادات في قاعدة البيانات
                const newMode = sidebar.classList.contains('collapsed') ? 'hide' : 'show';
                const url = window.APP_CONFIG.APP_URL + '/settings/sidebar/' + newMode;
                sendAjaxRequest(url);
            });
        }

        // تبديل الشريط الجانبي على الموبايل
        if (mobileSidebarToggle && sidebar && sidebarBackdrop) {
            // فتح السايدبار عند النقر على زر التبديل
            mobileSidebarToggle.addEventListener('click', function(e) {
                e.preventDefault();
                sidebar.classList.add('mobile-show');
                sidebarBackdrop.classList.add('show');
                document.body.style.overflow = 'hidden'; // منع التمرير في الصفحة
            });

            // إغلاق السايدبار عند النقر على زر الإغلاق
            if (mobileSidebarClose) {
                mobileSidebarClose.addEventListener('click', function(e) {
                    e.preventDefault();
                    sidebar.classList.remove('mobile-show');
                    sidebarBackdrop.classList.remove('show');
                    document.body.style.overflow = ''; // السماح بالتمرير مرة أخرى
                });
            }

            // إغلاق السايدبار عند النقر على الخلفية
            sidebarBackdrop.addEventListener('click', function() {
                sidebar.classList.remove('mobile-show');
                sidebarBackdrop.classList.remove('show');
                document.body.style.overflow = ''; // السماح بالتمرير مرة أخرى
            });
        }

        // تبديل وضع القائمة الجانبية من القائمة المنسدلة
        const sidebarModeToggles = document.querySelectorAll('[data-toggle="sidebar-mode"]');
        sidebarModeToggles.forEach(function(toggle) {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                const mode = this.getAttribute('data-mode');
                const url = window.APP_CONFIG.APP_URL + '/settings/sidebar/' + mode;

                // تطبيق التغيير فوراً
                if (mode === 'hide') {
                    sidebar.classList.add('collapsed');
                    content.classList.add('expanded');
                    this.setAttribute('data-mode', 'show');
                    this.innerHTML = '<i class="fas fa-angle-double-right me-2"></i> توسيع القائمة الجانبية';
                } else {
                    sidebar.classList.remove('collapsed');
                    content.classList.remove('expanded');
                    this.setAttribute('data-mode', 'hide');
                    this.innerHTML = '<i class="fas fa-angle-double-left me-2"></i> تصغير القائمة الجانبية';
                }

                // تحديث الإعدادات في قاعدة البيانات
                sendAjaxRequest(url);
            });
        });

        // تبديل وضع المحتوى
        const contentModeToggles = document.querySelectorAll('[data-toggle="content-mode"]');
        contentModeToggles.forEach(function(toggle) {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                const mode = this.getAttribute('data-mode');
                const url = window.APP_CONFIG.APP_URL + '/settings/content/' + mode;

                // تطبيق التغيير فوراً
                if (mode === 'small') {
                    content.classList.add('small-width');
                    this.setAttribute('data-mode', 'large');
                    this.innerHTML = '<i class="fas fa-expand me-2"></i> توسيع المحتوى';
                } else {
                    content.classList.remove('small-width');
                    this.setAttribute('data-mode', 'small');
                    this.innerHTML = '<i class="fas fa-compress me-2"></i> تقليص المحتوى';
                }

                // تحديث الإعدادات في قاعدة البيانات
                sendAjaxRequest(url);
            });
        });

        // إظهار/إخفاء زر الإغلاق حسب حجم الشاشة
        const handleResize = () => {
            if (mobileSidebarClose && mobileSidebarToggle) {
                if (window.innerWidth < 992) {
                    mobileSidebarClose.style.display = 'flex';
                    mobileSidebarToggle.style.display = 'flex';
                } else {
                    mobileSidebarClose.style.display = 'none';
                    mobileSidebarToggle.style.display = 'none';
                    // إغلاق السايدبار على الأجهزة الكبيرة
                    if (sidebar) sidebar.classList.remove('mobile-show');
                    if (sidebarBackdrop) sidebarBackdrop.classList.remove('show');
                    document.body.style.overflow = '';
                }
            }
        };

        window.addEventListener('resize', handleResize);
        handleResize();
    }

    // ===== نظام التمرير ===== //
    function initScrollSystem() {
        const scrollToTopBtn = document.getElementById('scroll-to-top');
        
        if (scrollToTopBtn) {
            // إظهار/إخفاء زر العودة للأعلى
            window.addEventListener('scroll', function() {
                if (window.scrollY > 300) {
                    scrollToTopBtn.classList.add('visible');
                } else {
                    scrollToTopBtn.classList.remove('visible');
                }
            });

            // العودة للأعلى عند النقر
            scrollToTopBtn.addEventListener('click', function() {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            });
        }
    }

    // ===== اختصارات لوحة المفاتيح ===== //
    function initKeyboardShortcuts() {
        const shortcutsModal = document.getElementById('keyboard-shortcuts-modal');
        const shortcutsBtn = document.getElementById('keyboard-shortcuts-btn');
        const closeShortcutsBtn = document.getElementById('close-keyboard-shortcuts');

        // فتح مودال الاختصارات
        if (shortcutsBtn && shortcutsModal) {
            shortcutsBtn.addEventListener('click', () => {
                shortcutsModal.classList.add('show');
            });
        }

        // إغلاق مودال الاختصارات
        if (closeShortcutsBtn && shortcutsModal) {
            closeShortcutsBtn.addEventListener('click', () => shortcutsModal.classList.remove('show'));
            shortcutsModal.addEventListener('click', e => {
                if (e.target === shortcutsModal) shortcutsModal.classList.remove('show');
            });
        }

        // دالة موحدة للتعامل مع ضغطات المفاتيح
        document.addEventListener('keydown', function(e) {
            const tag = e.target.tagName;
            const editable = e.target.isContentEditable;

            // 1) إغلاق المودال بالـ Escape
            if (e.key === 'Escape' && shortcutsModal && shortcutsModal.classList.contains('show')) {
                shortcutsModal.classList.remove('show');
                return;
            }

            // 2) فتح/إغلاق المودال بالـ '?' (Shift + '/')
            if (!e.altKey && !e.ctrlKey && !e.metaKey && e.key === '?') {
                e.preventDefault();
                if (shortcutsModal) {
                    shortcutsModal.classList.toggle('show');
                }
                return;
            }

            // 3) بقية الاختصارات (تجاهل الحقول القابلة للتعديل)
            if (tag === 'INPUT' || tag === 'TEXTAREA' || editable) return;

            // 4) اختصارات Alt + مفتاح (باستخدام e.code للغة مستقلّة)
            if (e.altKey && !e.ctrlKey && !e.metaKey) {
                switch (e.code) {
                    case 'KeyT': // Alt + T: تبديل الثيم
                        e.preventDefault();
                        document.querySelector('[data-toggle="theme"]')?.click();
                        break;

                    case 'KeyL': // Alt + L: تبديل اللغة
                        e.preventDefault();
                        document.querySelector('.language-switch')?.click();
                        break;

                    case 'KeyS': // Alt + S: تصغير/توسيع السايدبار
                        e.preventDefault();
                        document.getElementById('sidebarToggle')?.click();
                        break;

                    case 'KeyC': // Alt + C: تغيير عرض المحتوى
                        e.preventDefault();
                        document.querySelector('[data-toggle="content-mode"]')?.click();
                        break;

                    case 'KeyH': // Alt + H: الصفحة الرئيسية
                        e.preventDefault();
                        window.location.href = window.APP_CONFIG.APP_URL + '/home';
                        break;

                    case 'KeyD': // Alt + D: لوحة التحكم (Dashboard)
                        e.preventDefault();
                        window.location.href = window.APP_CONFIG.APP_URL + '/dashboard';
                        break;

                    case 'KeyP': // Alt + P: الملف الشخصي
                        e.preventDefault();
                        window.location.href = window.APP_CONFIG.APP_URL + '/profile';
                        break;

                    case 'KeyN': // Alt + N: الإشعارات
                        e.preventDefault();
                        window.location.href = window.APP_CONFIG.APP_URL + '/notifications';
                        break;

                    case 'KeyM': // Alt + M: الرسائل
                        e.preventDefault();
                        window.location.href = window.APP_CONFIG.APP_URL + '/chat';
                        break;

                    case 'KeyI': // Alt + I: الإعدادات
                        e.preventDefault();
                        window.location.href = window.APP_CONFIG.APP_URL + '/settings';
                        break;

                    case 'ArrowUp': // Alt + ↑: العودة لأعلى الصفحة
                        e.preventDefault();
                        window.scrollTo({ top: 0, behavior: 'smooth' });
                        break;
                }
            }
        });
    }

    // ===== نظام القوائم المنسدلة ===== //
    function initDropdownSystem() {
        // تفعيل القوائم المنسدلة بشكل مخصص
        const dropdowns = document.querySelectorAll('.dropdown');
        dropdowns.forEach(function(dropdown) {
            const toggle = dropdown.querySelector('.topbar-user');
            const menu = dropdown.querySelector('.dropdown-menu');

            if (toggle && menu) {
                // إضافة معالج النقر
                toggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // إغلاق جميع القوائم المنسدلة الأخرى
                    dropdowns.forEach(function(otherDropdown) {
                        if (otherDropdown !== dropdown && otherDropdown.classList.contains('show')) {
                            otherDropdown.classList.remove('show');
                        }
                    });

                    // تبديل حالة القائمة المنسدلة الحالية
                    dropdown.classList.toggle('show');
                });

                // إضافة تأثيرات للعناصر
                const items = menu.querySelectorAll('.dropdown-item');
                items.forEach(function(item, index) {
                    // إضافة تأخير للظهور التدريجي
                    item.style.transitionDelay = (index * 0.03) + 's';

                    // إضافة معالج النقر
                    item.addEventListener('click', function() {
                        // إغلاق القائمة المنسدلة بعد النقر
                        setTimeout(function() {
                            dropdown.classList.remove('show');
                        }, 100);
                    });
                });
            }
        });

        // إغلاق القائمة المنسدلة عند النقر خارجها
        document.addEventListener('click', function(e) {
            dropdowns.forEach(function(dropdown) {
                if (!dropdown.contains(e.target) && dropdown.classList.contains('show')) {
                    dropdown.classList.remove('show');
                }
            });
        });
    }

    // ===== نوافذ السايدبار المنبثقة ===== //
    function initSidebarTooltips() {
        // إضافة CSS للنوافذ المنبثقة
        const style = document.createElement('style');
        style.textContent = `
            .sidebar-popup {
                position: fixed;
                background-color: #0f2a45;
                color: white;
                border-radius: 4px;
                z-index: 9999;
                display: none;
                opacity: 0;
                transition: all 0.2s ease;
                transform: translateX(-10px) translateY(-5px);
                min-width: 200px;
                max-width: 300px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                border: 1px solid rgba(255, 255, 255, 0.08);
                backdrop-filter: blur(10px);
                -webkit-backdrop-filter: blur(10px);
            }

            .rtl .sidebar-popup {
                transform: translateX(10px) translateY(5px);
            }

            .sidebar-popup.show {
                opacity: 1;
                transform: translateX(0) translateY(0);
            }

            .sidebar-popup-header {
                padding: 14px 16px;
                font-weight: 600;
                font-size: 0.95rem;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                display: flex;
                align-items: center;
                background-color: rgba(255, 255, 255, 0.03);
                letter-spacing: 0.01em;
            }

            .sidebar-popup-header.clickable {
                cursor: pointer;
                transition: background-color 0.2s ease;
            }

            .sidebar-popup-header.clickable:hover {
                background-color: rgba(255, 255, 255, 0.1);
            }

            .sidebar-popup-header i {
                margin-right: 12px;
                width: 22px;
                text-align: center;
                font-size: 1.1rem;
                color: rgba(255, 255, 255, 0.9);
            }

            .rtl .sidebar-popup-header i {
                margin-right: 0;
                margin-left: 12px;
            }

            .sidebar-popup-item {
                padding: 12px 16px;
                display: flex;
                align-items: center;
                cursor: pointer;
                transition: all 0.2s ease;
                border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            }

            .sidebar-popup-item:last-child {
                border-bottom: none;
            }

            .sidebar-popup-item:hover {
                background-color: rgba(255, 255, 255, 0.08);
                padding-left: 20px;
            }

            .rtl .sidebar-popup-item:hover {
                padding-left: 16px;
                padding-right: 20px;
            }

            .sidebar-popup-item i {
                margin-right: 12px;
                width: 20px;
                text-align: center;
                opacity: 0.8;
                font-size: 0.95rem;
                transition: all 0.2s ease;
            }

            .sidebar-popup-item:hover i {
                opacity: 1;
                transform: scale(1.1);
            }

            .rtl .sidebar-popup-item i {
                margin-right: 0;
                margin-left: 12px;
            }

            .sidebar-popup-item span {
                flex: 1;
                font-weight: 500;
                font-size: 0.9rem;
            }

            body.dark-theme .sidebar-popup {
                background-color: #1a202c;
                border-color: rgba(255, 255, 255, 0.05);
            }
        `;
        document.head.appendChild(style);

        // انتظار تحميل السايدبار
        setTimeout(() => {
            const sidebar = document.getElementById('sidebar');
            if (!sidebar) return;

            const isRTL = document.documentElement.dir === 'rtl' || document.body.classList.contains('rtl');
            const sidebarLinks = sidebar.querySelectorAll('.sidebar-menu-link[data-title]');

            sidebarLinks.forEach(link => {
                // تخطي الروابط التي ليس لها عنوان
                if (!link.getAttribute('data-title')) return;

                // إنشاء النافذة المنبثقة
                const popup = document.createElement('div');
                popup.className = 'sidebar-popup';
                if (isRTL) popup.classList.add('rtl');
                document.body.appendChild(popup);

                // إضافة رأس النافذة المنبثقة
                const header = document.createElement('div');
                header.className = 'sidebar-popup-header';

                // نسخ الأيقونة من الرابط
                const iconElement = link.querySelector('i');
                if (iconElement) {
                    const icon = iconElement.cloneNode(true);
                    header.appendChild(icon);
                }

                // إضافة العنوان
                const title = document.createElement('span');
                title.textContent = link.getAttribute('data-title');
                header.appendChild(title);

                popup.appendChild(header);

                // إضافة عناصر النافذة المنبثقة إذا كان الرابط له قائمة منسدلة
                const submenu = link.nextElementSibling && link.nextElementSibling.classList.contains('collapse')
                    ? link.nextElementSibling
                    : null;

                if (submenu) {
                    // إذا كان هناك قائمة منسدلة، أضف العناصر الفرعية
                    const submenuLinks = submenu.querySelectorAll('.sidebar-menu-link');
                    submenuLinks.forEach(sublink => {
                        const item = document.createElement('div');
                        item.className = 'sidebar-popup-item';

                        // نسخ الأيقونة من الرابط الفرعي
                        const subIconElement = sublink.querySelector('i');
                        if (subIconElement) {
                            const subIcon = subIconElement.cloneNode(true);
                            item.appendChild(subIcon);
                        }

                        // إضافة النص
                        const span = document.createElement('span');
                        span.textContent = sublink.textContent.trim();
                        item.appendChild(span);

                        // إضافة معالج النقر
                        item.addEventListener('click', () => {
                            window.location.href = sublink.getAttribute('href');
                        });

                        popup.appendChild(item);
                    });
                } else {
                    // إذا لم يكن هناك قائمة منسدلة، اجعل رأس النافذة المنبثقة قابل للنقر
                    header.classList.add('clickable');
                    header.addEventListener('click', () => {
                        window.location.href = link.getAttribute('href');
                    });
                }

                // إضافة معالجات الأحداث
                link.addEventListener('mouseenter', function() {
                    if (sidebar.classList.contains('collapsed')) {
                        // إخفاء جميع النوافذ المنبثقة الأخرى
                        document.querySelectorAll('.sidebar-popup').forEach(p => {
                            p.style.display = 'none';
                            p.classList.remove('show');
                        });

                        // تحديد موضع النافذة المنبثقة
                        const rect = this.getBoundingClientRect();

                        // تعديل موضع النافذة المنبثقة لتكون محاذية للرابط
                        popup.style.top = (rect.top - 5) + 'px';

                        if (isRTL) {
                            popup.style.right = (window.innerWidth - rect.left + 10) + 'px';
                            popup.style.left = 'auto';
                        } else {
                            popup.style.left = (rect.right + 10) + 'px';
                            popup.style.right = 'auto';
                        }

                        // إظهار النافذة المنبثقة بتأثير سلس
                        popup.style.display = 'block';
                        requestAnimationFrame(() => {
                            popup.classList.add('show');
                        });
                    }
                });

                // إضافة معالج لإخفاء النافذة المنبثقة عند مغادرة الرابط
                link.addEventListener('mouseleave', function() {
                    // تأخير قصير للسماح بالانتقال من الرابط إلى النافذة المنبثقة
                    setTimeout(() => {
                        if (!popup.matches(':hover')) {
                            hidePopup();
                        }
                    }, 50);
                });

                // إضافة معالج لإخفاء النافذة المنبثقة عند مغادرة النافذة
                popup.addEventListener('mouseleave', function(e) {
                    if (e.relatedTarget !== link) {
                        hidePopup();
                    }
                });

                // دالة موحدة لإخفاء النافذة المنبثقة
                function hidePopup() {
                    popup.classList.remove('show');
                    setTimeout(() => {
                        popup.style.display = 'none';
                    }, 200);
                }
            });
        }, 500); // انتظار نصف ثانية لضمان تحميل السايدبار

        console.log('✅ تم تهيئة نوافذ السايدبار المنبثقة');
    }

    // ===== نظام القوائم المنسدلة في السايدبار ===== //
    function initSidebarCollapse() {
        // CSS الآن في ملف sidebar.css - لا حاجة لإضافة CSS هنا

        // انتظار تحميل السايدبار للوظائف التفاعلية
        setTimeout(() => {
            const sidebar = document.getElementById('sidebar');
            if (!sidebar) return;

            // البحث عن جميع الروابط التي تحتوي على data-bs-toggle="collapse"
            const collapseToggles = sidebar.querySelectorAll('[data-bs-toggle="collapse"]');

            collapseToggles.forEach(toggle => {
                const targetSelector = toggle.getAttribute('data-bs-target');
                if (!targetSelector) return;

                const targetElement = document.querySelector(targetSelector);
                if (!targetElement) return;

                // CSS الآن يتولى كل شيء - لا حاجة لـ JavaScript إضافي

                // تحديد الحالة الأولية حسب aria-expanded
                const isExpanded = toggle.getAttribute('aria-expanded') === 'true';
                if (isExpanded) {
                    targetElement.classList.add('show');
                } else {
                    targetElement.classList.remove('show');
                }

                // إضافة معالج النقر
                toggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    const isCurrentlyExpanded = this.getAttribute('aria-expanded') === 'true';
                    const newExpandedState = !isCurrentlyExpanded;

                    // تحديث aria-expanded
                    this.setAttribute('aria-expanded', newExpandedState.toString());

                    // تحديث أيقونة السهم بهدوء
                    const arrow = this.querySelector('.sidebar-menu-arrow, .sidebar-dropdown-icon');
                    if (arrow) {
                        if (newExpandedState) {
                            arrow.style.transform = 'rotate(180deg)';
                            arrow.style.opacity = '0.7';
                        } else {
                            arrow.style.transform = 'rotate(0deg)';
                            arrow.style.opacity = '';
                        }
                    }

                    // إغلاق جميع القوائم الأخرى المفتوحة قبل فتح الجديدة
                    if (newExpandedState) {
                        // البحث عن جميع القوائم المفتوحة الأخرى وإغلاقها
                        collapseToggles.forEach(otherToggle => {
                            if (otherToggle !== this) {
                                const otherTargetSelector = otherToggle.getAttribute('data-bs-target');
                                const otherTargetElement = document.querySelector(otherTargetSelector);

                                if (otherTargetElement && otherTargetElement.classList.contains('show')) {
                                    // إغلاق القائمة الأخرى
                                    otherToggle.setAttribute('aria-expanded', 'false');
                                    otherTargetElement.classList.remove('show');

                                    // تحديث سهم القائمة الأخرى
                                    const otherArrow = otherToggle.querySelector('.sidebar-menu-arrow, .sidebar-dropdown-icon');
                                    if (otherArrow) {
                                        otherArrow.style.transform = 'rotate(0deg)';
                                        otherArrow.style.opacity = '';
                                    }
                                }
                            }
                        });

                        // فتح القائمة الحالية
                        targetElement.classList.add('show');
                    } else {
                        // إغلاق القائمة الحالية
                        targetElement.classList.remove('show');
                    }
                });

                // إضافة تأثير هادئ للسهم
                const arrow = toggle.querySelector('.sidebar-menu-arrow, .sidebar-dropdown-icon');
                if (arrow) {
                    arrow.style.transition = 'all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94)';

                    // تحديث السهم حسب الحالة الأولية
                    const initialExpanded = toggle.getAttribute('aria-expanded') === 'true';
                    arrow.style.transform = initialExpanded ? 'rotate(180deg)' : 'rotate(0deg)';

                    toggle.addEventListener('mouseenter', function() {
                        if (!sidebar.classList.contains('collapsed')) {
                            const isExpanded = this.getAttribute('aria-expanded') === 'true';
                            arrow.style.transform = isExpanded
                                ? 'rotate(180deg) scale(1.05)'
                                : 'rotate(0deg) scale(1.05)';
                            arrow.style.opacity = '0.8';
                        }
                    });

                    toggle.addEventListener('mouseleave', function() {
                        const isExpanded = this.getAttribute('aria-expanded') === 'true';
                        arrow.style.transform = isExpanded
                            ? 'rotate(180deg)'
                            : 'rotate(0deg)';
                        arrow.style.opacity = '';
                    });
                }
            });

            // إغلاق جميع القوائم المفتوحة عند تصغير السايدبار
            const sidebarToggle = document.getElementById('sidebarToggle');
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    // تأخير قصير للسماح بانتهاء انتقال السايدبار
                    setTimeout(() => {
                        if (sidebar.classList.contains('collapsed')) {
                            // إغلاق جميع القوائم المنسدلة
                            collapseToggles.forEach(toggle => {
                                const targetSelector = toggle.getAttribute('data-bs-target');
                                const targetElement = document.querySelector(targetSelector);

                                if (targetElement && targetElement.classList.contains('show')) {
                                    toggle.setAttribute('aria-expanded', 'false');
                                    targetElement.classList.remove('show');
                                    targetElement.style.height = '0px';
                                    targetElement.style.opacity = '0';

                                    const arrow = toggle.querySelector('.sidebar-menu-arrow');
                                    if (arrow) {
                                        arrow.style.transform = 'rotate(0deg)';
                                    }
                                }
                            });
                        }
                    }, 100);
                });
            }

            console.log('✅ تم تهيئة نظام القوائم المنسدلة في السايدبار');
        }, 500);
    }

    // ===== مساعدات AJAX ===== //
    function initAjaxHelpers() {
        // دالة عامة لإرسال طلبات AJAX
        window.sendAjaxRequest = function(url, options = {}) {
            const defaults = {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json'
                }
            };

            const config = Object.assign({}, defaults, options);

            return fetch(url, config)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .catch(error => {
                    console.error('AJAX Error:', error);
                    if (window.toastr) {
                        toastr.error('حدث خطأ في الاتصال');
                    }
                });
        };
    }

    // ===== إعدادات Toastr ===== //
    function initToastrConfig() {
        if (window.toastr) {
            toastr.options = {
                closeButton: true,
                debug: false,
                newestOnTop: true,
                progressBar: true,
                positionClass: window.APP_CONFIG.IS_RTL ? 'toast-top-left' : 'toast-top-right',
                preventDuplicates: true,
                onclick: null,
                showDuration: '300',
                hideDuration: '1000',
                timeOut: '5000',
                extendedTimeOut: '1000',
                showEasing: 'swing',
                hideEasing: 'linear',
                showMethod: 'fadeIn',
                hideMethod: 'fadeOut'
            };
        }
    }

    // ===== وظائف مساعدة عامة ===== //
    
    // تنسيق الأرقام
    window.formatNumber = function(number, decimals = 2) {
        return new Intl.NumberFormat(window.APP_CONFIG.LANG === 'ar' ? 'ar-SA' : 'en-US', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        }).format(number);
    };

    // تنسيق العملة
    window.formatCurrency = function(amount, currency = 'SAR') {
        return new Intl.NumberFormat(window.APP_CONFIG.LANG === 'ar' ? 'ar-SA' : 'en-US', {
            style: 'currency',
            currency: currency
        }).format(amount);
    };

    // تنسيق التاريخ
    window.formatDate = function(date, options = {}) {
        const defaults = {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };
        
        const config = Object.assign({}, defaults, options);
        
        return new Intl.DateTimeFormat(window.APP_CONFIG.LANG === 'ar' ? 'ar-SA' : 'en-US', config)
            .format(new Date(date));
    };

    // إظهار رسالة نجاح
    window.showSuccess = function(message, title = 'نجح') {
        if (window.toastr) {
            toastr.success(message, title);
        }
    };

    // إظهار رسالة خطأ
    window.showError = function(message, title = 'خطأ') {
        if (window.toastr) {
            toastr.error(message, title);
        }
    };

    // إظهار رسالة تحذير
    window.showWarning = function(message, title = 'تحذير') {
        if (window.toastr) {
            toastr.warning(message, title);
        }
    };

    // إظهار رسالة معلومات
    window.showInfo = function(message, title = 'معلومات') {
        if (window.toastr) {
            toastr.info(message, title);
        }
    };

})();
