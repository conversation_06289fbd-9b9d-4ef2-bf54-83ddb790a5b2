<?php
/**
 * Filter Helper Functions
 * مساعد الفلاتر - للتعامل مع فلاتر المستخدمين المحفوظة
 */

/**
 * حفظ فلتر المستخدم
 *
 * @param int $user_id
 * @param int $company_id
 * @param string $page_name
 * @param array $filter_data
 * @return bool
 */
function save_user_filter($user_id, $company_id, $page_name, $filter_data)
{
    try {
        global $db;

        // التحقق من وجود الاتصال بقاعدة البيانات
        if (!$db) {
            error_log("Database connection not available");
            return false;
        }

        // التحقق من وجود الجدول
        $table_check = $db->query("SHOW TABLES LIKE 'user_filters'");
        if ($table_check->rowCount() == 0) {
            error_log("Table user_filters does not exist");
            return false;
        }

        // تحويل بيانات الفلتر إلى JSON
        $filter_json = json_encode($filter_data, JSON_UNESCAPED_UNICODE);

        // البحث عن فلتر موجود للصفحة
        $existing = get_user_filter($user_id, $company_id, $page_name);

        if ($existing) {
            // تحديث الفلتر الموجود
            $sql = "UPDATE user_filters SET
                    filter_data = ?,
                    usage_count = usage_count + 1,
                    last_used_at = NOW(),
                    updated_at = NOW()
                    WHERE user_id = ? AND company_id = ? AND page_name = ? AND is_default = TRUE";

            $stmt = $db->prepare($sql);
            return $stmt->execute([$filter_json, $user_id, $company_id, $page_name]);
        } else {
            // إنشاء فلتر جديد
            $sql = "INSERT INTO user_filters
                    (user_id, company_id, page_name, filter_data, is_default, is_auto_apply, usage_count, last_used_at)
                    VALUES (?, ?, ?, ?, TRUE, TRUE, 1, NOW())";

            $stmt = $db->prepare($sql);
            return $stmt->execute([$user_id, $company_id, $page_name, $filter_json]);
        }
    } catch (Exception $e) {
        error_log("Error saving user filter: " . $e->getMessage());
        return false;
    }
}

/**
 * الحصول على فلتر المستخدم للصفحة
 *
 * @param int $user_id
 * @param int $company_id
 * @param string $page_name
 * @return array|null
 */
function get_user_filter($user_id, $company_id, $page_name)
{
    try {
        global $db;

        // التحقق من وجود الاتصال بقاعدة البيانات
        if (!$db) {
            return null;
        }

        // التحقق من وجود الجدول
        $table_check = $db->query("SHOW TABLES LIKE 'user_filters'");
        if ($table_check->rowCount() == 0) {
            return null;
        }

        $sql = "SELECT * FROM user_filters
                WHERE user_id = ? AND company_id = ? AND page_name = ? AND is_default = TRUE
                ORDER BY last_used_at DESC LIMIT 1";

        $stmt = $db->prepare($sql);
        $stmt->execute([$user_id, $company_id, $page_name]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result && $result['filter_data']) {
            $result['filter_data'] = json_decode($result['filter_data'], true);
        }

        return $result;
    } catch (Exception $e) {
        error_log("Error getting user filter: " . $e->getMessage());
        return null;
    }
}

/**
 * تطبيق الفلتر المحفوظ على البيانات
 *
 * @param array $filter_data
 * @param array $default_filters
 * @return array
 */
function apply_saved_filter($filter_data, $default_filters = [])
{
    if (!$filter_data || !is_array($filter_data)) {
        return $default_filters;
    }

    // دمج الفلاتر المحفوظة مع الافتراضية
    $applied_filters = array_merge($default_filters, $filter_data);

    // التأكد من وجود جميع المفاتيح المطلوبة مع قيم افتراضية
    $required_keys = ['search', 'status', 'group_id', 'per_page', 'current_page'];
    foreach ($required_keys as $key) {
        if (!isset($applied_filters[$key])) {
            $applied_filters[$key] = $default_filters[$key] ?? ($key === 'current_page' ? 1 : '');
        }
        // تحويل null إلى string فارغ بدلاً من حذف المفتاح
        if ($applied_filters[$key] === null) {
            $applied_filters[$key] = ($key === 'current_page' ? 1 : '');
        }
    }

    return $applied_filters;
}

/**
 * تحديث عداد الاستخدام
 *
 * @param int $filter_id
 * @return bool
 */
function update_filter_usage($filter_id)
{
    try {
        global $db;
        
        $sql = "UPDATE user_filters SET 
                usage_count = usage_count + 1,
                last_used_at = NOW()
                WHERE id = ?";
        
        $stmt = $db->prepare($sql);
        return $stmt->execute([$filter_id]);
    } catch (Exception $e) {
        error_log("Error updating usage count: " . $e->getMessage());
        return false;
    }
}

/**
 * تحضير الفلاتر للصفحة مع دمج الفلاتر المحفوظة
 *
 * @param string $page_name
 * @param array $default_filters
 * @return array
 */
function prepare_filters($page_name, $default_filters = [])
{
    // التأكد من وجود المفاتيح المطلوبة في default_filters
    $safe_defaults = array_merge([
        'search' => '',
        'status' => '',
        'group_id' => '',
        'per_page' => 20,
        'current_page' => 1
    ], $default_filters);

    $user = current_user();
    if (!$user) {
        return $safe_defaults;
    }

    // التحقق من وجود المفاتيح المطلوبة في بيانات المستخدم
    if (!isset($user['UserID']) || !isset($user['current_company_id'])) {
        return $safe_defaults;
    }

    $user_id = $user['UserID'];
    $company_id = $user['current_company_id'];

    // أولاً: جرب الحصول على الفلاتر المحفوظة
    try {
        $saved_filter = get_user_filter($user_id, $company_id, $page_name);

        if ($saved_filter && $saved_filter['is_auto_apply']) {
            // تطبيق الفلاتر المحفوظة
            $applied_filters = apply_saved_filter($saved_filter['filter_data'], $safe_defaults);

            // تحديث عداد الاستخدام
            update_filter_usage($saved_filter['id']);

            return $applied_filters;
        }
    } catch (Exception $e) {
        error_log("Error getting saved filter: " . $e->getMessage());
    }

    // إذا لم توجد فلاتر محفوظة، إرجاع فلاتر فارغة لعرض جميع البيانات
    $no_filters = [
        'search' => '',
        'status' => '',
        'group_id' => '',
        'per_page' => $safe_defaults['per_page'],
        'current_page' => 1
    ];

    return $no_filters;
}

/**
 * حفظ فلاتر من نموذج (عندما يطبق المستخدم فلاتر جديدة)
 *
 * @param string $page_name
 * @param array $filter_data
 * @return bool
 */
function save_filters_from_form($page_name, $filter_data)
{
    $user = current_user();
    if (!$user) {
        return false;
    }

    // التحقق من وجود المفاتيح المطلوبة في بيانات المستخدم
    if (!isset($user['UserID']) || !isset($user['current_company_id'])) {
        return false;
    }

    $user_id = $user['UserID'];
    $company_id = $user['current_company_id'];

    // حفظ الفلاتر
    try {
        return save_user_filter($user_id, $company_id, $page_name, $filter_data);
    } catch (Exception $e) {
        error_log("Error saving filters from form: " . $e->getMessage());
        return false;
    }
}

/**
 * مسح جميع الفلاتر للصفحة
 *
 * @param string $page_name
 * @return bool
 */
function clear_page_filters($page_name)
{
    $user = current_user();
    if (!$user) {
        return false;
    }

    // مسح الفلتر الافتراضي فقط
    try {
        global $db;
        $sql = "DELETE FROM user_filters 
                WHERE user_id = ? AND company_id = ? AND page_name = ? AND is_default = TRUE";
        $stmt = $db->prepare($sql);
        return $stmt->execute([$user['UserID'], $user['current_company_id'], $page_name]);
    } catch (Exception $e) {
        error_log("Error clearing page filters: " . $e->getMessage());
        return false;
    }
}

/**
 * التحقق من وجود فلاتر نشطة
 *
 * @param array $filters
 * @param array $exclude_keys
 * @return bool
 */
function has_active_filters($filters, $exclude_keys = ['page', 'per_page', 'limit', 'offset'])
{
    foreach ($filters as $key => $value) {
        if (!in_array($key, $exclude_keys) && !empty($value)) {
            return true;
        }
    }
    return false;
}

/**
 * عد الفلاتر النشطة
 *
 * @param array $filters
 * @param array $exclude_keys
 * @return int
 */
function count_active_filters($filters, $exclude_keys = ['page', 'per_page', 'limit', 'offset'])
{
    $count = 0;

    foreach ($filters as $key => $value) {
        // عد الفلاتر العادية (search, status, group_id, etc.)
        if (!in_array($key, $exclude_keys) && $key !== 'current_page' && !empty($value)) {
            $count++;
        }

        // عد current_page كفلتر منفصل إذا كان أكبر من 1
        if ($key === 'current_page' && (int)$value > 1) {
            $count++;
        }
    }

    return $count;
}

/**
 * عد الفلاتر النشطة مع التحقق من وجود فلاتر محفوظة
 * يظهر العداد فقط إذا كانت هناك فلاتر محفوظة فعلاً في قاعدة البيانات
 *
 * @param string $page_name
 * @param array $filters
 * @return int
 */
function count_active_filters_with_saved_check($page_name, $filters)
{
    $user = current_user();
    if (!$user || !isset($user['UserID']) || !isset($user['current_company_id'])) {
        return 0;
    }

    $user_id = $user['UserID'];
    $company_id = $user['current_company_id'];

    try {
        // التحقق من وجود فلاتر محفوظة في قاعدة البيانات
        $saved_filter = get_user_filter($user_id, $company_id, $page_name);

        if (!$saved_filter || !$saved_filter['is_auto_apply']) {
            return 0; // لا توجد فلاتر محفوظة → لا يظهر رقم
        }

        // إذا كانت هناك فلاتر محفوظة، احسب الفلاتر النشطة
        return count_active_filters($filters);

    } catch (Exception $e) {
        error_log("Error checking saved filters: " . $e->getMessage());
        return 0;
    }
}
