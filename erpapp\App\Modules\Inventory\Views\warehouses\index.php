<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="page-title">
                    <i class="fas fa-warehouse"></i> <?= __('إدارة المستودعات') ?>
                </h1>
                <div>
                    <?php if (canCreate('warehouses')): ?>
                    <a href="<?= base_url('inventory/warehouses/create') ?>" class="btn btn-primary me-2">
                        <i class="fas fa-plus me-1"></i> <?= __('إضافة مستودع جديد') ?>
                    </a>
                    <?php endif; ?>
                    <a href="<?= base_url('inventory') ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> <?= __('العودة للمخزون') ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php
    // استخدام Toastr للرسائل
    $success = flash('warehouse_success');
    if ($success) {
        echo '<script>
            document.addEventListener("DOMContentLoaded", function() {
                if (typeof toastr !== "undefined") {
                    toastr.success("' . addslashes($success['message']) . '", "' . __('نجاح') . '");
                }
            });
        </script>';
    }

    $error = flash('warehouse_error');
    if ($error) {
        echo '<script>
            document.addEventListener("DOMContentLoaded", function() {
                if (typeof toastr !== "undefined") {
                    toastr.error("' . addslashes($error['message']) . '", "' . __('خطأ') . '");
                }
            });
        </script>';
    }
    ?>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i> <?= __('إحصائيات المستودعات') ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-xl-3 col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div class="text-primary mb-2">
                                        <i class="fas fa-warehouse fa-2x"></i>
                                    </div>
                                    <h3 class="mb-1"><?= $stats['total_warehouses'] ?? 0 ?></h3>
                                    <p class="text-muted mb-0"><?= __('إجمالي المستودعات') ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div class="text-success mb-2">
                                        <i class="fas fa-check-circle fa-2x"></i>
                                    </div>
                                    <h3 class="mb-1"><?= $stats['active_warehouses'] ?? 0 ?></h3>
                                    <p class="text-muted mb-0"><?= __('مستودعات نشطة') ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div class="text-info mb-2">
                                        <i class="fas fa-boxes fa-2x"></i>
                                    </div>
                                    <h3 class="mb-1"><?= count(array_filter($warehouses, fn($w) => $w['product_count'] > 0)) ?></h3>
                                    <p class="text-muted mb-0"><?= __('تحتوي على مخزون') ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div class="text-warning mb-2">
                                        <i class="fas fa-chart-pie fa-2x"></i>
                                    </div>
                                    <h3 class="mb-1"><?= number_format(array_sum(array_column($warehouses, 'total_value')), 0) ?></h3>
                                    <p class="text-muted mb-0"><?= __('إجمالي القيمة (ر.س)') ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول المستودعات -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i> <?= __('قائمة المستودعات') ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <span class="text-muted"><?= __('إجمالي المستودعات') ?>: <strong><?= count($warehouses) ?></strong></span>
                        </div>
                        <div>
                            <?php if (canView('warehouses')): ?>
                            <button class="btn btn-outline-success btn-sm me-2" onclick="exportWarehouses()">
                                <i class="fas fa-file-excel me-1"></i> <?= __('تصدير Excel') ?>
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="printWarehouses()">
                                <i class="fas fa-print me-1"></i> <?= __('طباعة') ?>
                            </button>
                            <?php endif; ?>
                        </div>
                    </div>

                    <?php if (empty($warehouses)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-warehouse fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted"><?= __('لا توجد مستودعات') ?></h5>
                        <p class="text-muted"><?= __('لم يتم إنشاء أي مستودعات بعد') ?></p>
                        <?php if (canCreate('warehouses')): ?>
                        <a href="<?= base_url('inventory/warehouses/create') ?>" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> <?= __('إضافة أول مستودع') ?>
                        </a>
                        <?php endif; ?>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="warehousesTable">
                            <thead class="table-dark">
                                <tr>
                                    <th><?= __('#') ?></th>
                                    <th><?= __('كود المستودع') ?></th>
                                    <th><?= __('اسم المستودع') ?></th>
                                    <th><?= __('النوع') ?></th>
                                    <th><?= __('عدد المنتجات') ?></th>
                                    <th><?= __('إجمالي الكمية') ?></th>
                                    <th><?= __('إجمالي القيمة') ?></th>
                                    <th><?= __('السعة') ?></th>
                                    <th><?= __('الحالة') ?></th>
                                    <th><?= __('الإجراءات') ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($warehouses as $index => $warehouse): ?>
                                <tr>
                                    <td><?= $index + 1 ?></td>
                                    <td>
                                        <span class="badge bg-secondary"><?= htmlspecialchars($warehouse['warehouse_code']) ?></span>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?= htmlspecialchars($warehouse['warehouse_name_ar']) ?></strong>
                                            <?php if (!empty($warehouse['warehouse_name_en'])): ?>
                                            <br><small class="text-muted"><?= htmlspecialchars($warehouse['warehouse_name_en']) ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php
                                        $typeLabels = [
                                            'main' => ['text' => 'رئيسي', 'class' => 'primary'],
                                            'branch' => ['text' => 'فرع', 'class' => 'info'],
                                            'virtual' => ['text' => 'افتراضي', 'class' => 'warning'],
                                            'external' => ['text' => 'خارجي', 'class' => 'secondary'],
                                            'temporary' => ['text' => 'مؤقت', 'class' => 'dark']
                                        ];
                                        $type = $typeLabels[$warehouse['warehouse_type']] ?? ['text' => $warehouse['warehouse_type'], 'class' => 'secondary'];
                                        ?>
                                        <span class="badge bg-<?= $type['class'] ?>"><?= $type['text'] ?></span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info"><?= number_format($warehouse['product_count']) ?></span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success"><?= number_format($warehouse['total_quantity'], 3) ?></span>
                                    </td>
                                    <td>
                                        <strong><?= number_format($warehouse['total_value'], 2) ?> <?= __('ر.س') ?></strong>
                                    </td>
                                    <td>
                                        <?php if ($warehouse['capacity']): ?>
                                        <?php
                                        $usagePercentage = $warehouse['capacity'] > 0 ? ($warehouse['current_usage'] / $warehouse['capacity']) * 100 : 0;
                                        $progressClass = $usagePercentage >= 90 ? 'danger' : ($usagePercentage >= 70 ? 'warning' : 'success');
                                        ?>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-<?= $progressClass ?>" role="progressbar"
                                                 style="width: <?= min($usagePercentage, 100) ?>%">
                                                <?= number_format($usagePercentage, 1) ?>%
                                            </div>
                                        </div>
                                        <small class="text-muted">
                                            <?= number_format($warehouse['current_usage'], 2) ?> / <?= number_format($warehouse['capacity'], 2) ?>
                                        </small>
                                        <?php else: ?>
                                        <span class="text-muted"><?= __('غير محدد') ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($warehouse['is_active']): ?>
                                        <span class="badge bg-success"><?= __('نشط') ?></span>
                                        <?php else: ?>
                                        <span class="badge bg-danger"><?= __('غير نشط') ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <?php if (canView('warehouses')): ?>
                                            <a href="<?= base_url('inventory/warehouses/' . $warehouse['warehouse_id']) ?>"
                                               class="btn btn-sm btn-outline-info" title="<?= __('عرض') ?>">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php endif; ?>

                                            <?php if (canEdit('warehouses')): ?>
                                            <a href="<?= base_url('inventory/warehouses/' . $warehouse['warehouse_id'] . '/edit') ?>"
                                               class="btn btn-sm btn-outline-warning" title="<?= __('تعديل') ?>">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <?php endif; ?>

                                            <?php if (canDelete('warehouses')): ?>
                                            <button type="button" class="btn btn-sm btn-outline-danger"
                                                    onclick="deleteWarehouse(<?= $warehouse['warehouse_id'] ?>, '<?= htmlspecialchars($warehouse['warehouse_name_ar']) ?>')"
                                                    title="<?= __('حذف') ?>">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- DataTables CSS -->
<link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>

<script>
$(document).ready(function() {
    // تهيئة DataTables
    $('#warehousesTable').DataTable({
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        pageLength: 25,
        responsive: true,
        order: [[1, 'asc']],
        columnDefs: [
            { orderable: false, targets: [0, -1] }
        ]
    });
});

// حذف مستودع
function deleteWarehouse(warehouseId, warehouseName) {
    if (confirm('<?= __('هل أنت متأكد من حذف المستودع') ?> "' + warehouseName + '"?\n<?= __('هذا الإجراء لا يمكن التراجع عنه') ?>')) {
        // إنشاء نموذج مخفي للإرسال
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?= base_url('inventory/warehouses/') ?>' + warehouseId + '/delete';

        // إضافة CSRF token إذا كان متوفراً
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        }

        document.body.appendChild(form);
        form.submit();
    }
}

// تصدير المستودعات
function exportWarehouses() {
    window.open('<?= base_url('inventory/warehouses/export') ?>', '_blank');
}

// طباعة المستودعات
function printWarehouses() {
    window.print();
}
</script>

<style>
.progress {
    border-radius: 10px;
}

.progress-bar {
    border-radius: 10px;
    font-size: 12px;
    font-weight: bold;
}

.badge {
    font-size: 0.75em;
}

.btn-group .btn {
    margin: 0 1px;
}

.table td {
    vertical-align: middle;
}

@media print {
    .btn, .card-header, .d-flex {
        display: none !important;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>