/* ===== TABLES (بديل Bootstrap) ===== */

.table-responsive {
    overflow-x: auto;
    border-radius: var(--border-radius);
    background: var(--light-card-bg);
    box-shadow: var(--box-shadow-sm);
}

body.dark-theme .table-responsive {
    background: var(--dark-card-bg);
}

.table {
    width: 100%;
    margin: 0;
    background: var(--light-card-bg);
    border-collapse: collapse;
    font-size: 14px;
}

body.dark-theme .table {
    background: var(--dark-card-bg);
    color: var(--dark-text-color);
}

.table th {
    background: var(--gray-100);
    padding: var(--spacing-4) var(--spacing-5);
    text-align: start;
    font-weight: 600;
    color: var(--light-text-muted);
    border-bottom: 1px solid var(--light-card-border);
    white-space: nowrap;
    position: relative;
}

body.dark-theme .table th {
    background: var(--gray-800);
    color: var(--dark-text-muted);
    border-bottom-color: var(--dark-card-border);
}

.table td {
    padding: var(--spacing-4) var(--spacing-5);
    border-bottom: 1px solid var(--gray-200);
    color: var(--light-text-color);
    vertical-align: middle;
}

body.dark-theme .table td {
    border-bottom-color: var(--gray-800);
    color: var(--dark-text-color);
}

/* ===== TABLE HOVER ===== */
.table-hover tbody tr {
    transition: background-color var(--transition-fast) ease;
}

.table-hover tbody tr:hover {
    background: var(--light-hover-bg);
}

body.dark-theme .table-hover tbody tr:hover {
    background: var(--dark-hover-bg);
}

/* ===== TABLE STRIPED ===== */
.table-striped tbody tr:nth-child(odd) {
    background: rgba(0, 0, 0, 0.02);
}

body.dark-theme .table-striped tbody tr:nth-child(odd) {
    background: rgba(255, 255, 255, 0.02);
}

/* ===== TABLE BORDERED ===== */
.table-bordered {
    border: 1px solid var(--light-card-border);
}

.table-bordered th,
.table-bordered td {
    border: 1px solid var(--light-card-border);
}

body.dark-theme .table-bordered {
    border-color: var(--dark-card-border);
}

body.dark-theme .table-bordered th,
body.dark-theme .table-bordered td {
    border-color: var(--dark-card-border);
}

/* ===== TABLE SIZES ===== */
.table-sm th,
.table-sm td {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: 12px;
}

.table-lg th,
.table-lg td {
    padding: var(--spacing-5) var(--spacing-6);
    font-size: 16px;
}

/* ===== SORTABLE COLUMNS ===== */
.table th[data-sortable="true"] {
    cursor: pointer;
    user-select: none;
    padding-right: calc(var(--spacing-5) + 24px);
    transition: background-color var(--transition-fast) ease;
}

.table th[data-sortable="true"]:hover {
    background: var(--gray-200);
    color: var(--light-text-color);
}

body.dark-theme .table th[data-sortable="true"]:hover {
    background: var(--gray-700);
    color: var(--dark-text-color);
}

/* Sort Icons */
.sort-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0;
    visibility: hidden;
    transition: opacity var(--transition-normal) ease, visibility var(--transition-normal) ease;
}

.sort-icon i {
    font-size: 12px;
    color: var(--light-text-muted);
    transition: color var(--transition-fast) ease;
}

th[data-sortable="true"]:hover .sort-icon {
    opacity: 1;
    visibility: visible;
}

th[data-sortable="true"]:hover .sort-icon i {
    color: var(--light-text-color);
}

th.sorted-asc .sort-icon,
th.sorted-desc .sort-icon {
    opacity: 1 !important;
    visibility: visible !important;
}

th.sorted-asc .sort-icon i,
th.sorted-desc .sort-icon i {
    color: var(--primary-color) !important;
}

/* Dark Theme Sort Icons */
body.dark-theme .sort-icon i {
    color: var(--dark-text-muted);
}

body.dark-theme th[data-sortable="true"]:hover .sort-icon i {
    color: var(--dark-text-color);
}

body.dark-theme th.sorted-asc .sort-icon i,
body.dark-theme th.sorted-desc .sort-icon i {
    color: var(--primary-color) !important;
}

/* ===== TABLE ACTIONS ===== */
.table-actions {
    display: flex;
    gap: var(--spacing-2);
    align-items: center;
}

.table-actions .btn {
    padding: var(--spacing-1) var(--spacing-2);
    font-size: 12px;
}

/* ===== TABLE STATUS BADGES ===== */
.table .badge {
    font-size: 11px;
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius-sm);
    font-weight: 500;
}

.badge {
    display: inline-block;
    padding: var(--spacing-1) var(--spacing-2);
    font-size: 12px;
    font-weight: 500;
    border-radius: var(--border-radius-sm);
    text-transform: none;
}

.badge-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-dark);
}

.badge-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-dark);
}

.badge-danger {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-dark);
}

.badge-info {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-dark);
}

.badge-secondary {
    background: var(--gray-200);
    color: var(--gray-700);
}

/* Dark Theme Badges */
body.dark-theme .badge-success {
    background: var(--success-color);
    color: var(--light-color);
}

body.dark-theme .badge-warning {
    background: var(--warning-color);
    color: var(--dark-color);
}

body.dark-theme .badge-danger {
    background: var(--danger-color);
    color: var(--light-color);
}

body.dark-theme .badge-info {
    background: var(--info-color);
    color: var(--light-color);
}

body.dark-theme .badge-secondary {
    background: var(--gray-600);
    color: var(--light-color);
}

/* ===== TABLE LINKS ===== */
.table a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color var(--transition-fast) ease;
}

.table a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

body.dark-theme .table a {
    color: var(--primary-light);
}

body.dark-theme .table a:hover {
    color: var(--primary-color);
}

/* ===== TABLE PAGINATION ===== */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-1);
    margin: var(--spacing-4) 0 0 0;
}

.page-link {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2) var(--spacing-3);
    font-size: 14px;
    color: var(--light-text-muted);
    background: var(--light-card-bg);
    border: 1px solid var(--light-card-border);
    border-radius: var(--border-radius-sm);
    text-decoration: none;
    transition: all var(--transition-fast) ease;
    min-width: 40px;
    height: 40px;
}

.page-link:hover {
    color: var(--light-text-color);
    background: var(--light-hover-bg);
    text-decoration: none;
}

.page-item.active .page-link {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--light-color);
    font-weight: 500;
}

.page-item.disabled .page-link {
    color: var(--gray-400);
    background: var(--light-card-bg);
    cursor: not-allowed;
    opacity: 0.6;
}

/* Dark Theme Pagination */
body.dark-theme .page-link {
    color: var(--dark-text-muted);
    background: var(--dark-card-bg);
    border-color: var(--dark-card-border);
}

body.dark-theme .page-link:hover {
    color: var(--dark-text-color);
    background: var(--dark-hover-bg);
}

body.dark-theme .page-item.active .page-link {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--light-color);
}

body.dark-theme .page-item.disabled .page-link {
    color: var(--gray-600);
    background: var(--dark-card-bg);
}

/* ===== TABLE INFO ===== */
.datatable-info {
    color: var(--light-text-muted);
    font-size: 13px;
    text-align: center;
}

.datatable-info strong {
    color: var(--light-text-color);
    font-weight: 500;
}

body.dark-theme .datatable-info {
    color: var(--dark-text-muted);
}

body.dark-theme .datatable-info strong {
    color: var(--dark-text-color);
}

/* ===== RESPONSIVE TABLE ===== */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 12px;
    }
    
    .table th,
    .table td {
        padding: var(--spacing-2) var(--spacing-3);
    }
    
    .table th[data-sortable="true"] {
        padding-right: calc(var(--spacing-3) + 20px);
    }
    
    .sort-icon {
        right: 8px;
    }
}
