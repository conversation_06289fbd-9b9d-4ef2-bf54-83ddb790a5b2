<?php
/**
 * Template موحد للإجراءات (Add, Export, etc.)
 */
?>

<?php foreach ($actions as $action): ?>
    <?php
    $class = $action['class'] ?? 'btn-primary';
    $icon = $action['icon'] ?? '';
    $text = $action['text'] ?? '';
    $url = $action['url'] ?? '#';
    $type = $action['type'] ?? 'link'; // link, button, dropdown
    ?>
    
    <?php if ($type === 'link'): ?>
        <a href="<?= base_url($url) ?>" class="btn <?= $class ?> mb-2">
            <?php if ($icon): ?>
                <i class="<?= $icon ?> me-2"></i>
            <?php endif; ?>
            <?= $text ?>
        </a>
        
    <?php elseif ($type === 'button'): ?>
        <button type="button" 
                class="btn <?= $class ?> mb-2"
                <?= isset($action['onclick']) ? 'onclick="' . $action['onclick'] . '"' : '' ?>
                <?= isset($action['data_attributes']) ? implode(' ', array_map(function($k, $v) { return 'data-' . $k . '="' . $v . '"'; }, array_keys($action['data_attributes']), $action['data_attributes'])) : '' ?>>
            <?php if ($icon): ?>
                <i class="<?= $icon ?> me-2"></i>
            <?php endif; ?>
            <?= $text ?>
        </button>
        
    <?php elseif ($type === 'dropdown'): ?>
        <div class="btn-group mb-2">
            <button type="button" class="btn <?= $class ?> dropdown-toggle" data-bs-toggle="dropdown">
                <?php if ($icon): ?>
                    <i class="<?= $icon ?> me-2"></i>
                <?php endif; ?>
                <?= $text ?>
            </button>
            <ul class="dropdown-menu">
                <?php foreach ($action['items'] as $item): ?>
                    <?php if ($item['type'] === 'divider'): ?>
                        <li><hr class="dropdown-divider"></li>
                    <?php else: ?>
                        <li>
                            <a class="dropdown-item" href="<?= base_url($item['url']) ?>">
                                <?php if (isset($item['icon'])): ?>
                                    <i class="<?= $item['icon'] ?> me-2"></i>
                                <?php endif; ?>
                                <?= $item['text'] ?>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>
    
<?php endforeach; ?>
