<h1 class="page-title"><?= __('لوحة التحكم') ?></h1>

<div class="row mb-4">
    <div class="col-md-6 col-sm-6 mb-3">
        <div class="card bg-primary text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-uppercase fw-bold mb-1"><?= __('الشركات') ?></h6>
                        <h2 class="display-6 fw-bold mb-0"><?= $stats['companies'] ?? 0 ?></h2>
                    </div>
                    <i class="fas fa-building fa-3x opacity-50"></i>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a href="<?= base_url('companies') ?>" class="text-white text-decoration-none"><?= __('عرض التفاصيل') ?></a>
                <i class="fas fa-arrow-circle-right text-white"></i>
            </div>
        </div>
    </div>



    <div class="col-md-6 col-sm-6 mb-3">
        <div class="card bg-danger text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-uppercase fw-bold mb-1"><?= __('المهام') ?></h6>
                        <h2 class="display-6 fw-bold mb-0"><?= count($tasks) ?></h2>
                    </div>
                    <i class="fas fa-tasks fa-3x opacity-50"></i>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a href="#" class="text-white text-decoration-none"><?= __('عرض التفاصيل') ?></a>
                <i class="fas fa-arrow-circle-right text-white"></i>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><?= __('الشركات') ?></h5>
                <a href="<?= base_url('companies/create') ?>" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus"></i> <?= __('إضافة شركة') ?>
                </a>
            </div>
            <div class="card-body">
                <?php if (empty($companies)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-building fa-4x text-muted mb-3"></i>
                        <p class="mb-0"><?= __('لا توجد شركات حتى الآن') ?></p>
                        <a href="<?= base_url('companies/create') ?>" class="btn btn-primary mt-3">
                            <?= __('إضافة شركة جديدة') ?>
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th><?= __('الشركة') ?></th>
                                    <th><?= __('البريد الإلكتروني') ?></th>
                                    <th><?= __('الحالة') ?></th>
                                    <th><?= __('الإجراءات') ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($companies as $company): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <?php if (!empty($company['CompanyLogo'])): ?>
                                                    <img src="<?= base_url($company['CompanyLogo']) ?>" alt="<?= e($company['CompanyName']) ?>" class="rounded-circle me-2" width="40" height="40" style="object-fit: cover;">
                                                <?php else: ?>
                                                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 40px; height: 40px;">
                                                        <?= strtoupper(substr($company['CompanyName'], 0, 1)) ?>
                                                    </div>
                                                <?php endif; ?>
                                                <div>
                                                    <div class="fw-bold"><?= $company['CompanyName'] ?></div>
                                                    <small class="text-muted"><?= $company['CompanyNameEN'] ?? '' ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td><?= $company['CompanyEmail'] ?></td>
                                        <td>
                                            <?php if ($company['CompanyStatus'] == 'Active'): ?>
                                                <span class="badge bg-success"><?= __('نشط') ?></span>
                                            <?php elseif ($company['CompanyStatus'] == 'Inactive'): ?>
                                                <span class="badge bg-danger"><?= __('غير نشط') ?></span>
                                            <?php elseif ($company['CompanyStatus'] == 'Trial'): ?>
                                                <span class="badge bg-warning"><?= __('تجريبي') ?></span>
                                            <?php elseif ($company['CompanyStatus'] == 'Expired'): ?>
                                                <span class="badge bg-secondary"><?= __('منتهي') ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <a href="<?= base_url('companies/' . $company['CompanyID']) ?>" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?= base_url('companies/' . $company['CompanyID'] . '/edit') ?>" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><?= __('آخر الإشعارات') ?></h5>
                <a href="<?= base_url('notifications') ?>" class="btn btn-sm btn-outline-primary">
                    <?= __('عرض الكل') ?>
                </a>
            </div>
            <div class="card-body p-0">
                <?php if (empty($notifications)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-bell fa-4x text-muted mb-3"></i>
                        <p class="mb-0"><?= __('لا توجد إشعارات حتى الآن') ?></p>
                    </div>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($notifications as $notification): ?>
                            <a href="#" class="list-group-item list-group-item-action <?= $notification['is_read'] ? '' : 'bg-light' ?>">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1"><?= $notification['title'] ?></h6>
                                    <small class="text-muted"><?= format_date($notification['created_at'], 'd/m/Y H:i') ?></small>
                                </div>
                                <p class="mb-1"><?= $notification['message'] ?></p>
                            </a>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><?= __('المهام') ?></h5>
                <a href="#" class="btn btn-sm btn-outline-primary">
                    <?= __('عرض الكل') ?>
                </a>
            </div>
            <div class="card-body p-0">
                <?php if (empty($tasks)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-tasks fa-4x text-muted mb-3"></i>
                        <p class="mb-0"><?= __('لا توجد مهام حتى الآن') ?></p>
                    </div>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($tasks as $task): ?>
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1"><?= $task['title'] ?></h6>
                                    <small class="text-muted"><?= format_date($task['due_date'], 'd/m/Y') ?></small>
                                </div>
                                <p class="mb-1"><?= $task['description'] ?></p>
                                <?php if ($task['status'] == 'pending'): ?>
                                    <span class="badge bg-warning"><?= __('قيد الانتظار') ?></span>
                                <?php elseif ($task['status'] == 'in_progress'): ?>
                                    <span class="badge bg-info"><?= __('قيد التنفيذ') ?></span>
                                <?php elseif ($task['status'] == 'completed'): ?>
                                    <span class="badge bg-success"><?= __('مكتمل') ?></span>
                                <?php elseif ($task['status'] == 'canceled'): ?>
                                    <span class="badge bg-danger"><?= __('ملغي') ?></span>
                                <?php elseif ($task['status'] == 'deferred'): ?>
                                    <span class="badge bg-secondary"><?= __('مؤجل') ?></span>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
