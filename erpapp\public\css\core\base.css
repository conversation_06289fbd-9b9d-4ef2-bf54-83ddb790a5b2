/* ===== BASE STYLES (بديل Bootstrap) ===== */

/* Reset & Base */
*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    line-height: 1.6;
}

body {
    font-family: 'Inter', 'Tajawal', sans-serif;
    background-color: var(--light-bg-color);
    color: var(--light-text-color);
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* Dark Theme */
body.dark-theme {
    background-color: var(--dark-bg-color);
    color: var(--dark-text-color);
}

/* ===== CONTAINER SYSTEM ===== */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
}

.container-fluid {
    width: 100%;
    padding: 0 var(--spacing-4);
}

/* ===== GRID SYSTEM ===== */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 calc(-1 * var(--spacing-3));
}

.col {
    flex: 1;
    padding: 0 var(--spacing-3);
}

.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

/* ===== CARDS ===== */
.card {
    background: var(--light-card-bg);
    border: 1px solid var(--light-card-border);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-sm);
    overflow: hidden;
}

body.dark-theme .card {
    background: var(--dark-card-bg);
    border-color: var(--dark-card-border);
}

.card-header {
    padding: var(--spacing-4) var(--spacing-5);
    background: var(--gray-100);
    border-bottom: 1px solid var(--light-card-border);
    font-weight: 600;
}

body.dark-theme .card-header {
    background: var(--gray-800);
    border-bottom-color: var(--dark-card-border);
    color: var(--dark-text-color);
}

.card-body {
    padding: var(--spacing-5);
}

.card-footer {
    padding: var(--spacing-4) var(--spacing-5);
    background: var(--gray-100);
    border-top: 1px solid var(--light-card-border);
}

body.dark-theme .card-footer {
    background: var(--gray-800);
    border-top-color: var(--dark-card-border);
}

/* ===== ALERTS ===== */
.alert {
    padding: var(--spacing-4);
    border-radius: var(--border-radius);
    border: 1px solid transparent;
    margin-bottom: var(--spacing-4);
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    border-color: var(--success-color);
    color: var(--success-dark);
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    border-color: var(--warning-color);
    color: var(--warning-dark);
}

.alert-danger {
    background: rgba(239, 68, 68, 0.1);
    border-color: var(--danger-color);
    color: var(--danger-dark);
}

.alert-info {
    background: rgba(59, 130, 246, 0.1);
    border-color: var(--info-color);
    color: var(--info-dark);
}

/* Dark Theme Alerts */
body.dark-theme .alert-success {
    background: rgba(16, 185, 129, 0.2);
    color: var(--success-color);
}

body.dark-theme .alert-warning {
    background: rgba(245, 158, 11, 0.2);
    color: var(--warning-color);
}

body.dark-theme .alert-danger {
    background: rgba(239, 68, 68, 0.2);
    color: var(--danger-color);
}

body.dark-theme .alert-info {
    background: rgba(59, 130, 246, 0.2);
    color: var(--info-color);
}

/* ===== UTILITIES ===== */
.d-flex { display: flex; }
.d-block { display: block; }
.d-none { display: none; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }

.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-end { justify-content: flex-end; }
.align-items-center { align-items: center; }
.align-items-start { align-items: flex-start; }

.text-center { text-align: center; }
.text-end { text-align: end; }
.text-start { text-align: start; }

.text-muted { color: var(--light-text-muted); }
body.dark-theme .text-muted { color: var(--dark-text-muted); }

.text-primary { color: var(--primary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }
.text-info { color: var(--info-color); }

/* Margins */
.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-1); }
.m-2 { margin: var(--spacing-2); }
.m-3 { margin: var(--spacing-3); }
.m-4 { margin: var(--spacing-4); }
.m-5 { margin: var(--spacing-5); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-1); }
.mt-2 { margin-top: var(--spacing-2); }
.mt-3 { margin-top: var(--spacing-3); }
.mt-4 { margin-top: var(--spacing-4); }
.mt-5 { margin-top: var(--spacing-5); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-1); }
.mb-2 { margin-bottom: var(--spacing-2); }
.mb-3 { margin-bottom: var(--spacing-3); }
.mb-4 { margin-bottom: var(--spacing-4); }
.mb-5 { margin-bottom: var(--spacing-5); }

.me-1 { margin-inline-end: var(--spacing-1); }
.me-2 { margin-inline-end: var(--spacing-2); }
.me-3 { margin-inline-end: var(--spacing-3); }
.me-4 { margin-inline-end: var(--spacing-4); }

/* Paddings */
.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-1); }
.p-2 { padding: var(--spacing-2); }
.p-3 { padding: var(--spacing-3); }
.p-4 { padding: var(--spacing-4); }
.p-5 { padding: var(--spacing-5); }

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-3);
    }
    
    .col-md-6 { flex: 0 0 100%; max-width: 100%; }
    .col-md-4 { flex: 0 0 100%; max-width: 100%; }
    .col-md-3 { flex: 0 0 100%; max-width: 100%; }
}

/* ===== RTL SUPPORT ===== */
[dir="rtl"] .me-1 { margin-inline-end: 0; margin-inline-start: var(--spacing-1); }
[dir="rtl"] .me-2 { margin-inline-end: 0; margin-inline-start: var(--spacing-2); }
[dir="rtl"] .me-3 { margin-inline-end: 0; margin-inline-start: var(--spacing-3); }
[dir="rtl"] .me-4 { margin-inline-end: 0; margin-inline-start: var(--spacing-4); }
