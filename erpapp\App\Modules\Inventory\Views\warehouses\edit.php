<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="page-title">
                    <i class="fas fa-edit"></i> <?= __('تعديل المستودع') ?>
                </h1>
                <div>
                    <a href="<?= base_url('inventory/warehouses/' . $warehouse['warehouse_id']) ?>" class="btn btn-info me-2">
                        <i class="fas fa-eye me-1"></i> <?= __('عرض المستودع') ?>
                    </a>
                    <a href="<?= base_url('inventory/warehouses') ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> <?= __('العودة للمستودعات') ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php
    // عرض رسائل الخطأ
    $error = flash('warehouse_error');
    if ($error) {
        echo '<script>
            document.addEventListener("DOMContentLoaded", function() {
                if (typeof toastr !== "undefined") {
                    toastr.error("' . addslashes($error['message']) . '", "' . __('خطأ') . '");
                }
            });
        </script>';
    }
    ?>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-warehouse me-2"></i> <?= __('تعديل بيانات المستودع') ?>: <?= htmlspecialchars($warehouse['warehouse_name_ar']) ?>
                    </h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('inventory/warehouses/' . $warehouse['warehouse_id'] . '/update') ?>" method="POST" id="warehouseForm">
                        <div class="row">
                            <!-- كود المستودع -->
                            <div class="col-md-6 mb-3">
                                <label for="warehouse_code" class="form-label required">
                                    <i class="fas fa-barcode me-1"></i> <?= __('كود المستودع') ?>
                                </label>
                                <input type="text" class="form-control" id="warehouse_code" name="warehouse_code"
                                       value="<?= htmlspecialchars($warehouse['warehouse_code']) ?>" required maxlength="50">
                                <div class="form-text"><?= __('كود فريد للمستودع') ?></div>
                            </div>

                            <!-- نوع المستودع -->
                            <div class="col-md-6 mb-3">
                                <label for="warehouse_type" class="form-label required">
                                    <i class="fas fa-tags me-1"></i> <?= __('نوع المستودع') ?>
                                </label>
                                <select class="form-select" id="warehouse_type" name="warehouse_type" required>
                                    <option value=""><?= __('اختر نوع المستودع') ?></option>
                                    <option value="main" <?= $warehouse['warehouse_type'] == 'main' ? 'selected' : '' ?>>
                                        <?= __('مستودع رئيسي') ?>
                                    </option>
                                    <option value="branch" <?= $warehouse['warehouse_type'] == 'branch' ? 'selected' : '' ?>>
                                        <?= __('مستودع فرع') ?>
                                    </option>
                                    <option value="virtual" <?= $warehouse['warehouse_type'] == 'virtual' ? 'selected' : '' ?>>
                                        <?= __('مستودع افتراضي') ?>
                                    </option>
                                    <option value="external" <?= $warehouse['warehouse_type'] == 'external' ? 'selected' : '' ?>>
                                        <?= __('مستودع خارجي') ?>
                                    </option>
                                    <option value="temporary" <?= $warehouse['warehouse_type'] == 'temporary' ? 'selected' : '' ?>>
                                        <?= __('مستودع مؤقت') ?>
                                    </option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <!-- اسم المستودع بالعربية -->
                            <div class="col-md-6 mb-3">
                                <label for="warehouse_name_ar" class="form-label required">
                                    <i class="fas fa-font me-1"></i> <?= __('اسم المستودع (عربي)') ?>
                                </label>
                                <input type="text" class="form-control" id="warehouse_name_ar" name="warehouse_name_ar"
                                       value="<?= htmlspecialchars($warehouse['warehouse_name_ar']) ?>" required maxlength="100">
                            </div>

                            <!-- اسم المستودع بالإنجليزية -->
                            <div class="col-md-6 mb-3">
                                <label for="warehouse_name_en" class="form-label">
                                    <i class="fas fa-font me-1"></i> <?= __('اسم المستودع (إنجليزي)') ?>
                                </label>
                                <input type="text" class="form-control" id="warehouse_name_en" name="warehouse_name_en"
                                       value="<?= htmlspecialchars($warehouse['warehouse_name_en'] ?? '') ?>" maxlength="100">
                                <div class="form-text"><?= __('اختياري') ?></div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- العنوان -->
                            <div class="col-12 mb-3">
                                <label for="address" class="form-label">
                                    <i class="fas fa-map-marker-alt me-1"></i> <?= __('العنوان') ?>
                                </label>
                                <textarea class="form-control" id="address" name="address" rows="3"
                                          maxlength="500"><?= htmlspecialchars($warehouse['address'] ?? '') ?></textarea>
                                <div class="form-text"><?= __('عنوان المستودع الفعلي') ?></div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- رقم الهاتف -->
                            <div class="col-md-4 mb-3">
                                <label for="phone" class="form-label">
                                    <i class="fas fa-phone me-1"></i> <?= __('رقم الهاتف') ?>
                                </label>
                                <input type="tel" class="form-control" id="phone" name="phone"
                                       value="<?= htmlspecialchars($warehouse['phone'] ?? '') ?>" maxlength="20">
                            </div>

                            <!-- البريد الإلكتروني -->
                            <div class="col-md-4 mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i> <?= __('البريد الإلكتروني') ?>
                                </label>
                                <input type="email" class="form-control" id="email" name="email"
                                       value="<?= htmlspecialchars($warehouse['email'] ?? '') ?>" maxlength="100">
                            </div>

                            <!-- اسم المدير -->
                            <div class="col-md-4 mb-3">
                                <label for="manager_name" class="form-label">
                                    <i class="fas fa-user-tie me-1"></i> <?= __('اسم المدير') ?>
                                </label>
                                <input type="text" class="form-control" id="manager_name" name="manager_name"
                                       value="<?= htmlspecialchars($warehouse['manager_name'] ?? '') ?>" maxlength="100">
                            </div>
                        </div>

                        <div class="row">
                            <!-- السعة -->
                            <div class="col-md-6 mb-3">
                                <label for="capacity" class="form-label">
                                    <i class="fas fa-weight me-1"></i> <?= __('السعة القصوى') ?>
                                </label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="capacity" name="capacity"
                                           value="<?= $warehouse['capacity'] ?? '' ?>" min="0" step="0.01">
                                    <span class="input-group-text"><?= __('وحدة') ?></span>
                                </div>
                                <div class="form-text"><?= __('السعة القصوى للمستودع (اختياري)') ?></div>
                            </div>

                            <!-- الحالة -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label"><?= __('الحالة') ?></label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                           value="1" <?= $warehouse['is_active'] ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="is_active">
                                        <i class="fas fa-toggle-on me-1"></i> <?= __('مستودع نشط') ?>
                                    </label>
                                </div>
                                <div class="form-text"><?= __('يمكن استخدام المستودع في العمليات') ?></div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <!-- أزرار الحفظ والإلغاء -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="submit" class="btn btn-warning btn-lg">
                                            <i class="fas fa-save me-2"></i> <?= __('حفظ التعديلات') ?>
                                        </button>
                                        <button type="reset" class="btn btn-outline-secondary btn-lg ms-2">
                                            <i class="fas fa-undo me-2"></i> <?= __('إعادة تعيين') ?>
                                        </button>
                                    </div>
                                    <div>
                                        <a href="<?= base_url('inventory/warehouses/' . $warehouse['warehouse_id']) ?>" class="btn btn-outline-info btn-lg me-2">
                                            <i class="fas fa-eye me-2"></i> <?= __('عرض المستودع') ?>
                                        </a>
                                        <a href="<?= base_url('inventory/warehouses') ?>" class="btn btn-outline-danger btn-lg">
                                            <i class="fas fa-times me-2"></i> <?= __('إلغاء') ?>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من صحة النموذج
    const form = document.getElementById('warehouseForm');
    form.addEventListener('submit', function(e) {
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(function(field) {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });

        if (!isValid) {
            e.preventDefault();
            if (typeof toastr !== 'undefined') {
                toastr.error('<?= __('يرجى ملء جميع الحقول المطلوبة') ?>', '<?= __('خطأ في النموذج') ?>');
            }
        }
    });

    // إزالة رسائل الخطأ عند الكتابة
    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach(function(input) {
        input.addEventListener('input', function() {
            this.classList.remove('is-invalid');
        });
    });

    // تأكيد التعديل
    form.addEventListener('submit', function(e) {
        if (!confirm('<?= __('هل أنت متأكد من حفظ التعديلات؟') ?>')) {
            e.preventDefault();
        }
    });
});
</script>

<style>
.required::after {
    content: " *";
    color: #dc3545;
    font-weight: bold;
}

.form-check-input:checked {
    background-color: #198754;
    border-color: #198754;
}

.is-invalid {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.form-control:focus, .form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}
</style>