-- إنشاء جدول فلاتر المستخدمين إذا لم يكن موجود
CREATE TABLE IF NOT EXISTS user_filters (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    company_id INT NOT NULL,
    page_name VARCHAR(100) NOT NULL,
    filter_name VARCHAR(100),
    filter_data JSON NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    is_auto_apply BOOLEAN DEFAULT TRUE,
    description TEXT,
    usage_count INT DEFAULT 0,
    last_used_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_company (user_id, company_id),
    INDEX idx_page_name (page_name),
    INDEX idx_last_used (last_used_at),
    INDEX idx_usage_count (usage_count)
);

-- إدراج بيانات تجريبية (اختياري)
-- INSERT INTO user_filters (user_id, company_id, page_name, filter_data, is_default, is_auto_apply, usage_count, last_used_at) 
-- VALUES (1, 1, 'suppliers', '{"search":"","status":"","group_id":"","per_page":"20"}', TRUE, TRUE, 1, NOW());
