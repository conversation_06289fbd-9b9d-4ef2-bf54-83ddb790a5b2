<?php
namespace App\Modules\Purchases\Controllers;

use App\Core\Controller;
use App\Modules\Purchases\Models\SupplierGroup;

/**
 * SupplierGroupController - متحكم مجموعات الموردين
 */
class SupplierGroupController extends Controller
{
    protected $supplierGroupModel;

    public function __construct()
    {
        parent::__construct();
        $this->supplierGroupModel = new SupplierGroup();
    }

    /**
     * عرض قائمة مجموعات الموردين
     */
    public function index()
    {
        $company_id = current_user()['current_company_id'];
        
        // الحصول على الفلاتر
        $filters = [
            'search' => $_GET['search'] ?? ''
        ];

        // الحصول على مجموعات الموردين
        $supplierGroups = $this->supplierGroupModel->getByCompany($company_id, $filters);

        // الحصول على الإحصائيات
        $stats = $this->supplierGroupModel->getStats($company_id);

        // عرض الصفحة
        $this->view('Purchases::supplier-groups/index', [
            'supplierGroups' => $supplierGroups,
            'stats' => $stats,
            'filters' => $filters
        ]);
    }

    /**
     * عرض صفحة إضافة مجموعة موردين جديدة
     */
    public function create()
    {
        $this->view('Purchases::supplier-groups/create');
    }

    /**
     * حفظ مجموعة موردين جديدة
     */
    public function store()
    {
        try {
            // التحقق من صحة البيانات
            $validatedData = $this->validateSupplierGroupData($_POST);
            
            // إضافة معلومات المستخدم والشركة
            $validatedData['company_id'] = current_user()['current_company_id'];
            $validatedData['created_by'] = current_user()['UserID'];

            // إنشاء المجموعة
            $groupNumber = $this->supplierGroupModel->create($validatedData);

            if ($groupNumber) {
                flash('success', 'تم إنشاء مجموعة الموردين بنجاح');
                redirect('/purchases/supplier-groups/' . $groupNumber);
            } else {
                flash('error', 'حدث خطأ أثناء إنشاء مجموعة الموردين');
                redirect('/purchases/supplier-groups/create');
            }

        } catch (Exception $e) {
            flash('error', $e->getMessage());
            redirect('/purchases/supplier-groups/create');
        }
    }

    /**
     * عرض تفاصيل مجموعة موردين
     */
    public function show()
    {
        $group_number = $this->params['id'];
        $company_id = current_user()['current_company_id'];

        $supplierGroup = $this->supplierGroupModel->getByNumber($group_number, $company_id);

        if (!$supplierGroup) {
            flash('error', 'مجموعة الموردين غير موجودة');
            redirect('/purchases/supplier-groups');
        }

        $this->view('Purchases::supplier-groups/show', [
            'supplierGroup' => $supplierGroup
        ]);
    }

    /**
     * عرض صفحة تعديل مجموعة موردين
     */
    public function edit()
    {
        $group_number = $this->params['id'];
        $company_id = current_user()['current_company_id'];

        $supplierGroup = $this->supplierGroupModel->getByNumber($group_number, $company_id);

        if (!$supplierGroup) {
            flash('error', 'مجموعة الموردين غير موجودة');
            redirect('/purchases/supplier-groups');
        }

        $this->view('Purchases::supplier-groups/edit', [
            'supplierGroup' => $supplierGroup
        ]);
    }

    /**
     * تحديث مجموعة موردين
     */
    public function update()
    {
        try {
            $group_number = $this->params['id'];
            $company_id = current_user()['current_company_id'];

            // التحقق من وجود المجموعة
            $supplierGroup = $this->supplierGroupModel->getByNumber($group_number, $company_id);
            if (!$supplierGroup) {
                flash('error', 'مجموعة الموردين غير موجودة');
                redirect('/purchases/supplier-groups');
            }

            // التحقق من صحة البيانات
            $validatedData = $this->validateSupplierGroupData($_POST);
            $validatedData['updated_by'] = current_user()['UserID'];

            // تحديث المجموعة
            $result = $this->supplierGroupModel->update($group_number, $validatedData, $company_id);

            if ($result) {
                flash('success', 'تم تحديث مجموعة الموردين بنجاح');
                redirect('/purchases/supplier-groups/' . $group_number);
            } else {
                flash('error', 'حدث خطأ أثناء تحديث مجموعة الموردين');
                redirect('/purchases/supplier-groups/' . $group_number . '/edit');
            }

        } catch (Exception $e) {
            flash('error', $e->getMessage());
            redirect('/purchases/supplier-groups/' . $this->params['id'] . '/edit');
        }
    }

    /**
     * حذف مجموعة موردين
     */
    public function delete()
    {
        try {
            $group_number = $this->params['id'];
            $company_id = current_user()['current_company_id'];

            // التحقق من وجود المجموعة
            $supplierGroup = $this->supplierGroupModel->getByNumber($group_number, $company_id);
            if (!$supplierGroup) {
                flash('error', 'مجموعة الموردين غير موجودة');
                redirect('/purchases/supplier-groups');
            }

            // حذف المجموعة
            $result = $this->supplierGroupModel->delete($group_number, $company_id);

            if ($result) {
                flash('success', 'تم حذف مجموعة الموردين بنجاح');
            } else {
                flash('error', 'حدث خطأ أثناء حذف مجموعة الموردين');
            }

        } catch (Exception $e) {
            flash('error', $e->getMessage());
        }

        redirect('/purchases/supplier-groups');
    }

    /**
     * التحقق من صحة بيانات مجموعة الموردين
     */
    private function validateSupplierGroupData($data)
    {
        $errors = [];

        // التحقق من الاسم العربي
        if (empty($data['name_ar'])) {
            $errors[] = 'اسم المجموعة بالعربية مطلوب';
        }

        if (!empty($errors)) {
            throw new Exception(implode('<br>', $errors));
        }

        // إعداد البيانات
        return [
            'name_ar' => trim($data['name_ar']),
            'name_en' => trim($data['name_en'] ?? '')
        ];
    }
}
