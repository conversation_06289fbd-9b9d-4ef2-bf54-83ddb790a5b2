<?php
namespace App\Modules\Purchases\Controllers;

use App\Modules\Purchases\Models\SupplierGroup;
use Exception;
use PDO;

/**
 * SupplierGroupController - متحكم مجموعات الموردين
 */
class SupplierGroupController
{
    /**
     * Route parameters
     */
    protected $params = [];

    /**
     * SupplierGroup model
     */
    protected $supplierGroupModel;

    /**
     * Constructor
     */
    public function __construct($params = [])
    {
        $this->params = $params;
        $this->supplierGroupModel = new SupplierGroup();

        // التحقق من تسجيل الدخول
        if (!is_logged_in()) {
            redirect(base_url('login'));
        }

        // التحقق من وجود شركة حالية
        $user = current_user();
        if (!$user || !$user['current_company_id']) {
            flash('supplier_group_error', 'يجب تحديد شركة حالية للوصول إلى مجموعات الموردين', 'warning');
            redirect(base_url('companies'));
        }
    }

    /**
     * عرض قائمة مجموعات الموردين
     */
    public function index()
    {
        $company_id = current_user()['current_company_id'];

        // الحصول على الفلاتر من قاعدة البيانات (متوافق مع سياستنا)
        $default_filters = [
            'search' => '',
            'per_page' => 20,
            'current_page' => 1
        ];

        // الحصول على الفلاتر مع دمج الفلاتر المحفوظة
        $applied_filters = prepare_filters('supplier_groups', $default_filters);

        // إعدادات pagination - استخدام current_page من الفلاتر المحفوظة فقط
        // إذا كان هناك page في URL، استخدمه وحدث الفلاتر (للتوافق مع pagination links)
        $url_page = (int)($_GET['page'] ?? 0);
        if ($url_page > 0) {
            $page = max(1, $url_page);
            // تحديث current_page في الفلاتر المحفوظة
            $applied_filters['current_page'] = $page;
            save_filters_from_form('supplier_groups', $applied_filters);
        } else {
            // استخدام current_page من الفلاتر المحفوظة
            $page = max(1, (int)($applied_filters['current_page'] ?? 1));
        }

        $perPage = max(10, min(100, (int)($applied_filters['per_page'] ?? 20))); // بين 10 و 100
        $offset = ($page - 1) * $perPage;

        // الحصول على الفلاتر للاستعلام
        $filters = [
            'search' => $applied_filters['search'] ?? '',
            'limit' => $perPage,
            'offset' => $offset
        ];

        // الحصول على مجموعات الموردين مع pagination
        $supplierGroups = $this->supplierGroupModel->getByCompany($company_id, $filters);

        // الحصول على العدد الكلي للمجموعات (بدون pagination)
        $filtersForCount = $filters;
        unset($filtersForCount['limit'], $filtersForCount['offset']);
        $totalGroups = $this->supplierGroupModel->getCountByCompany($company_id, $filtersForCount);

        // حساب معلومات pagination
        $totalPages = ceil($totalGroups / $perPage);
        $pagination = [
            'current_page' => $page,
            'per_page' => $perPage,
            'total_items' => $totalGroups,
            'total_pages' => $totalPages,
            'has_previous' => $page > 1,
            'has_next' => $page < $totalPages,
            'previous_page' => $page > 1 ? $page - 1 : null,
            'next_page' => $page < $totalPages ? $page + 1 : null,
            'start_item' => $totalGroups > 0 ? $offset + 1 : 0,
            'end_item' => min($offset + $perPage, $totalGroups)
        ];

        // الحصول على الإحصائيات
        $stats = $this->supplierGroupModel->getStats($company_id);

        // عرض الصفحة
        $data = [
            'title' => 'مجموعات الموردين',
            'supplierGroups' => $supplierGroups,
            'stats' => $stats,
            'pagination' => $pagination,
            'filters' => $applied_filters,
            'breadcrumb' => [
                ['title' => 'المشتريات', 'url' => base_url('purchases')],
                ['title' => 'مجموعات الموردين', 'active' => true]
            ]
        ];

        view('Purchases::supplier-groups/index', $data);
    }

    /**
     * عرض صفحة إضافة مجموعة موردين جديدة
     */
    public function create()
    {
        $data = [
            'title' => 'إضافة مجموعة موردين جديدة',
            'breadcrumb' => [
                ['title' => 'المشتريات', 'url' => base_url('purchases')],
                ['title' => 'مجموعات الموردين', 'url' => base_url('purchases/supplier-groups')],
                ['title' => 'إضافة مجموعة', 'active' => true]
            ]
        ];

        view('Purchases::supplier-groups/create', $data);
    }

    /**
     * حفظ مجموعة موردين جديدة
     */
    public function store()
    {
        try {
            // التحقق من صحة البيانات
            $validatedData = $this->validateSupplierGroupData($_POST);

            // إضافة معلومات المستخدم والشركة
            $company_id = current_user()['current_company_id'];
            $validatedData['company_id'] = $company_id;
            $validatedData['created_by'] = current_user()['UserID'];

            // إنشاء المجموعة
            $groupNumber = $this->supplierGroupModel->create($validatedData);

            if ($groupNumber) {
                // إذا تم تعيين هذه المجموعة كافتراضية، تحديث الإعدادات
                if ($validatedData['is_default'] == 1) {
                    $this->supplierGroupModel->setAsDefault($groupNumber, $company_id);
                }

                flash('success', 'تم إنشاء مجموعة الموردين بنجاح');
                redirect(base_url('purchases/supplier-groups'));
            } else {
                flash('error', 'حدث خطأ أثناء إنشاء مجموعة الموردين');
                redirect(base_url('purchases/supplier-groups/create'));
            }

        } catch (Exception $e) {
            flash('error', $e->getMessage());
            redirect(base_url('purchases/supplier-groups/create'));
        }
    }

    /**
     * عرض تفاصيل مجموعة موردين
     */
    public function show()
    {
        $group_number = $this->params['id'];
        $company_id = current_user()['current_company_id'];

        $supplierGroup = $this->supplierGroupModel->getByNumber($group_number, $company_id);

        if (!$supplierGroup) {
            flash('error', 'مجموعة الموردين غير موجودة');
            redirect(base_url('purchases/supplier-groups'));
        }

        $data = [
            'title' => $supplierGroup['name_ar'],
            'supplierGroup' => $supplierGroup,
            'breadcrumb' => [
                ['title' => 'المشتريات', 'url' => base_url('purchases')],
                ['title' => 'مجموعات الموردين', 'url' => base_url('purchases/supplier-groups')],
                ['title' => $supplierGroup['name_ar'], 'active' => true]
            ]
        ];

        view('Purchases::supplier-groups/show', $data);
    }

    /**
     * عرض صفحة تعديل مجموعة موردين
     */
    public function edit()
    {
        $group_number = $this->params['id'];
        $company_id = current_user()['current_company_id'];

        $supplierGroup = $this->supplierGroupModel->getByNumber($group_number, $company_id);

        if (!$supplierGroup) {
            flash('error', 'مجموعة الموردين غير موجودة');
            redirect(base_url('purchases/supplier-groups'));
        }

        $data = [
            'title' => 'تعديل المجموعة - ' . $supplierGroup['name_ar'],
            'supplierGroup' => $supplierGroup,
            'breadcrumb' => [
                ['title' => 'المشتريات', 'url' => base_url('purchases')],
                ['title' => 'مجموعات الموردين', 'url' => base_url('purchases/supplier-groups')],
                ['title' => 'تعديل المجموعة', 'active' => true]
            ]
        ];

        view('Purchases::supplier-groups/edit', $data);
    }

    /**
     * تحديث مجموعة موردين
     */
    public function update()
    {
        try {
            $group_number = $this->params['id'];
            $company_id = current_user()['current_company_id'];

            // التحقق من وجود المجموعة
            $supplierGroup = $this->supplierGroupModel->getByNumber($group_number, $company_id);
            if (!$supplierGroup) {
                flash('error', 'مجموعة الموردين غير موجودة');
                redirect(base_url('purchases/supplier-groups'));
            }

            // التحقق من صحة البيانات
            $validatedData = $this->validateSupplierGroupData($_POST);
            $validatedData['updated_by'] = current_user()['UserID'];

            // إذا تم تعيين هذه المجموعة كافتراضية، إزالة الافتراضية من المجموعات الأخرى
            if ($validatedData['is_default'] == 1) {
                $this->supplierGroupModel->setAsDefault($group_number, $company_id);
                // إزالة is_default من البيانات لأنه تم معالجته بشكل منفصل
                unset($validatedData['is_default']);
            }

            // تحديث المجموعة
            $result = $this->supplierGroupModel->update($group_number, $validatedData, $company_id);

            if ($result) {
                flash('success', 'تم تحديث مجموعة الموردين بنجاح');
                redirect(base_url('purchases/supplier-groups'));
            } else {
                flash('error', 'حدث خطأ أثناء تحديث مجموعة الموردين');
                redirect(base_url('purchases/supplier-groups/' . $group_number . '/edit'));
            }

        } catch (Exception $e) {
            flash('error', $e->getMessage());
            redirect(base_url('purchases/supplier-groups/' . $this->params['id'] . '/edit'));
        }
    }

    /**
     * حذف مجموعة موردين
     */
    public function delete()
    {
        try {
            $group_number = $this->params['id'];
            $company_id = current_user()['current_company_id'];

            // التحقق من وجود المجموعة
            $supplierGroup = $this->supplierGroupModel->getByNumber($group_number, $company_id);
            if (!$supplierGroup) {
                flash('error', 'مجموعة الموردين غير موجودة');
                redirect(base_url('purchases/supplier-groups'));
            }

            // حذف المجموعة
            $result = $this->supplierGroupModel->delete($group_number, $company_id);

            if ($result) {
                flash('success', 'تم حذف مجموعة الموردين بنجاح');
            } else {
                flash('error', 'حدث خطأ أثناء حذف مجموعة الموردين');
            }

        } catch (Exception $e) {
            flash('error', $e->getMessage());
        }

        redirect(base_url('purchases/supplier-groups'));
    }

    /**
     * تطبيق فلاتر مجموعات الموردين (POST form submission)
     */
    public function applyFilters()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect(base_url('purchases/supplier-groups'));
            return;
        }

        // الحصول على بيانات الفلاتر من POST
        $filter_data = [
            'search' => $_POST['search'] ?? '',
            'per_page' => $_POST['per_page'] ?? 20,
            'current_page' => 1  // إعادة تعيين إلى الصفحة الأولى عند تطبيق فلاتر جديدة
        ];

        // حفظ الفلاتر
        $success = save_filters_from_form('supplier_groups', $filter_data);

        if ($success) {
            flash('success', 'تم تطبيق الفلاتر بنجاح');
        } else {
            flash('error', 'فشل في حفظ الفلاتر');
        }

        // إعادة توجيه إلى صفحة مجموعات الموردين
        redirect(base_url('purchases/supplier-groups'));
    }

    /**
     * مسح فلاتر مجموعات الموردين
     */
    public function clearFilters()
    {
        // مسح الفلاتر المحفوظة للمستخدم
        clear_page_filters('supplier_groups');

        // إعادة توجيه إلى صفحة مجموعات الموردين بدون فلاتر (URL نظيف)
        redirect(base_url('purchases/supplier-groups'));
    }

    /**
     * التحقق من صحة بيانات مجموعة الموردين
     */
    private function validateSupplierGroupData($data)
    {
        $errors = [];

        // التحقق من الاسم العربي
        if (empty($data['name_ar'])) {
            $errors[] = 'اسم المجموعة بالعربية مطلوب';
        }

        if (!empty($errors)) {
            throw new Exception(implode('<br>', $errors));
        }

        // إعداد البيانات
        return [
            'name_ar' => trim($data['name_ar']),
            'name_en' => trim($data['name_en'] ?? ''),
            'is_default' => isset($data['is_default']) && $data['is_default'] == '1' ? 1 : 0
        ];
    }
}
