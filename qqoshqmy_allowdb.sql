-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- مضيف: localhost:3306
-- وقت الجيل: 31 مايو 2025 الساعة 10:29
-- إصدار الخادم: 8.0.42-33
-- نسخة PHP: 8.3.20

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- قاعدة بيانات: `qqoshqmy_allowdb`
--

-- --------------------------------------------------------

--
-- بنية الجدول `activity_log`
--

CREATE TABLE `activity_log` (
  `log_id` int NOT NULL,
  `user_id` int NOT NULL,
  `company_id` int NOT NULL,
  `action_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `action_details` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `chat_attachments`
--

CREATE TABLE `chat_attachments` (
  `id` int NOT NULL,
  `message_id` int NOT NULL,
  `file_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_path` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_size` bigint NOT NULL,
  `file_type` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `mime_type` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `thumbnail_path` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `chat_conversations`
--

CREATE TABLE `chat_conversations` (
  `conversation_id` int NOT NULL,
  `company_id` int NOT NULL,
  `conversation_type` enum('private','group') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'private',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `created_by` int NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_active` tinyint(1) NOT NULL DEFAULT '1'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `chat_conversations`
--

INSERT INTO `chat_conversations` (`conversation_id`, `company_id`, `conversation_type`, `title`, `created_by`, `created_at`, `updated_at`, `is_active`) VALUES
(1, 1, 'private', '', 32, '2025-05-24 21:13:44', '2025-05-24 22:38:03', 1);

-- --------------------------------------------------------

--
-- بنية الجدول `chat_conversation_participants`
--

CREATE TABLE `chat_conversation_participants` (
  `participant_id` int NOT NULL,
  `conversation_id` int NOT NULL,
  `user_id` int NOT NULL,
  `joined_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `is_admin` tinyint(1) NOT NULL DEFAULT '0',
  `last_read_message_id` int DEFAULT NULL,
  `is_muted` tinyint(1) NOT NULL DEFAULT '0',
  `left_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `chat_conversation_participants`
--

INSERT INTO `chat_conversation_participants` (`participant_id`, `conversation_id`, `user_id`, `joined_at`, `is_admin`, `last_read_message_id`, `is_muted`, `left_at`) VALUES
(1, 1, 32, '2025-05-24 21:13:44', 1, 6, 0, NULL),
(2, 1, 35, '2025-05-24 21:13:44', 0, 4, 0, NULL);

-- --------------------------------------------------------

--
-- بنية الجدول `chat_messages`
--

CREATE TABLE `chat_messages` (
  `message_id` int NOT NULL,
  `conversation_id` int NOT NULL,
  `sender_id` int NOT NULL,
  `message_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `message_type` enum('text','image','file','system') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'text',
  `file_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `file_size` int DEFAULT NULL,
  `file_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `is_edited` tinyint(1) NOT NULL DEFAULT '0',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0',
  `reply_to_message_id` int DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `chat_messages`
--

INSERT INTO `chat_messages` (`message_id`, `conversation_id`, `sender_id`, `message_text`, `message_type`, `file_url`, `file_name`, `file_size`, `file_type`, `is_edited`, `is_deleted`, `reply_to_message_id`, `created_at`, `updated_at`) VALUES
(1, 1, 32, 'g', 'text', NULL, NULL, NULL, NULL, 0, 1, NULL, '2025-05-24 22:16:25', '2025-05-24 22:20:22'),
(2, 1, 35, 'ب', 'text', NULL, NULL, NULL, NULL, 0, 0, NULL, '2025-05-24 22:17:14', '2025-05-24 22:17:14'),
(3, 1, 32, 'مين معي', 'text', NULL, NULL, NULL, NULL, 0, 0, NULL, '2025-05-24 22:17:34', '2025-05-24 22:17:34'),
(4, 1, 32, 'لال', 'text', NULL, NULL, NULL, NULL, 0, 0, NULL, '2025-05-24 22:23:01', '2025-05-24 22:23:01'),
(5, 1, 35, 'ياهلابك', 'text', NULL, NULL, NULL, NULL, 0, 0, NULL, '2025-05-24 22:30:22', '2025-05-24 22:30:22'),
(6, 1, 32, 'بببب', 'text', NULL, NULL, NULL, NULL, 0, 0, NULL, '2025-05-24 22:38:03', '2025-05-24 22:38:03');

-- --------------------------------------------------------

--
-- بنية الجدول `chat_message_reactions`
--

CREATE TABLE `chat_message_reactions` (
  `reaction_id` int NOT NULL,
  `message_id` int NOT NULL,
  `user_id` int NOT NULL,
  `reaction_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `chat_message_reads`
--

CREATE TABLE `chat_message_reads` (
  `read_id` int NOT NULL,
  `message_id` int NOT NULL,
  `user_id` int NOT NULL,
  `read_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `chat_notifications`
--

CREATE TABLE `chat_notifications` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `conversation_id` int NOT NULL,
  `message_id` int DEFAULT NULL,
  `type` enum('new_message','mention','file_shared','user_joined','user_left') COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` text COLLATE utf8mb4_unicode_ci,
  `is_read` tinyint(1) NOT NULL DEFAULT '0',
  `is_sent` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `read_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `chat_notification_settings`
--

CREATE TABLE `chat_notification_settings` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `conversation_id` int DEFAULT NULL,
  `email_notifications` tinyint(1) NOT NULL DEFAULT '1',
  `push_notifications` tinyint(1) NOT NULL DEFAULT '1',
  `sound_notifications` tinyint(1) NOT NULL DEFAULT '1',
  `desktop_notifications` tinyint(1) NOT NULL DEFAULT '1',
  `mention_notifications` tinyint(1) NOT NULL DEFAULT '1',
  `mute_until` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `chat_typing_status`
--

CREATE TABLE `chat_typing_status` (
  `id` int NOT NULL,
  `conversation_id` int NOT NULL,
  `user_id` int NOT NULL,
  `is_typing` tinyint(1) NOT NULL DEFAULT '0',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `chat_user_status`
--

CREATE TABLE `chat_user_status` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `status` enum('online','offline','away') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'offline',
  `last_seen` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `companies`
--

CREATE TABLE `companies` (
  `CompanyID` int NOT NULL,
  `OwnerID` int NOT NULL,
  `CompanyName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `CompanyNameEN` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `CompanyEmail` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `CompanyPhone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `CompanyLogo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `CompanyStatus` enum('Active','Inactive') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'Active',
  `CompanyAddress` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `CompanyWebsite` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `TaxID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `industry_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `company_size` enum('1-10','11-50','51-200','201-500','501-1000','1000+') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `foundation_date` date DEFAULT NULL,
  `social_media_links` json DEFAULT NULL,
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `subscription_id` int DEFAULT NULL,
  `subscription_status` enum('Trial','Active','Expired','Canceled','Pending') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'Trial',
  `trial_start_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `trial_end_date` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deactivation_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `deactivation_date` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `companies`
--

INSERT INTO `companies` (`CompanyID`, `OwnerID`, `CompanyName`, `CompanyNameEN`, `CompanyEmail`, `CompanyPhone`, `CompanyLogo`, `CompanyStatus`, `CompanyAddress`, `CompanyWebsite`, `TaxID`, `industry_type`, `company_size`, `foundation_date`, `social_media_links`, `notes`, `subscription_id`, `subscription_status`, `trial_start_date`, `trial_end_date`, `created_at`, `updated_at`, `deactivation_reason`, `deactivation_date`) VALUES
(4, 32, 'مؤسسة شغف المبتكر', 'مؤسسة شغف المبتكر', '<EMAIL>', '0*********', 'uploads/company_logos/1748176469_شعار سامي.png', 'Active', '', '', '', 'retail', '11-50', NULL, NULL, '', 80, 'Trial', '2025-05-24 10:00:48', '2025-06-07 19:00:48', '2025-05-24 10:00:48', '2025-05-27 08:31:58', NULL, NULL);

-- --------------------------------------------------------

--
-- بنية الجدول `company_modules`
--

CREATE TABLE `company_modules` (
  `id` int NOT NULL,
  `company_id` int NOT NULL,
  `module_id` int NOT NULL,
  `installed_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `installed_by` int NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `license_expires_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- إرجاع أو استيراد بيانات الجدول `company_modules`
--

INSERT INTO `company_modules` (`id`, `company_id`, `module_id`, `installed_at`, `installed_by`, `is_active`, `license_expires_at`) VALUES
(1, 4, 1, '2025-05-28 18:59:59', 32, 1, '2026-01-01 06:59:59'),
(2, 4, 2, '2025-05-28 18:59:59', 32, 1, '2026-01-01 06:59:59'),
(3, 4, 3, '2025-05-28 18:59:59', 32, 1, '2026-01-01 06:59:59'),
(4, 4, 4, '2025-05-28 18:59:59', 32, 1, '2026-01-01 06:59:59'),
(5, 4, 6, '2025-05-28 18:59:59', 32, 1, '2026-01-01 06:59:59');

-- --------------------------------------------------------

--
-- بنية الجدول `company_users`
--

CREATE TABLE `company_users` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `company_id` int NOT NULL,
  `position_id` int DEFAULT NULL,
  `added_by` int NOT NULL,
  `status` enum('pending','accepted','rejected') DEFAULT 'pending',
  `user_status` enum('active','inactive','suspended') DEFAULT 'active',
  `joined_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `notes` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- إرجاع أو استيراد بيانات الجدول `company_users`
--

INSERT INTO `company_users` (`id`, `user_id`, `company_id`, `position_id`, `added_by`, `status`, `user_status`, `joined_at`, `notes`) VALUES
(1, 35, 4, 2, 32, 'accepted', 'active', '2025-05-28 18:59:59', 'مدير المخزون - تم إضافته من قبل مالك الشركة');

-- --------------------------------------------------------

--
-- بنية الجدول `inventory_audits`
--

CREATE TABLE `inventory_audits` (
  `audit_id` int NOT NULL,
  `company_id` int NOT NULL,
  `module_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'inventory',
  `audit_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `audit_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `warehouse_id` int NOT NULL,
  `audit_date` date NOT NULL,
  `status` enum('planned','in_progress','completed','cancelled') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'planned',
  `audit_type` enum('full','partial','cycle') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'full',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `total_items_counted` int DEFAULT '0',
  `total_discrepancies` int DEFAULT '0',
  `total_value_difference` decimal(15,2) DEFAULT '0.00',
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `completed_by` int DEFAULT NULL,
  `completed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `inventory_audit_items`
--

CREATE TABLE `inventory_audit_items` (
  `audit_item_id` int NOT NULL,
  `company_id` int NOT NULL,
  `module_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'inventory',
  `audit_id` int NOT NULL,
  `product_id` int NOT NULL,
  `system_quantity` decimal(15,3) NOT NULL DEFAULT '0.000',
  `counted_quantity` decimal(15,3) DEFAULT NULL,
  `difference_quantity` decimal(15,3) DEFAULT '0.000',
  `unit_cost` decimal(15,2) DEFAULT '0.00',
  `difference_value` decimal(15,2) DEFAULT '0.00',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `counted_by` int DEFAULT NULL,
  `counted_at` timestamp NULL DEFAULT NULL,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `inventory_categories`
--

CREATE TABLE `inventory_categories` (
  `category_id` int NOT NULL,
  `company_id` int NOT NULL,
  `module_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'inventory',
  `parent_category_id` int DEFAULT NULL,
  `category_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `category_name_ar` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `category_name_en` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `description_ar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `description_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `image_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `display_order` int NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `inventory_categories`
--

INSERT INTO `inventory_categories` (`category_id`, `company_id`, `module_code`, `parent_category_id`, `category_code`, `category_name_ar`, `category_name_en`, `description_ar`, `description_en`, `image_url`, `display_order`, `is_active`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 4, 'inventory', NULL, 'ELECTRONICS', 'إلكترونيات', 'Electronics', NULL, NULL, NULL, 1, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(2, 4, 'inventory', NULL, 'COMPUTERS', 'حاسوب وملحقاته', 'Computers & Accessories', NULL, NULL, NULL, 2, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(3, 4, 'inventory', NULL, 'MOBILE', 'هواتف ذكية', 'Mobile Phones', NULL, NULL, NULL, 3, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(4, 4, 'inventory', NULL, 'FURNITURE', 'أثاث', 'Furniture', NULL, NULL, NULL, 4, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(5, 4, 'inventory', NULL, 'OFFICE_FURNITURE', 'أثاث مكتبي', 'Office Furniture', NULL, NULL, NULL, 5, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(6, 4, 'inventory', NULL, 'OFFICE_SUPPLIES', 'مستلزمات مكتبية', 'Office Supplies', NULL, NULL, NULL, 6, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(7, 4, 'inventory', NULL, 'STATIONERY', 'قرطاسية', 'Stationery', NULL, NULL, NULL, 7, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(8, 4, 'inventory', NULL, 'BOOKS', 'كتب', 'Books', NULL, NULL, NULL, 8, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(9, 4, 'inventory', NULL, 'FOOD_BEVERAGE', 'أطعمة ومشروبات', 'Food & Beverage', NULL, NULL, NULL, 9, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(10, 4, 'inventory', NULL, 'CLEANING', 'مواد تنظيف', 'Cleaning Supplies', NULL, NULL, NULL, 10, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(11, 4, 'inventory', NULL, 'TOOLS', 'أدوات', 'Tools', NULL, NULL, NULL, 11, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(12, 4, 'inventory', NULL, 'AUTOMOTIVE', 'قطع غيار سيارات', 'Automotive Parts', NULL, NULL, NULL, 12, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(13, 4, 'inventory', NULL, 'CLOTHING', 'ملابس', 'Clothing', NULL, NULL, NULL, 13, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(14, 4, 'inventory', NULL, 'MEDICAL', 'مستلزمات طبية', 'Medical Supplies', NULL, NULL, NULL, 14, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(15, 4, 'inventory', NULL, 'SPORTS', 'مستلزمات رياضية', 'Sports Equipment', NULL, NULL, NULL, 15, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33');

-- --------------------------------------------------------

--
-- بنية الجدول `inventory_movements`
--

CREATE TABLE `inventory_movements` (
  `movement_id` int NOT NULL,
  `company_id` int NOT NULL,
  `module_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'inventory',
  `movement_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `product_id` int NOT NULL,
  `warehouse_id` int NOT NULL,
  `movement_type` enum('in','out','transfer','adjustment') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `movement_reason` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `quantity` decimal(15,3) NOT NULL,
  `unit_cost` decimal(15,2) DEFAULT '0.00',
  `total_cost` decimal(15,2) DEFAULT '0.00',
  `reference_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `reference_id` int DEFAULT NULL,
  `reference_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `movement_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `inventory_products`
--

CREATE TABLE `inventory_products` (
  `product_id` int NOT NULL,
  `company_id` int NOT NULL,
  `module_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'inventory',
  `product_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `barcode` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `product_name_ar` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `product_name_en` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `description_ar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `description_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `category_id` int NOT NULL,
  `unit_id` int NOT NULL,
  `product_type` enum('product','service','digital') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'product',
  `track_inventory` tinyint(1) NOT NULL DEFAULT '1',
  `cost_price` decimal(15,2) DEFAULT '0.00',
  `selling_price` decimal(15,2) DEFAULT '0.00',
  `min_stock_level` decimal(15,2) DEFAULT '0.00',
  `max_stock_level` decimal(15,2) DEFAULT NULL,
  `reorder_point` decimal(15,2) DEFAULT '0.00',
  `weight` decimal(10,3) DEFAULT NULL,
  `dimensions` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `image_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `tax_rate` decimal(5,2) DEFAULT '0.00',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `inventory_products`
--

INSERT INTO `inventory_products` (`product_id`, `company_id`, `module_code`, `product_code`, `barcode`, `product_name_ar`, `product_name_en`, `description_ar`, `description_en`, `category_id`, `unit_id`, `product_type`, `track_inventory`, `cost_price`, `selling_price`, `min_stock_level`, `max_stock_level`, `reorder_point`, `weight`, `dimensions`, `image_url`, `tax_rate`, `is_active`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 4, 'inventory', 'LAPTOP001', '1234567890123', 'لابتوب ديل انسبايرون', 'Dell Inspiron Laptop', NULL, NULL, 2, 7, 'product', 1, 2500.00, 3200.00, 5.00, NULL, 10.00, NULL, NULL, NULL, 0.00, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(2, 4, 'inventory', 'MOUSE001', '1234567890124', 'فأرة لاسلكية', 'Wireless Mouse', NULL, NULL, 2, 7, 'product', 1, 25.00, 45.00, 20.00, NULL, 30.00, NULL, NULL, NULL, 0.00, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(3, 4, 'inventory', 'KEYBOARD001', '1234567890125', 'لوحة مفاتيح عربية', 'Arabic Keyboard', NULL, NULL, 2, 7, 'product', 1, 35.00, 60.00, 15.00, NULL, 25.00, NULL, NULL, NULL, 0.00, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(4, 4, 'inventory', 'CHAIR001', '1234567890126', 'كرسي مكتب جلد', 'Leather Office Chair', NULL, NULL, 5, 7, 'product', 1, 180.00, 280.00, 8.00, NULL, 15.00, NULL, NULL, NULL, 0.00, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(5, 4, 'inventory', 'DESK001', '1234567890127', 'مكتب خشبي', 'Wooden Desk', NULL, NULL, 5, 7, 'product', 1, 350.00, 550.00, 5.00, NULL, 10.00, NULL, NULL, NULL, 0.00, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(6, 4, 'inventory', 'PEN001', '1234567890128', 'قلم حبر أزرق', 'Blue Ink Pen', NULL, NULL, 7, 7, 'product', 1, 1.50, 3.00, 100.00, NULL, 200.00, NULL, NULL, NULL, 0.00, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(7, 4, 'inventory', 'PAPER001', '1234567890129', 'ورق A4', 'A4 Paper', NULL, NULL, 7, 16, 'product', 1, 15.00, 25.00, 50.00, NULL, 100.00, NULL, NULL, NULL, 0.00, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(8, 4, 'inventory', 'NOTEBOOK001', '1234567890130', 'دفتر ملاحظات', 'Notebook', NULL, NULL, 7, 7, 'product', 1, 8.00, 15.00, 30.00, NULL, 50.00, NULL, NULL, NULL, 0.00, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(9, 4, 'inventory', 'DETERGENT001', '1234567890131', 'مسحوق غسيل', 'Laundry Detergent', NULL, NULL, 10, 8, 'product', 1, 12.00, 20.00, 25.00, NULL, 40.00, NULL, NULL, NULL, 0.00, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(10, 4, 'inventory', 'DISINFECTANT001', '1234567890132', 'مطهر', 'Disinfectant', NULL, NULL, 10, 10, 'product', 1, 8.00, 15.00, 20.00, NULL, 35.00, NULL, NULL, NULL, 0.00, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(13, 4, 'inventory', '101', '1010101', 'منتج تست', 'منتج تست', NULL, NULL, 12, 16, 'product', 1, 100.00, 250.00, 0.00, NULL, 0.00, NULL, NULL, NULL, 15.00, 1, 32, NULL, '2025-05-28 12:58:26', '2025-05-28 12:58:26');

-- --------------------------------------------------------

--
-- بنية الجدول `inventory_product_prices`
--

CREATE TABLE `inventory_product_prices` (
  `price_id` int NOT NULL,
  `company_id` int NOT NULL,
  `module_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'inventory',
  `product_id` int NOT NULL,
  `price_type` enum('cost','selling','wholesale','retail','special') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `price` decimal(15,2) NOT NULL,
  `currency` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'SAR',
  `min_quantity` decimal(15,3) DEFAULT '1.000',
  `max_quantity` decimal(15,3) DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `inventory_adjustments`
--

CREATE TABLE `inventory_adjustments` (
  `adjustment_id` int NOT NULL,
  `company_id` int NOT NULL,
  `module_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'inventory',
  `adjustment_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `adjustment_date` date NOT NULL,
  `warehouse_id` int NOT NULL,
  `adjustment_type` enum('increase','decrease','correction') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `reference_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `total_items` int DEFAULT '0',
  `total_value` decimal(15,2) DEFAULT '0.00',
  `status` enum('draft','approved','cancelled') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'draft',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `approved_by` int DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `inventory_adjustment_items`
--

CREATE TABLE `inventory_adjustment_items` (
  `adjustment_item_id` int NOT NULL,
  `company_id` int NOT NULL,
  `module_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'inventory',
  `adjustment_id` int NOT NULL,
  `product_id` int NOT NULL,
  `current_quantity` decimal(15,3) NOT NULL DEFAULT '0.000',
  `adjusted_quantity` decimal(15,3) NOT NULL DEFAULT '0.000',
  `difference_quantity` decimal(15,3) NOT NULL DEFAULT '0.000',
  `unit_cost` decimal(15,2) DEFAULT '0.00',
  `total_cost` decimal(15,2) DEFAULT '0.00',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `inventory_locations`
--

CREATE TABLE `inventory_locations` (
  `location_id` int NOT NULL,
  `company_id` int NOT NULL,
  `module_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'inventory',
  `warehouse_id` int NOT NULL,
  `location_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `location_name_ar` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `location_name_en` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `location_type` enum('zone','aisle','shelf','bin') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'bin',
  `parent_location_id` int DEFAULT NULL,
  `capacity` decimal(15,2) DEFAULT NULL,
  `current_usage` decimal(15,2) DEFAULT '0.00',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `inventory_stock`
--

CREATE TABLE `inventory_stock` (
  `stock_id` int NOT NULL,
  `company_id` int NOT NULL,
  `module_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'inventory',
  `product_id` int NOT NULL,
  `warehouse_id` int NOT NULL,
  `location_id` int DEFAULT NULL,
  `quantity_on_hand` decimal(15,3) NOT NULL DEFAULT '0.000',
  `quantity_reserved` decimal(15,3) NOT NULL DEFAULT '0.000',
  `quantity_available` decimal(15,3) NOT NULL DEFAULT '0.000',
  `average_cost` decimal(15,2) DEFAULT '0.00',
  `last_cost` decimal(15,2) DEFAULT '0.00',
  `last_movement_date` timestamp NULL DEFAULT NULL,
  `expiry_date` date DEFAULT NULL,
  `batch_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `serial_numbers` json DEFAULT NULL,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `inventory_stock`
--

INSERT INTO `inventory_stock` (`stock_id`, `company_id`, `module_code`, `product_id`, `warehouse_id`, `quantity_on_hand`, `quantity_reserved`, `quantity_available`, `average_cost`, `last_cost`, `last_movement_date`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 4, 'inventory', 1, 1, 15.000, 0.000, 15.000, 2500.00, 0.00, NULL, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(2, 4, 'inventory', 2, 1, 50.000, 0.000, 50.000, 25.00, 0.00, NULL, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(3, 4, 'inventory', 3, 1, 30.000, 0.000, 30.000, 35.00, 0.00, NULL, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(4, 4, 'inventory', 4, 1, 25.000, 0.000, 25.000, 180.00, 0.00, NULL, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(5, 4, 'inventory', 5, 1, 12.000, 0.000, 12.000, 350.00, 0.00, NULL, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(6, 4, 'inventory', 6, 1, 500.000, 0.000, 500.000, 1.50, 0.00, NULL, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(7, 4, 'inventory', 7, 1, 100.000, 0.000, 100.000, 15.00, 0.00, NULL, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(8, 4, 'inventory', 8, 1, 80.000, 0.000, 80.000, 8.00, 0.00, NULL, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(9, 4, 'inventory', 9, 1, 60.000, 0.000, 60.000, 12.00, 0.00, NULL, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(10, 4, 'inventory', 10, 1, 40.000, 0.000, 40.000, 8.00, 0.00, NULL, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33');

-- --------------------------------------------------------

--
-- بنية الجدول `inventory_transfers`
--

CREATE TABLE `inventory_transfers` (
  `transfer_id` int NOT NULL,
  `company_id` int NOT NULL,
  `module_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'inventory',
  `transfer_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `from_warehouse_id` int NOT NULL,
  `to_warehouse_id` int NOT NULL,
  `transfer_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` enum('pending','in_transit','completed','cancelled') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'pending',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `total_items` int DEFAULT '0',
  `total_quantity` decimal(15,3) DEFAULT '0.000',
  `total_value` decimal(15,2) DEFAULT '0.00',
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `approved_by` int DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `inventory_transfer_items`
--

CREATE TABLE `inventory_transfer_items` (
  `transfer_item_id` int NOT NULL,
  `company_id` int NOT NULL,
  `module_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'inventory',
  `transfer_id` int NOT NULL,
  `product_id` int NOT NULL,
  `quantity_requested` decimal(15,3) NOT NULL,
  `quantity_sent` decimal(15,3) DEFAULT '0.000',
  `quantity_received` decimal(15,3) DEFAULT '0.000',
  `unit_cost` decimal(15,2) DEFAULT '0.00',
  `total_cost` decimal(15,2) DEFAULT '0.00',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `inventory_units`
--

CREATE TABLE `inventory_units` (
  `unit_id` int NOT NULL,
  `company_id` int NOT NULL,
  `module_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'inventory',
  `unit_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `unit_name_ar` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `unit_name_en` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `unit_symbol_ar` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `unit_symbol_en` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `base_unit_id` int DEFAULT NULL,
  `conversion_factor` decimal(10,4) DEFAULT '1.0000',
  `is_base_unit` tinyint(1) NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `inventory_units`
--

INSERT INTO `inventory_units` (`unit_id`, `company_id`, `module_code`, `unit_code`, `unit_name_ar`, `unit_name_en`, `unit_symbol_ar`, `unit_symbol_en`, `base_unit_id`, `conversion_factor`, `is_base_unit`, `is_active`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(7, 4, 'inventory', 'PIECE', 'قطعة', 'Piece', 'قطعة', 'pcs', NULL, 1.0000, 1, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(8, 4, 'inventory', 'KG', 'كيلوجرام', 'Kilogram', 'كجم', 'kg', NULL, 1.0000, 1, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(9, 4, 'inventory', 'GRAM', 'جرام', 'Gram', 'جم', 'g', NULL, 1.0000, 0, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(10, 4, 'inventory', 'LITER', 'لتر', 'Liter', 'لتر', 'L', NULL, 1.0000, 1, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(11, 4, 'inventory', 'ML', 'مليلتر', 'Milliliter', 'مل', 'ml', NULL, 1.0000, 0, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(12, 4, 'inventory', 'METER', 'متر', 'Meter', 'م', 'm', NULL, 1.0000, 1, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(13, 4, 'inventory', 'CM', 'سنتيمتر', 'Centimeter', 'سم', 'cm', NULL, 1.0000, 0, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(14, 4, 'inventory', 'BOX', 'صندوق', 'Box', 'صندوق', 'box', NULL, 1.0000, 0, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(15, 4, 'inventory', 'CARTON', 'كرتون', 'Carton', 'كرتون', 'carton', NULL, 1.0000, 0, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(16, 4, 'inventory', 'PACK', 'عبوة', 'Pack', 'عبوة', 'pack', NULL, 1.0000, 0, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(17, 4, 'inventory', 'DOZEN', 'دزينة', 'Dozen', 'دزينة', 'dz', NULL, 1.0000, 0, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(18, 4, 'inventory', 'SET', 'طقم', 'Set', 'طقم', 'set', NULL, 1.0000, 0, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33');

-- --------------------------------------------------------

--
-- بنية الجدول `inventory_warehouses`
--

CREATE TABLE `inventory_warehouses` (
  `warehouse_id` int NOT NULL,
  `company_id` int NOT NULL,
  `module_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'inventory',
  `warehouse_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `warehouse_name_ar` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `warehouse_name_en` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `address` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `manager_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `capacity` decimal(15,2) DEFAULT NULL,
  `current_usage` decimal(15,2) DEFAULT '0.00',
  `warehouse_type` enum('main','branch','virtual','external') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'main',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `inventory_warehouses`
--

INSERT INTO `inventory_warehouses` (`warehouse_id`, `company_id`, `module_code`, `warehouse_code`, `warehouse_name_ar`, `warehouse_name_en`, `address`, `phone`, `email`, `manager_name`, `capacity`, `current_usage`, `warehouse_type`, `is_active`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 4, 'inventory', 'MAIN_WH', 'المخزن الرئيسي', 'Main Warehouse', 'الرياض، المملكة العربية السعودية', NULL, NULL, NULL, 10000.00, 0.00, 'main', 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(2, 4, 'inventory', 'BRANCH_RUH', 'مخزن فرع الرياض', 'Riyadh Branch Warehouse', 'الرياض، حي العليا', NULL, NULL, NULL, 5000.00, 0.00, 'branch', 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(3, 4, 'inventory', 'BRANCH_JED', 'مخزن فرع جدة', 'Jeddah Branch Warehouse', 'جدة، حي الروضة', NULL, NULL, NULL, 3000.00, 0.00, 'branch', 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(4, 4, 'inventory', 'BRANCH_DAM', 'مخزن فرع الدمام', 'Dammam Branch Warehouse', 'الدمام، حي الفيصلية', NULL, NULL, NULL, 2000.00, 0.00, 'branch', 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(5, 4, 'inventory', 'VIRTUAL_WH', 'المخزن الافتراضي', 'Virtual Warehouse', 'للمنتجات الرقمية والخدمات', NULL, NULL, NULL, NULL, 0.00, 'virtual', 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(6, 4, 'inventory', 'EXTERNAL_WH', 'المخزن الخارجي', 'External Warehouse', 'مخزن خارجي للتخزين المؤقت', NULL, NULL, NULL, 1000.00, 0.00, 'external', 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33');

--
-- إرجاع أو استيراد بيانات الجدول `inventory_locations`
--

INSERT INTO `inventory_locations` (`location_id`, `company_id`, `module_code`, `warehouse_id`, `location_code`, `location_name_ar`, `location_name_en`, `location_type`, `parent_location_id`, `capacity`, `current_usage`, `is_active`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 4, 'inventory', 1, 'ZONE-A', 'المنطقة أ', 'Zone A', 'zone', NULL, 1000.00, 0.00, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(2, 4, 'inventory', 1, 'AISLE-A1', 'ممر أ1', 'Aisle A1', 'aisle', 1, 200.00, 0.00, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(3, 4, 'inventory', 1, 'SHELF-A1-1', 'رف أ1-1', 'Shelf A1-1', 'shelf', 2, 50.00, 0.00, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(4, 4, 'inventory', 1, 'BIN-A1-1-1', 'صندوق أ1-1-1', 'Bin A1-1-1', 'bin', 3, 10.00, 0.00, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33'),
(5, 4, 'inventory', 1, 'BIN-A1-1-2', 'صندوق أ1-1-2', 'Bin A1-1-2', 'bin', 3, 10.00, 0.00, 1, 32, NULL, '2025-05-27 20:01:33', '2025-05-27 20:01:33');

-- --------------------------------------------------------

--
-- بنية الجدول `inventory_alerts`
--

CREATE TABLE `inventory_alerts` (
  `alert_id` int NOT NULL,
  `company_id` int NOT NULL,
  `module_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'inventory',
  `alert_type` enum('low_stock','out_of_stock','expiry_warning','overstock') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `product_id` int NOT NULL,
  `warehouse_id` int NOT NULL,
  `current_quantity` decimal(15,3) DEFAULT '0.000',
  `threshold_quantity` decimal(15,3) DEFAULT '0.000',
  `expiry_date` date DEFAULT NULL,
  `days_to_expiry` int DEFAULT NULL,
  `alert_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `severity` enum('info','warning','critical') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'warning',
  `is_read` tinyint(1) DEFAULT '0',
  `is_resolved` tinyint(1) DEFAULT '0',
  `resolved_by` int DEFAULT NULL,
  `resolved_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `inventory_reorder_requests`
--

CREATE TABLE `inventory_reorder_requests` (
  `request_id` int NOT NULL,
  `company_id` int NOT NULL,
  `module_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'inventory',
  `request_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `product_id` int NOT NULL,
  `warehouse_id` int NOT NULL,
  `current_stock` decimal(15,3) NOT NULL DEFAULT '0.000',
  `reorder_point` decimal(15,3) NOT NULL DEFAULT '0.000',
  `suggested_quantity` decimal(15,3) NOT NULL DEFAULT '0.000',
  `requested_quantity` decimal(15,3) DEFAULT NULL,
  `priority` enum('low','medium','high','urgent') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'medium',
  `status` enum('pending','approved','ordered','cancelled') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'pending',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `approved_by` int DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `inventory_reports`
--

CREATE TABLE `inventory_reports` (
  `report_id` int NOT NULL,
  `company_id` int NOT NULL,
  `module_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'inventory',
  `report_type` enum('stock_valuation','movement_summary','low_stock','expiry_report','abc_analysis') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `report_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `report_parameters` json DEFAULT NULL,
  `report_data` json DEFAULT NULL,
  `file_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `status` enum('generating','completed','failed') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'generating',
  `generated_by` int NOT NULL,
  `generated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `module_programs`
--

CREATE TABLE `module_programs` (
  `program_id` int NOT NULL,
  `company_id` int NOT NULL,
  `module_id` int NOT NULL,
  `parent_program_id` int DEFAULT NULL,
  `program_name_ar` varchar(100) NOT NULL,
  `program_name_en` varchar(100) NOT NULL,
  `program_code` varchar(50) NOT NULL,
  `page_url` varchar(255) DEFAULT NULL,
  `icon_name` varchar(50) DEFAULT NULL,
  `program_type` enum('Main','Sub') DEFAULT 'Sub',
  `display_order` int DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- إرجاع أو استيراد بيانات الجدول `module_programs`
--

INSERT INTO `module_programs` (`program_id`, `company_id`, `module_id`, `parent_program_id`, `program_name_ar`, `program_name_en`, `program_code`, `page_url`, `icon_name`, `program_type`, `display_order`, `is_active`, `created_at`) VALUES
(1, 4, 1, NULL, 'إدارة المخزون', 'Inventory Management', 'inventory_main', 'inventory', 'fas fa-boxes', 'Main', 1, 1, '2025-05-28 18:59:59'),
(2, 4, 1, 1, 'المنتجات', 'Products', 'products', 'inventory/products', 'fas fa-box', 'Sub', 11, 1, '2025-05-28 18:59:59'),
(3, 4, 1, 1, 'الفئات', 'Categories', 'categories', 'inventory/categories', 'fas fa-tags', 'Sub', 12, 1, '2025-05-28 18:59:59'),
(4, 4, 1, 1, 'وحدات القياس', 'Units', 'units', 'inventory/units', 'fas fa-ruler', 'Sub', 13, 1, '2025-05-28 18:59:59'),
(5, 4, 1, 1, 'المستودعات', 'Warehouses', 'warehouses', 'inventory/warehouses', 'fas fa-warehouse', 'Sub', 14, 1, '2025-05-28 18:59:59'),
(6, 4, 1, 1, 'حركة المخزون', 'Stock Movements', 'movements', 'inventory/movements', 'fas fa-exchange-alt', 'Sub', 15, 1, '2025-05-28 18:59:59'),
(27, 4, 1, 1, 'تسويات المخزون', 'Stock Adjustments', 'adjustments', 'inventory/adjustments', 'fas fa-balance-scale', 'Sub', 16, 1, '2025-05-28 18:59:59'),
(28, 4, 1, 1, 'تحويلات المخزون', 'Stock Transfers', 'transfers', 'inventory/transfers', 'fas fa-arrows-alt-h', 'Sub', 17, 1, '2025-05-28 18:59:59'),
(29, 4, 1, 1, 'جرد المخزون', 'Stock Audits', 'audits', 'inventory/audits', 'fas fa-clipboard-check', 'Sub', 18, 1, '2025-05-28 18:59:59'),
(30, 4, 1, 1, 'مواقع التخزين', 'Storage Locations', 'locations', 'inventory/locations', 'fas fa-map-marker-alt', 'Sub', 19, 1, '2025-05-28 18:59:59'),
(31, 4, 1, 1, 'طلبات إعادة الطلب', 'Reorder Requests', 'reorder_requests', 'inventory/reorder_requests', 'fas fa-redo', 'Sub', 20, 1, '2025-05-28 18:59:59'),
(32, 4, 1, 1, 'تنبيهات المخزون', 'Inventory Alerts', 'alerts', 'inventory/alerts', 'fas fa-bell', 'Sub', 21, 1, '2025-05-28 18:59:59'),
(33, 4, 1, 1, 'أسعار المنتجات', 'Product Prices', 'product_prices', 'inventory/product_prices', 'fas fa-tags', 'Sub', 22, 1, '2025-05-28 18:59:59'),
(7, 4, 2, NULL, 'إدارة المشتريات', 'Purchases Management', 'purchases_main', 'purchases', 'fas fa-shopping-cart', 'Main', 2, 1, '2025-05-28 18:59:59'),
(8, 4, 2, 7, 'طلبات الشراء', 'Purchase Orders', 'purchase_orders', 'purchases/orders', 'fas fa-file-alt', 'Sub', 21, 1, '2025-05-28 18:59:59'),
(9, 4, 2, 7, 'الموردين', 'Suppliers', 'suppliers', 'purchases/suppliers', 'fas fa-truck', 'Sub', 22, 1, '2025-05-28 18:59:59'),
(10, 4, 2, 7, 'استلام البضائع', 'Receiving', 'receiving', 'purchases/receiving', 'fas fa-dolly', 'Sub', 23, 1, '2025-05-28 18:59:59'),
(11, 4, 2, 7, 'فواتير الشراء', 'Purchase Invoices', 'purchase_invoices', 'purchases/invoices', 'fas fa-file-invoice', 'Sub', 24, 1, '2025-05-28 18:59:59'),
(12, 4, 3, NULL, 'إدارة المبيعات', 'Sales Management', 'sales_main', 'sales', 'fas fa-chart-line', 'Main', 3, 1, '2025-05-28 18:59:59'),
(13, 4, 3, 12, 'العملاء', 'Customers', 'customers', 'sales/customers', 'fas fa-users', 'Sub', 31, 1, '2025-05-28 18:59:59'),
(14, 4, 3, 12, 'عروض الأسعار', 'Quotations', 'quotations', 'sales/quotations', 'fas fa-file-contract', 'Sub', 32, 1, '2025-05-28 18:59:59'),
(15, 4, 3, 12, 'أوامر البيع', 'Sales Orders', 'sales_orders', 'sales/orders', 'fas fa-clipboard-list', 'Sub', 33, 1, '2025-05-28 18:59:59'),
(16, 4, 3, 12, 'فواتير المبيعات', 'Sales Invoices', 'sales_invoices', 'sales/invoices', 'fas fa-file-invoice-dollar', 'Sub', 34, 1, '2025-05-28 18:59:59'),
(17, 4, 4, NULL, 'المحاسبة', 'Accounting', 'accounting_main', 'accounting', 'fas fa-calculator', 'Main', 4, 1, '2025-05-28 18:59:59'),
(18, 4, 4, 17, 'دليل الحسابات', 'Chart of Accounts', 'chart_accounts', 'accounting/accounts', 'fas fa-list', 'Sub', 41, 1, '2025-05-28 18:59:59'),
(19, 4, 4, 17, 'القيود اليومية', 'Journal Entries', 'journal_entries', 'accounting/entries', 'fas fa-book', 'Sub', 42, 1, '2025-05-28 18:59:59'),
(20, 4, 4, 17, 'المدفوعات', 'Payments', 'payments', 'accounting/payments', 'fas fa-credit-card', 'Sub', 43, 1, '2025-05-28 18:59:59'),
(21, 4, 4, 17, 'المقبوضات', 'Receipts', 'receipts', 'accounting/receipts', 'fas fa-money-bill', 'Sub', 44, 1, '2025-05-28 18:59:59'),
(22, 4, 6, NULL, 'التقارير', 'Reports', 'reports_main', 'reports', 'fas fa-chart-bar', 'Main', 6, 1, '2025-05-28 18:59:59'),
(23, 4, 6, 22, 'تقارير المخزون', 'Inventory Reports', 'inventory_reports', 'reports/inventory', 'fas fa-chart-line', 'Sub', 61, 1, '2025-05-28 18:59:59'),
(24, 4, 6, 22, 'التقارير المالية', 'Financial Reports', 'financial_reports', 'reports/financial', 'fas fa-chart-pie', 'Sub', 62, 1, '2025-05-28 18:59:59'),
(25, 4, 6, 22, 'تقارير المبيعات', 'Sales Reports', 'sales_reports', 'reports/sales', 'fas fa-chart-area', 'Sub', 63, 1, '2025-05-28 18:59:59'),
(26, 4, 6, 22, 'تقارير المشتريات', 'Purchases Reports', 'purchases_reports', 'reports/purchases', 'fas fa-chart-column', 'Sub', 64, 1, '2025-05-28 18:59:59');

-- --------------------------------------------------------

--
-- بنية الجدول `notifications`
--

CREATE TABLE `notifications` (
  `notification_id` int NOT NULL,
  `user_id` int NOT NULL,
  `company_id` int NOT NULL,
  `notification_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `template_id` int DEFAULT NULL,
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `data_json` json DEFAULT NULL,
  `is_read` tinyint(1) DEFAULT '0',
  `is_sent_email` tinyint(1) DEFAULT '0',
  `is_sent_sms` tinyint(1) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `read_at` timestamp NULL DEFAULT NULL,
  `related_entity_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `related_entity_id` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `notification_templates`
--

CREATE TABLE `notification_templates` (
  `template_id` int NOT NULL,
  `template_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `name_ar` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `name_en` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `title_template_ar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `title_template_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `message_template_ar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `message_template_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `email_subject_ar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `email_subject_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `email_template_ar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `email_template_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `sms_template_ar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `sms_template_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'system',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `notification_templates`
--

INSERT INTO `notification_templates` (`template_id`, `template_code`, `name_ar`, `name_en`, `title_template_ar`, `title_template_en`, `message_template_ar`, `message_template_en`, `email_subject_ar`, `email_subject_en`, `email_template_ar`, `email_template_en`, `sms_template_ar`, `sms_template_en`, `category`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'user_created', 'إنشاء مستخدم جديد', 'New User Created', 'مستخدم جديد: {new_user_name}', 'New User: {new_user_name}', 'تم إنشاء مستخدم جديد \"{new_user_name}\" بواسطة {created_by} في {date}', 'A new user \"{new_user_name}\" has been created by {created_by} on {date}', NULL, NULL, NULL, NULL, NULL, NULL, 'user_management', 1, '2025-05-27 16:46:09', '2025-05-27 16:46:09'),
(2, 'user_updated', 'تحديث مستخدم', 'User Updated', 'تم تحديث المستخدم: {updated_user_name}', 'User Updated: {updated_user_name}', 'تم تحديث بيانات المستخدم \"{updated_user_name}\" بواسطة {updated_by} في {date}', 'User \"{updated_user_name}\" has been updated by {updated_by} on {date}', NULL, NULL, NULL, NULL, NULL, NULL, 'user_management', 1, '2025-05-27 16:46:09', '2025-05-27 16:46:09'),
(3, 'user_deleted', 'حذف مستخدم', 'User Deleted', 'تم حذف المستخدم: {deleted_user_name}', 'User Deleted: {deleted_user_name}', 'تم حذف المستخدم \"{deleted_user_name}\" بواسطة {deleted_by} في {date}', 'User \"{deleted_user_name}\" has been deleted by {deleted_by} on {date}', NULL, NULL, NULL, NULL, NULL, NULL, 'user_management', 1, '2025-05-27 16:46:09', '2025-05-27 16:46:09'),
(4, 'company_created', 'إنشاء شركة جديدة', 'New Company Created', 'شركة جديدة: {new_company_name}', 'New Company: {new_company_name}', 'تم إنشاء شركة جديدة \"{new_company_name}\" بواسطة {created_by} في {date}', 'A new company \"{new_company_name}\" has been created by {created_by} on {date}', NULL, NULL, NULL, NULL, NULL, NULL, 'company_management', 1, '2025-05-27 16:46:09', '2025-05-27 16:46:09'),
(5, 'company_updated', 'تحديث شركة', 'Company Updated', 'تم تحديث الشركة: {updated_company_name}', 'Company Updated: {updated_company_name}', 'تم تحديث بيانات الشركة \"{updated_company_name}\" بواسطة {updated_by} في {date}', 'Company \"{updated_company_name}\" has been updated by {updated_by} on {date}', NULL, NULL, NULL, NULL, NULL, NULL, 'company_management', 1, '2025-05-27 16:46:09', '2025-05-27 16:46:09'),
(6, 'company_deleted', 'حذف شركة', 'Company Deleted', 'تم حذف الشركة: {deleted_company_name}', 'Company Deleted: {deleted_company_name}', 'تم حذف الشركة \"{deleted_company_name}\" بواسطة {deleted_by} في {date}', 'Company \"{deleted_company_name}\" has been deleted by {deleted_by} on {date}', NULL, NULL, NULL, NULL, NULL, NULL, 'company_management', 1, '2025-05-27 16:46:09', '2025-05-27 16:46:09'),
(7, 'permission_granted', 'منح صلاحية', 'Permission Granted', 'تم منحك صلاحية: {permission_name}', 'Permission Granted: {permission_name}', 'تم منحك صلاحية \"{permission_name}\" بواسطة {granted_by} في {date}', 'You have been granted \"{permission_name}\" permission by {granted_by} on {date}', NULL, NULL, NULL, NULL, NULL, NULL, 'permissions', 1, '2025-05-27 16:46:09', '2025-05-27 16:46:09'),
(8, 'permission_revoked', 'إلغاء صلاحية', 'Permission Revoked', 'تم إلغاء صلاحية: {permission_name}', 'Permission Revoked: {permission_name}', 'تم إلغاء صلاحية \"{permission_name}\" بواسطة {revoked_by} في {date}', 'Your \"{permission_name}\" permission has been revoked by {revoked_by} on {date}', NULL, NULL, NULL, NULL, NULL, NULL, 'permissions', 1, '2025-05-27 16:46:09', '2025-05-27 16:46:09'),
(9, 'task_assigned', 'تعيين مهمة', 'Task Assigned', 'مهمة جديدة: {task_title}', 'New Task: {task_title}', 'تم تعيين مهمة جديدة لك \"{task_title}\" بواسطة {assigned_by} في {date}', 'A new task \"{task_title}\" has been assigned to you by {assigned_by} on {date}', NULL, NULL, NULL, NULL, NULL, NULL, 'tasks', 1, '2025-05-27 16:46:09', '2025-05-27 16:46:09'),
(10, 'task_completed', 'إكمال مهمة', 'Task Completed', 'تم إكمال المهمة: {task_title}', 'Task Completed: {task_title}', 'تم إكمال المهمة \"{task_title}\" بواسطة {completed_by} في {date}', 'Task \"{task_title}\" has been completed by {completed_by} on {date}', NULL, NULL, NULL, NULL, NULL, NULL, 'tasks', 1, '2025-05-27 16:46:09', '2025-05-27 16:46:09'),
(11, 'payment_received', 'استلام دفعة', 'Payment Received', 'تم استلام دفعة: {amount} {currency}', 'Payment Received: {amount} {currency}', 'تم استلام دفعة بقيمة {amount} {currency} بنجاح في {date}', 'A payment of {amount} {currency} has been received successfully on {date}', NULL, NULL, NULL, NULL, NULL, NULL, 'payments', 1, '2025-05-27 16:46:09', '2025-05-27 16:46:09'),
(12, 'subscription_expired', 'انتهاء الاشتراك', 'Subscription Expired', 'انتهى اشتراك: {plan_name}', 'Subscription Expired: {plan_name}', 'انتهى اشتراكك في خطة \"{plan_name}\". يرجى تجديد الاشتراك لمواصلة الخدمة.', 'Your \"{plan_name}\" subscription has expired. Please renew to continue service.', NULL, NULL, NULL, NULL, NULL, NULL, 'subscriptions', 1, '2025-05-27 16:46:09', '2025-05-27 16:46:09'),
(13, 'system_alert', 'تحذير النظام', 'System Alert', 'تحذير النظام: {alert_type}', 'System Alert: {alert_type}', 'تحذير من النظام: {alert_message} في {date}', 'System Alert: {alert_message} on {date}', NULL, NULL, NULL, NULL, NULL, NULL, 'system', 1, '2025-05-27 16:46:09', '2025-05-27 16:46:09');

-- --------------------------------------------------------

--
-- بنية الجدول `passwordreset`
--

CREATE TABLE `passwordreset` (
  `ID` int NOT NULL,
  `UserID` int NOT NULL,
  `Token` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `ExpiresAt` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `passwordreset`
--

INSERT INTO `passwordreset` (`ID`, `UserID`, `Token`, `ExpiresAt`) VALUES
(77, 34, '604464cdd48ee0c7ba43901373db8abc22d935bb57dab9897ca35eb798f48bc4', '2025-05-20 01:00:35'),
(78, 34, '9ac4a431ecc57cef8cb8d29773707fc50e5475d1938d895daf5d4635df8fc900', '2025-05-20 01:02:49'),
(79, 34, '3defd93c646a177f3bcc1f490d4858242a5f5026877c6fec67da2f8e4ef21b57', '2025-05-20 01:09:09'),
(82, 35, 'c39f4ce1546b1db5edbe393b4ea784bdb2c9b07675b4991972fdf0e611d151b9', '2025-05-21 16:19:32'),
(83, 35, 'ab47f9bc492e0bc2aae6f051f769913b4ae83aa231c2fcf4a252984b5a7c8654', '2025-05-31 02:50:30'),
(84, 35, '62ff335eebc15c1c9d7e8cf25aec2a9d6492b7a0b851e6ab9d28ef57530e9a27', '2025-05-31 03:24:03'),
(85, 35, '5d3d6bd0e69ebb4c445ba5d04a6b1b3e6a5d7cca32901f4a4b3d64596edf5343', '2025-05-31 03:28:14'),
(86, 35, '9d6bb7d1c579eb15fe1a41964ca38211555161becbb7ad990cc3e5003dbedc76', '2025-05-31 03:28:33'),
(87, 35, '374f1fef8fbecc38365eb4c6aafbd1dc5d81a4ddbf333d3e7e75fcd2017c4639', '2025-05-31 03:34:31'),
(88, 35, '36b3d1b38291b3eee801637d68a22e5451ca82ab98a1f5cb2a23d1c7882d8224', '2025-05-31 03:42:51'),
(89, 35, 'd07833ddcb393c2f9538e3206f3aed85e6166539ac92b94a4a0b28369dcb06de', '2025-05-31 03:43:04'),
(90, 35, '1d9ea1f04135cb3b40a969c261c95f5246ad1f1836c6c8b0ccee055702477ec3', '2025-05-31 03:45:27'),
(91, 35, '57e06273979545f43b3f2d455c21b352d416f1abd1e10d78fcbbcb9d2c89e440', '2025-05-31 03:48:00'),
(92, 35, '4cb944a895cd2c3314ab816eb097e21f353b25fb0d65f890419784d3d555b2d8', '2025-05-31 03:48:06'),
(93, 35, '88fab3140b6bb43210c48cf3228a9c7f5917739b65a577876752560d8ea5dec9', '2025-05-31 03:48:16'),
(94, 35, '135b88f1087927e4a3cd3befac3e76b615d71dcaffd2ad577683ca2595ec3840', '2025-05-31 03:51:13'),
(95, 35, '3b9a777c1f22f7a8bb2f6b4bdcc256833a13218ea4e015bcfb260c22653c791f', '2025-05-31 03:52:05'),
(96, 35, '15b04a143b43807a2c7fbb74751aa06155696b2bc23ebc2ea3c2344397262406', '2025-05-31 03:55:24'),
(97, 35, '8fb3317ec86998148ce94a917014bb2e164f7ad5798f1abbc8c0170d512c9952', '2025-05-31 03:57:22'),
(98, 35, '44a9d6c6c8d8e70a2b96c90406168c495d8dee85c6359c7230a5a15073e6391f', '2025-05-31 03:58:08'),
(99, 32, 'd63ef345387de172846c2410a1fda8e9983bb910178812feef9faa60b39ce90e', '2025-05-31 04:00:39'),
(100, 32, '519c89ff33acdcdef56b9f5563b97a415b1afa580aa6f1c5ae802cdc5e56300b', '2025-05-31 04:01:04'),
(101, 35, '7b4fba34f35c1b768935e56a2bf715f2ab35c8b9e63ba64a1d1b1920438d30e5', '2025-05-31 04:09:42'),
(102, 35, 'ee22b6c5abb441913cd206dd0c13bf8e108a4882767708dc217782604b4c3b47', '2025-05-31 04:11:32'),
(104, 35, 'f60705e7055e2cc328e3731a5e520330c30b04b246eca41b07edc5353c3464b3', '2025-05-31 04:31:57'),
(105, 35, 'b8d07f64edd69fc0c2ce569a088f0678da283312a88ba2c3b26a890bfe2d2b23', '2025-05-31 04:32:54'),
(106, 35, '57ccf13d0dbc9f6210ff4e8a7b5e3ac81383d9716a317b527a727d9e4896747a', '2025-05-31 04:33:24'),
(108, 35, '04d2ecb980061315d124dfc6cfa497751750f6c4eeeaf1f0277ef5aca1d2c0d7', '2025-05-31 04:37:11'),
(109, 35, '6a05a823c8d3f1fec9f6f9bf1a57fe0a478c68815c7677b70a0f1dd3eb544b6f', '2025-05-31 04:51:19'),
(110, 35, '2fd29811e5dc24781d76f443dad5a106b59d6ad6e33cdef1611272897ab2de2d', '2025-05-31 04:52:05'),
(111, 35, '55a4aee3e8c4977cdbcf4d4bc6249399ee2f83d68676a0c7f14021283456eb87', '2025-05-31 04:52:17'),
(112, 35, 'cbc23de1fe4334de62e497aae9dfa0e3d2dd584b88111b63fd89f4ac0d968f4f', '2025-05-31 04:58:22'),
(113, 35, '800db5f6f1dc7a446bbaa22348dcb0ec28b677d44e9ba6400f0b60434a92952d', '2025-05-31 05:00:07'),
(114, 35, 'c5740797d9fe4ee47ed738cf1cc45e39652efe42f7b5a00c23eb10e5ddcaeae2', '2025-05-31 05:00:13'),
(115, 35, '62990a397465f4521451ec665fa1bee7faccfe4738801be5c958f392b1c18485', '2025-05-31 05:01:03'),
(118, 35, 'bde6863bf27da3972688f83cd3a19d118d535b6757050a979d34ab27871170aa', '2025-05-31 05:04:55'),
(119, 35, 'a4e573c50953fa956d3648e518acc63f3d408dbbe03fff51319775db74b63176', '2025-05-31 05:05:39'),
(120, 35, '5269b36d9e9cf96e1fd191fbd1c7d51cffb87f0da86bc848cc0c00494d6f475e', '2025-05-31 05:08:29'),
(121, 35, 'fc5eb2218cbaaf217b91f7d15fdea3715055cf126e84f44277452557beab17b9', '2025-05-31 05:11:58'),
(122, 35, '6eab5b28e905ee0bbb94223390875ed89a408c9d871f439647229f67bfce4e8b', '2025-05-31 05:16:07'),
(124, 35, 'cda158c048f6a1a88a14e5a86c35490a6cdf8efcf41ccb930567bc985f747a3a', '2025-05-31 05:25:09'),
(126, 35, 'f305ea5d201a803a5c2683db0e69a12b592a993665ba0305cbd951b9a408edcd', '2025-05-31 05:27:52');

-- --------------------------------------------------------

--
-- بنية الجدول `payments`
--

CREATE TABLE `payments` (
  `payment_id` int NOT NULL,
  `subscription_id` int NOT NULL,
  `company_id` int NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `currency` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'SAR',
  `payment_method` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `transaction_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `status` enum('pending','completed','failed','refunded') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'pending',
  `payment_date` timestamp NULL DEFAULT NULL,
  `invoice_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `invoice_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `payments`
--

INSERT INTO `payments` (`payment_id`, `subscription_id`, `company_id`, `amount`, `currency`, `payment_method`, `transaction_id`, `status`, `payment_date`, `invoice_number`, `invoice_url`, `created_at`, `updated_at`) VALUES
(1, 3, 27, 100.00, 'SAR', 'credit_card', NULL, 'pending', NULL, NULL, NULL, '2025-05-21 19:58:45', '2025-05-21 19:58:45'),
(2, 4, 27, 100.00, 'SAR', 'bank_transfer', NULL, 'pending', NULL, NULL, NULL, '2025-05-21 19:59:12', '2025-05-21 19:59:12'),
(3, 5, 27, 100.00, 'SAR', 'credit_card', NULL, 'pending', NULL, NULL, NULL, '2025-05-21 19:59:23', '2025-05-21 19:59:23'),
(4, 6, 27, 50.00, 'SAR', 'credit_card', NULL, 'pending', NULL, NULL, NULL, '2025-05-21 20:02:26', '2025-05-21 20:02:26'),
(5, 7, 27, 50.00, 'SAR', 'mada', NULL, 'pending', NULL, NULL, NULL, '2025-05-21 20:04:05', '2025-05-21 20:04:05'),
(6, 8, 27, 50.00, 'SAR', 'bank_transfer', NULL, 'pending', NULL, NULL, NULL, '2025-05-21 20:08:12', '2025-05-21 20:08:12'),
(7, 9, 27, 100.00, 'SAR', 'credit_card', NULL, 'pending', NULL, NULL, NULL, '2025-05-21 20:17:42', '2025-05-21 20:17:42'),
(8, 10, 27, 50.00, 'SAR', 'credit_card', NULL, 'pending', NULL, NULL, NULL, '2025-05-21 20:22:23', '2025-05-21 20:22:23'),
(9, 11, 27, 50.00, 'SAR', 'credit_card', NULL, 'pending', NULL, NULL, NULL, '2025-05-21 20:22:49', '2025-05-21 20:22:49'),
(10, 12, 27, 2000.00, 'SAR', 'mada', NULL, 'pending', NULL, NULL, NULL, '2025-05-21 20:23:04', '2025-05-21 20:23:04'),
(11, 13, 27, 100.00, 'SAR', 'mada', NULL, 'completed', '2025-05-21 20:30:51', NULL, NULL, '2025-05-21 20:30:08', '2025-05-21 20:30:51'),
(12, 14, 27, 200.00, 'SAR', 'credit_card', NULL, 'completed', '2025-05-21 20:36:23', 'INV-********-14', NULL, '2025-05-21 20:35:35', '2025-05-21 20:36:23'),
(13, 15, 27, 50.00, 'SAR', 'credit_card', NULL, 'completed', '2025-05-21 20:39:57', 'INV-********-15', NULL, '2025-05-21 20:38:50', '2025-05-21 20:39:57'),
(14, 16, 27, 100.00, 'SAR', 'credit_card', NULL, 'completed', '2025-05-21 20:47:04', 'INV-********-16', NULL, '2025-05-21 20:46:11', '2025-05-21 20:47:04'),
(15, 17, 27, 50.00, 'SAR', 'credit_card', NULL, 'completed', '2025-05-21 20:50:10', 'INV-********-17', NULL, '2025-05-21 20:49:28', '2025-05-21 20:50:10'),
(16, 18, 27, 100.00, 'SAR', 'credit_card', NULL, 'pending', NULL, NULL, NULL, '2025-05-21 21:04:56', '2025-05-21 21:04:56'),
(17, 19, 27, 2.00, 'SAR', 'mada', NULL, 'pending', '2025-05-21 21:49:15', NULL, NULL, '2025-05-21 21:40:14', '2025-05-21 21:49:15'),
(18, 20, 27, 2.00, 'SAR', 'mada', 'TST2514101051419', 'pending', NULL, NULL, NULL, '2025-05-21 21:50:45', '2025-05-21 22:22:39'),
(19, 21, 27, 2.00, 'SAR', 'mada', 'TST2514101051421', 'pending', NULL, NULL, NULL, '2025-05-21 22:24:49', '2025-05-21 22:25:25'),
(20, 22, 27, 2.00, 'SAR', 'mada', NULL, 'pending', NULL, NULL, NULL, '2025-05-21 22:38:05', '2025-05-21 22:38:05'),
(21, 23, 27, 2.00, 'SAR', 'mada', NULL, 'pending', NULL, NULL, NULL, '2025-05-21 22:38:37', '2025-05-21 22:38:37'),
(22, 24, 27, 2.00, 'SAR', 'mada', 'TST2514101051424', 'pending', NULL, NULL, NULL, '2025-05-21 22:55:14', '2025-05-21 23:02:53'),
(23, 25, 27, 2.00, 'SAR', 'mada', 'TST2514101051428', 'pending', NULL, NULL, NULL, '2025-05-21 23:14:46', '2025-05-21 23:15:06'),
(24, 26, 27, 2.00, 'SAR', 'mada', 'TST2514201051508', 'pending', NULL, NULL, NULL, '2025-05-22 07:07:38', '2025-05-22 07:08:29'),
(25, 27, 27, 2.00, 'SAR', 'mada', NULL, 'pending', NULL, NULL, NULL, '2025-05-22 07:27:27', '2025-05-22 07:27:27'),
(26, 28, 27, 2.00, 'SAR', 'credit_card', NULL, 'pending', NULL, NULL, NULL, '2025-05-22 07:28:30', '2025-05-22 07:28:30'),
(27, 29, 27, 2.00, 'SAR', 'mada', NULL, 'pending', '2025-05-22 08:08:26', NULL, NULL, '2025-05-22 07:46:48', '2025-05-22 08:08:26'),
(28, 30, 27, 2.00, 'SAR', 'mada', NULL, 'pending', NULL, NULL, NULL, '2025-05-22 07:54:17', '2025-05-22 07:54:17'),
(29, 31, 27, 2.00, 'SAR', 'credit_card', NULL, 'pending', NULL, NULL, NULL, '2025-05-22 08:09:13', '2025-05-22 08:09:13'),
(30, 32, 27, 5.00, 'SAR', 'mada', 'TST2514201051587', 'pending', NULL, NULL, NULL, '2025-05-22 08:39:23', '2025-05-22 08:39:50'),
(31, 33, 27, 2.00, 'SAR', 'credit_card', 'TST2514201051594', 'pending', NULL, NULL, NULL, '2025-05-22 08:45:27', '2025-05-22 08:46:16'),
(32, 34, 27, 2.00, 'SAR', 'credit_card', NULL, 'pending', NULL, NULL, NULL, '2025-05-22 08:53:11', '2025-05-22 08:53:11'),
(33, 35, 27, 2.00, 'SAR', 'credit_card', NULL, 'pending', NULL, NULL, NULL, '2025-05-22 08:54:29', '2025-05-22 08:54:29'),
(34, 36, 27, 2.00, 'SAR', 'credit_card', 'TST2514201051626', 'pending', NULL, NULL, NULL, '2025-05-22 09:27:40', '2025-05-22 09:28:23'),
(35, 37, 27, 100.00, 'SAR', 'credit_card', 'TST2514201051627', 'pending', NULL, NULL, NULL, '2025-05-22 09:30:45', '2025-05-22 09:31:23'),
(36, 38, 27, 200.00, 'SAR', 'credit_card', 'TST2514201051638', 'pending', NULL, NULL, NULL, '2025-05-22 09:39:59', '2025-05-22 09:40:52'),
(37, 39, 27, 2.00, 'SAR', 'credit_card', 'TST2514201051642', 'pending', NULL, NULL, NULL, '2025-05-22 09:44:32', '2025-05-22 09:45:01'),
(38, 40, 27, 100.00, 'SAR', 'credit_card', 'TST2514201051644', 'pending', NULL, NULL, NULL, '2025-05-22 09:52:12', '2025-05-22 09:52:42'),
(39, 41, 27, 2.00, 'SAR', 'credit_card', NULL, 'pending', NULL, NULL, NULL, '2025-05-22 12:51:50', '2025-05-22 12:51:50'),
(40, 42, 27, 2.00, 'SAR', 'credit_card', NULL, 'pending', '2025-05-22 15:32:32', NULL, NULL, '2025-05-22 15:32:26', '2025-05-22 15:32:32'),
(41, 43, 27, 2.00, 'SAR', 'credit_card', NULL, 'pending', '2025-05-22 18:30:36', NULL, NULL, '2025-05-22 18:30:02', '2025-05-22 18:30:36'),
(42, 44, 27, 2.00, 'SAR', 'credit_card', NULL, 'pending', NULL, NULL, NULL, '2025-05-22 19:01:38', '2025-05-22 19:01:38'),
(43, 45, 27, 2.00, 'SAR', 'credit_card', NULL, 'pending', '2025-05-22 19:06:57', NULL, NULL, '2025-05-22 19:06:54', '2025-05-22 19:06:57'),
(44, 46, 27, 2.00, 'SAR', 'credit_card', NULL, 'pending', NULL, NULL, NULL, '2025-05-22 22:55:29', '2025-05-22 22:55:29'),
(45, 47, 27, 2.00, 'SAR', 'credit_card', NULL, 'pending', NULL, NULL, NULL, '2025-05-23 07:03:12', '2025-05-23 07:03:12'),
(46, 48, 27, 2.00, 'SAR', 'credit_card', NULL, 'pending', '2025-05-23 12:42:52', NULL, NULL, '2025-05-23 12:42:41', '2025-05-23 12:42:52'),
(47, 49, 27, 2.00, 'SAR', 'credit_card', NULL, 'pending', '2025-05-23 13:15:53', NULL, NULL, '2025-05-23 13:15:46', '2025-05-23 13:15:53'),
(48, 50, 27, 2.00, 'SAR', 'mada', NULL, 'pending', NULL, NULL, NULL, '2025-05-23 13:22:40', '2025-05-23 13:22:40'),
(49, 51, 27, 2.00, 'SAR', 'mada', NULL, 'pending', NULL, NULL, NULL, '2025-05-23 19:59:17', '2025-05-23 19:59:17'),
(50, 52, 27, 2.00, 'SAR', 'credit_card', NULL, 'pending', NULL, NULL, NULL, '2025-05-23 20:47:28', '2025-05-23 20:47:28'),
(51, 53, 27, 200.00, 'SAR', 'mada', NULL, 'pending', '2025-05-23 20:47:54', NULL, NULL, '2025-05-23 20:47:51', '2025-05-23 20:47:54'),
(52, 54, 27, 100.00, 'SAR', 'mada', NULL, 'pending', '2025-05-23 21:01:46', NULL, NULL, '2025-05-23 21:01:42', '2025-05-23 21:01:46'),
(53, 55, 27, 200.00, 'SAR', 'bank_transfer', NULL, 'pending', '2025-05-23 21:03:43', NULL, NULL, '2025-05-23 21:03:39', '2025-05-23 21:03:43'),
(54, 56, 27, 2.00, 'SAR', 'bank_transfer', NULL, 'pending', '2025-05-23 21:41:54', NULL, NULL, '2025-05-23 21:41:50', '2025-05-23 21:41:54'),
(55, 57, 27, 100.00, 'SAR', 'bank_transfer', NULL, 'pending', '2025-05-23 21:43:35', NULL, NULL, '2025-05-23 21:43:32', '2025-05-23 21:43:35'),
(56, 58, 27, 200.00, 'SAR', 'bank_transfer', NULL, 'pending', '2025-05-23 21:46:17', NULL, NULL, '2025-05-23 21:46:13', '2025-05-23 21:46:17'),
(57, 59, 27, 100.00, 'SAR', 'bank_transfer', NULL, 'pending', '2025-05-23 21:47:26', NULL, NULL, '2025-05-23 21:47:23', '2025-05-23 21:47:26'),
(58, 60, 1, 2.00, 'SAR', 'bank_transfer', NULL, 'pending', '2025-05-23 21:48:21', NULL, NULL, '2025-05-23 21:48:17', '2025-05-23 21:48:21'),
(59, 61, 1, 100.00, 'SAR', 'bank_transfer', NULL, 'pending', NULL, NULL, NULL, '2025-05-23 21:49:29', '2025-05-23 21:49:29'),
(60, 62, 1, 100.00, 'SAR', 'credit_card', NULL, 'pending', NULL, NULL, NULL, '2025-05-24 09:45:22', '2025-05-24 09:45:22'),
(61, 63, 4, 1000.00, 'SAR', 'bank_transfer', NULL, 'pending', '2025-05-24 10:01:43', NULL, NULL, '2025-05-24 10:01:39', '2025-05-24 10:01:43'),
(62, 64, 4, 2.00, 'SAR', 'bank_transfer', NULL, 'pending', '2025-05-24 10:09:38', NULL, NULL, '2025-05-24 10:09:34', '2025-05-24 10:09:38'),
(63, 65, 4, 2.00, 'SAR', 'bank_transfer', NULL, 'pending', '2025-05-24 10:11:34', NULL, NULL, '2025-05-24 10:11:29', '2025-05-24 10:11:34'),
(64, 66, 5, 200.00, 'SAR', 'bank_transfer', NULL, 'pending', '2025-05-24 10:19:38', NULL, NULL, '2025-05-24 10:19:34', '2025-05-24 10:19:38'),
(65, 67, 6, 100.00, 'SAR', 'bank_transfer', NULL, 'pending', '2025-05-24 10:51:38', NULL, NULL, '2025-05-24 10:51:32', '2025-05-24 10:51:38'),
(66, 68, 4, 200.00, 'SAR', 'bank_transfer', NULL, 'pending', '2025-05-24 17:52:07', NULL, NULL, '2025-05-24 17:52:02', '2025-05-24 17:52:07'),
(67, 69, 4, 100.00, 'SAR', 'bank_transfer', NULL, 'pending', '2025-05-24 18:38:09', NULL, NULL, '2025-05-24 18:37:58', '2025-05-24 18:38:09'),
(68, 70, 4, 100.00, 'SAR', 'bank_transfer', NULL, 'pending', '2025-05-24 18:39:12', NULL, NULL, '2025-05-24 18:39:07', '2025-05-24 18:39:12'),
(69, 71, 5, 100.00, 'SAR', 'bank_transfer', NULL, 'pending', '2025-05-24 18:39:47', NULL, NULL, '2025-05-24 18:39:43', '2025-05-24 18:39:47'),
(70, 72, 4, 100.00, 'SAR', 'bank_transfer', NULL, 'pending', '2025-05-24 18:40:44', NULL, NULL, '2025-05-24 18:40:39', '2025-05-24 18:40:44'),
(71, 73, 1, 2.00, 'SAR', 'credit_card', NULL, 'pending', NULL, NULL, NULL, '2025-05-24 18:50:58', '2025-05-24 18:50:58'),
(72, 74, 5, 2.00, 'SAR', 'credit_card', NULL, 'pending', '2025-05-24 18:59:23', NULL, NULL, '2025-05-24 18:55:22', '2025-05-24 18:59:23'),
(73, 75, 4, 1000.00, 'SAR', 'bank_transfer', NULL, 'pending', NULL, NULL, NULL, '2025-05-24 18:59:44', '2025-05-24 18:59:44'),
(74, 76, 4, 100.00, 'SAR', 'bank_transfer', NULL, 'pending', '2025-05-26 16:26:52', NULL, NULL, '2025-05-26 16:26:48', '2025-05-26 16:26:52'),
(75, 77, 6, 2.00, 'SAR', 'credit_card', NULL, 'pending', NULL, NULL, NULL, '2025-05-26 20:30:01', '2025-05-26 20:30:01'),
(76, 78, 5, 100.00, 'SAR', 'credit_card', NULL, 'pending', NULL, NULL, NULL, '2025-05-27 08:23:27', '2025-05-27 08:23:27'),
(77, 79, 4, 100.00, 'SAR', 'bank_transfer', NULL, 'pending', NULL, NULL, NULL, '2025-05-27 08:29:58', '2025-05-27 08:29:58'),
(78, 80, 4, 100.00, 'SAR', 'credit_card', NULL, 'pending', NULL, NULL, NULL, '2025-05-27 08:31:58', '2025-05-27 08:31:58'),
(79, 81, 5, 100.00, 'SAR', 'credit_card', NULL, 'pending', '2025-05-27 09:40:30', NULL, NULL, '2025-05-27 09:40:27', '2025-05-27 09:40:30');

-- --------------------------------------------------------

--
-- بنية الجدول `permissions`
--

CREATE TABLE `permissions` (
  `permission_id` int NOT NULL,
  `company_id` int NOT NULL,
  `position_id` int NOT NULL,
  `program_id` int NOT NULL,
  `can_view` tinyint(1) DEFAULT '0',
  `can_create` tinyint(1) DEFAULT '0',
  `can_edit` tinyint(1) DEFAULT '0',
  `can_delete` tinyint(1) DEFAULT '0',
  `can_approve` tinyint(1) DEFAULT '0',
  `created_by` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- إرجاع أو استيراد بيانات الجدول `permissions`
--

INSERT INTO `permissions` (`permission_id`, `company_id`, `position_id`, `program_id`, `can_view`, `can_create`, `can_edit`, `can_delete`, `can_approve`, `created_by`, `created_at`) VALUES
(1, 4, 1, 1, 1, 1, 1, 1, 1, 32, '2025-05-28 18:59:59'),
(2, 4, 1, 2, 1, 1, 1, 1, 1, 32, '2025-05-28 18:59:59'),
(3, 4, 1, 3, 1, 1, 1, 1, 1, 32, '2025-05-28 18:59:59'),
(4, 4, 1, 4, 1, 1, 1, 1, 1, 32, '2025-05-28 18:59:59'),
(5, 4, 1, 5, 1, 1, 1, 1, 1, 32, '2025-05-28 18:59:59'),
(6, 4, 1, 6, 1, 1, 1, 1, 1, 32, '2025-05-28 18:59:59'),
(7, 4, 1, 7, 1, 1, 1, 1, 1, 32, '2025-05-28 18:59:59'),
(8, 4, 1, 8, 1, 1, 1, 1, 1, 32, '2025-05-28 18:59:59'),
(9, 4, 1, 9, 1, 1, 1, 1, 1, 32, '2025-05-28 18:59:59'),
(10, 4, 1, 10, 1, 1, 1, 1, 1, 32, '2025-05-28 18:59:59'),
(11, 4, 1, 11, 1, 1, 1, 1, 1, 32, '2025-05-28 18:59:59'),
(12, 4, 1, 12, 1, 1, 1, 1, 1, 32, '2025-05-28 18:59:59'),
(13, 4, 1, 13, 1, 1, 1, 1, 1, 32, '2025-05-28 18:59:59'),
(14, 4, 1, 14, 1, 1, 1, 1, 1, 32, '2025-05-28 18:59:59'),
(15, 4, 1, 15, 1, 1, 1, 1, 1, 32, '2025-05-28 18:59:59'),
(16, 4, 1, 16, 1, 1, 1, 1, 1, 32, '2025-05-28 18:59:59'),
(17, 4, 1, 17, 1, 1, 1, 1, 1, 32, '2025-05-28 18:59:59'),
(18, 4, 1, 18, 1, 1, 1, 1, 1, 32, '2025-05-28 18:59:59'),
(19, 4, 1, 19, 1, 1, 1, 1, 1, 32, '2025-05-28 18:59:59'),
(20, 4, 1, 20, 1, 1, 1, 1, 1, 32, '2025-05-28 18:59:59'),
(21, 4, 1, 21, 1, 1, 1, 1, 1, 32, '2025-05-28 18:59:59'),
(22, 4, 1, 22, 1, 1, 1, 1, 1, 32, '2025-05-28 18:59:59'),
(23, 4, 1, 23, 1, 1, 1, 1, 1, 32, '2025-05-28 18:59:59'),
(24, 4, 1, 24, 1, 1, 1, 1, 1, 32, '2025-05-28 18:59:59'),
(25, 4, 1, 25, 1, 1, 1, 1, 1, 32, '2025-05-28 18:59:59'),
(26, 4, 1, 26, 1, 1, 1, 1, 1, 32, '2025-05-28 18:59:59'),
(32, 4, 2, 1, 1, 0, 1, 1, 0, 32, '2025-05-28 18:59:59'),
(33, 4, 2, 2, 1, 0, 1, 1, 0, 32, '2025-05-28 18:59:59'),
(34, 4, 2, 3, 1, 1, 1, 1, 0, 32, '2025-05-28 18:59:59'),
(35, 4, 2, 4, 1, 1, 1, 1, 0, 32, '2025-05-28 18:59:59'),
(36, 4, 2, 5, 1, 1, 1, 1, 0, 32, '2025-05-28 18:59:59'),
(37, 4, 2, 6, 1, 1, 1, 1, 0, 32, '2025-05-28 18:59:59'),
(39, 4, 2, 12, 1, 1, 1, 1, 0, 32, '2025-05-28 18:59:59'),
(40, 4, 2, 13, 1, 1, 1, 1, 0, 32, '2025-05-28 18:59:59'),
(41, 4, 3, 14, 1, 1, 1, 1, 0, 32, '2025-05-28 18:59:59'),
(42, 4, 3, 15, 1, 1, 1, 1, 0, 32, '2025-05-28 18:59:59'),
(43, 4, 3, 16, 1, 1, 1, 1, 0, 32, '2025-05-28 18:59:59'),
(46, 4, 4, 17, 1, 1, 1, 0, 0, 32, '2025-05-28 18:59:59'),
(47, 4, 4, 18, 1, 1, 1, 0, 0, 32, '2025-05-28 18:59:59'),
(48, 4, 4, 19, 1, 1, 1, 0, 0, 32, '2025-05-28 18:59:59'),
(49, 4, 4, 20, 1, 1, 1, 0, 0, 32, '2025-05-28 18:59:59'),
(50, 4, 4, 21, 1, 1, 1, 0, 0, 32, '2025-05-28 18:59:59'),
(51, 4, 4, 22, 1, 1, 1, 0, 0, 32, '2025-05-28 18:59:59'),
(52, 4, 4, 23, 1, 1, 1, 0, 0, 32, '2025-05-28 18:59:59'),
(53, 4, 4, 24, 1, 1, 1, 0, 0, 32, '2025-05-28 18:59:59'),
(54, 4, 4, 25, 1, 1, 1, 0, 0, 32, '2025-05-28 18:59:59'),
(55, 4, 4, 26, 1, 1, 1, 0, 0, 32, '2025-05-28 18:59:59'),
(61, 4, 5, 1, 1, 1, 0, 0, 0, 32, '2025-05-28 18:59:59'),
(62, 4, 5, 2, 1, 1, 0, 0, 0, 32, '2025-05-28 18:59:59'),
(63, 4, 5, 3, 1, 1, 0, 0, 0, 32, '2025-05-28 18:59:59'),
(64, 4, 5, 4, 1, 1, 0, 0, 0, 32, '2025-05-28 18:59:59'),
(65, 4, 5, 5, 1, 1, 0, 0, 0, 32, '2025-05-28 18:59:59'),
(66, 4, 5, 6, 1, 1, 0, 0, 0, 32, '2025-05-28 18:59:59');

-- --------------------------------------------------------

--
-- بنية الجدول `plan_available_programs`
--

CREATE TABLE `plan_available_programs` (
  `id` int NOT NULL,
  `plan_id` int NOT NULL,
  `program_id` int NOT NULL,
  `is_included` tinyint(1) NOT NULL DEFAULT '1'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `plan_features`
--

CREATE TABLE `plan_features` (
  `id` int NOT NULL,
  `plan_id` int NOT NULL,
  `feature_id` int NOT NULL,
  `feature_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `plan_features`
--

INSERT INTO `plan_features` (`id`, `plan_id`, `feature_id`, `feature_value`, `is_enabled`) VALUES
(1, 1, 1, '5', 1),
(2, 1, 2, '3', 1),
(3, 1, 3, '5', 1),
(4, 1, 4, 'البريد الإلكتروني فقط', 1),
(5, 1, 5, 'أسبوعي', 1),
(6, 1, 6, 'لا', 0),
(7, 1, 7, 'لا', 0),
(8, 2, 1, '15', 1),
(9, 2, 2, '7', 1),
(10, 2, 3, '20', 1),
(11, 2, 4, 'البريد الإلكتروني والهاتف', 1),
(12, 2, 5, 'يومي', 1),
(13, 2, 6, 'نعم', 1),
(14, 2, 7, 'لا', 0),
(15, 3, 1, '50', 1),
(16, 3, 2, '15', 1),
(17, 3, 3, '100', 1),
(18, 3, 4, 'البريد الإلكتروني والهاتف والدردشة الحية', 1),
(19, 3, 5, 'في الوقت الحقيقي', 1),
(20, 3, 6, 'نعم', 1),
(21, 3, 7, 'نعم', 1);

-- --------------------------------------------------------

--
-- بنية الجدول `positions`
--

CREATE TABLE `positions` (
  `position_id` int NOT NULL,
  `company_id` int NOT NULL,
  `position_name_ar` varchar(100) NOT NULL,
  `position_name_en` varchar(100) NOT NULL,
  `description` text,
  `created_by` int NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- إرجاع أو استيراد بيانات الجدول `positions`
--

INSERT INTO `positions` (`position_id`, `company_id`, `position_name_ar`, `position_name_en`, `description`, `created_by`, `is_active`, `created_at`) VALUES
(1, 4, 'مدير عام', 'General Manager', 'صلاحيات كاملة على جميع الوحدات والبرامج', 32, 1, '2025-05-28 18:59:59'),
(2, 4, 'مدير المخزون', 'Inventory Manager', 'صلاحيات كاملة على وحدة المخزون', 32, 1, '2025-05-28 18:59:59'),
(3, 4, 'مدير المبيعات', 'Sales Manager', 'صلاحيات كاملة على وحدة المبيعات', 32, 1, '2025-05-28 18:59:59'),
(4, 4, 'محاسب', 'Accountant', 'صلاحيات على وحدة المحاسبة والتقارير المالية', 32, 1, '2025-05-28 18:59:59'),
(5, 4, 'موظف مخزون', 'Inventory Employee', 'صلاحيات محدودة على وحدة المخزون', 32, 1, '2025-05-28 18:59:59');

-- --------------------------------------------------------

--
-- بنية الجدول `programs`
--

CREATE TABLE `programs` (
  `ProgramID` int NOT NULL,
  `ProgramName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `ProgramNameEN` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ProgramDescription` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `ProgramDescriptionEN` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `ProgramIcon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ProgramPrice` decimal(10,2) NOT NULL DEFAULT '0.00',
  `ProgramStatus` enum('active','inactive','coming_soon') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active',
  `ProgramCategory` enum('accounting','hr','inventory','sales','crm','other') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'other',
  `ProgramVersion` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `created_by` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `program_categories`
--

CREATE TABLE `program_categories` (
  `category_id` int NOT NULL,
  `name_ar` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `name_en` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `description_ar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `description_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `icon_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `display_order` int NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `subscriptions`
--

CREATE TABLE `subscriptions` (
  `subscription_id` int NOT NULL,
  `company_id` int NOT NULL,
  `plan_id` int NOT NULL,
  `start_date` timestamp NOT NULL,
  `end_date` datetime NOT NULL,
  `status` enum('active','canceled','expired','pending') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'pending',
  `is_auto_renew` tinyint(1) NOT NULL DEFAULT '0',
  `billing_cycle` enum('monthly','yearly') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'monthly',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `canceled_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `subscriptions`
--

INSERT INTO `subscriptions` (`subscription_id`, `company_id`, `plan_id`, `start_date`, `end_date`, `status`, `is_auto_renew`, `billing_cycle`, `created_at`, `updated_at`, `canceled_at`) VALUES
(65, 4, 1, '2025-05-24 10:11:49', '2025-06-24 13:11:29', 'canceled', 1, 'monthly', '2025-05-24 10:11:29', '2025-05-24 10:15:38', '2025-05-24 10:15:38'),
(66, 5, 3, '2025-05-24 19:19:34', '2025-06-24 13:19:34', 'pending', 1, 'monthly', '2025-05-24 10:19:34', '2025-05-24 10:19:34', NULL),
(67, 6, 2, '2025-05-24 19:51:32', '2025-06-24 13:51:32', 'pending', 0, 'monthly', '2025-05-24 10:51:32', '2025-05-24 10:51:32', NULL),
(68, 4, 3, '2025-05-24 18:08:26', '2025-06-24 20:52:02', 'active', 0, 'monthly', '2025-05-24 17:52:02', '2025-05-24 18:18:10', '2025-05-24 17:53:53'),
(69, 4, 2, '2025-05-25 03:37:58', '2025-06-24 21:37:58', 'pending', 1, 'monthly', '2025-05-24 18:37:58', '2025-05-24 18:37:58', NULL),
(70, 4, 2, '2025-05-25 03:39:07', '2025-06-24 21:39:07', 'pending', 1, 'monthly', '2025-05-24 18:39:07', '2025-05-24 18:39:07', NULL),
(71, 5, 2, '2025-05-25 03:39:43', '2025-06-24 21:39:43', 'pending', 0, 'monthly', '2025-05-24 18:39:43', '2025-05-24 18:39:43', NULL),
(72, 4, 2, '2025-05-25 03:40:39', '2025-06-24 21:40:39', 'pending', 1, 'monthly', '2025-05-24 18:40:39', '2025-05-24 18:40:39', NULL),
(73, 1, 1, '2025-05-25 03:50:58', '2025-06-24 21:50:58', 'pending', 1, 'monthly', '2025-05-24 18:50:58', '2025-05-24 18:50:58', NULL),
(74, 5, 1, '2025-05-25 03:55:22', '2025-06-24 21:55:22', 'pending', 1, 'monthly', '2025-05-24 18:55:22', '2025-05-24 18:55:22', NULL),
(75, 4, 2, '2025-05-25 03:59:44', '2026-05-24 21:59:44', 'pending', 0, 'yearly', '2025-05-24 18:59:44', '2025-05-24 18:59:44', NULL),
(76, 4, 2, '2025-05-27 01:26:48', '2025-06-26 19:26:48', 'pending', 1, 'monthly', '2025-05-26 16:26:48', '2025-05-26 16:26:48', NULL),
(77, 6, 1, '2025-05-27 05:30:01', '2025-06-26 23:30:01', 'pending', 1, 'monthly', '2025-05-26 20:30:01', '2025-05-26 20:30:01', NULL),
(78, 5, 2, '2025-05-27 17:23:27', '2025-06-27 11:23:27', 'pending', 1, 'monthly', '2025-05-27 08:23:27', '2025-05-27 08:23:27', NULL),
(79, 4, 2, '2025-05-27 17:29:58', '2025-06-27 11:29:58', 'pending', 1, 'monthly', '2025-05-27 08:29:58', '2025-05-27 08:29:58', NULL),
(80, 4, 2, '2025-05-28 20:03:46', '2025-06-27 11:31:58', 'active', 1, 'monthly', '2025-05-27 08:31:58', '2025-05-28 20:03:46', NULL),
(81, 5, 2, '2025-05-27 18:40:27', '2025-06-27 12:40:27', 'pending', 1, 'monthly', '2025-05-27 09:40:27', '2025-05-27 09:40:27', NULL);

-- --------------------------------------------------------

--
-- بنية الجدول `subscription_features`
--

CREATE TABLE `subscription_features` (
  `feature_id` int NOT NULL,
  `feature_name_ar` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `feature_name_en` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `feature_description_ar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `feature_description_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `feature_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `subscription_features`
--

INSERT INTO `subscription_features` (`feature_id`, `feature_name_ar`, `feature_name_en`, `feature_description_ar`, `feature_description_en`, `feature_code`, `is_active`, `created_at`) VALUES
(1, 'عدد المستخدمين', 'Number of Users', 'الحد الأقصى لعدد المستخدمين المسموح به', 'Maximum number of allowed users', 'max_users', 1, '2025-05-21 18:57:24'),
(2, 'عدد البرامج', 'Number of Programs', 'الحد الأقصى لعدد البرامج المسموح به', 'Maximum number of allowed programs', 'max_programs', 1, '2025-05-21 18:57:24'),
(3, 'مساحة التخزين', 'Storage Space', 'مساحة التخزين المتاحة بالجيجابايت', 'Available storage space in GB', 'storage_space', 1, '2025-05-21 18:57:24'),
(4, 'الدعم الفني', 'Technical Support', 'نوع الدعم الفني المتاح', 'Type of available technical support', 'support_type', 1, '2025-05-21 18:57:24'),
(5, 'النسخ الاحتياطي', 'Backup', 'تكرار النسخ الاحتياطي', 'Backup frequency', 'backup_frequency', 1, '2025-05-21 18:57:24'),
(6, 'نطاق مخصص', 'Custom Domain', 'إمكانية استخدام نطاق مخصص', 'Ability to use a custom domain', 'custom_domain', 1, '2025-05-21 18:57:24'),
(7, 'دعم ذو أولوية', 'Priority Support', 'الحصول على دعم فني ذو أولوية', 'Get priority technical support', 'priority_support', 1, '2025-05-21 18:57:24');

-- --------------------------------------------------------

--
-- بنية الجدول `subscription_plans`
--

CREATE TABLE `subscription_plans` (
  `plan_id` int NOT NULL,
  `plan_name_ar` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `plan_name_en` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `description_ar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `description_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `price_monthly` decimal(10,2) NOT NULL,
  `price_yearly` decimal(10,2) NOT NULL,
  `max_users` int NOT NULL DEFAULT '5',
  `max_programs` int NOT NULL DEFAULT '3',
  `max_storage_gb` int NOT NULL DEFAULT '5',
  `features_json` json DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `subscription_plans`
--

INSERT INTO `subscription_plans` (`plan_id`, `plan_name_ar`, `plan_name_en`, `description_ar`, `description_en`, `price_monthly`, `price_yearly`, `max_users`, `max_programs`, `max_storage_gb`, `features_json`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'الخطة الأساسية', 'Basic Plan', 'خطة اشتراك أساسية مناسبة للشركات الصغيرة والمبتدئين', 'Basic subscription plan suitable for small businesses and beginners', 2.00, 5.00, 5, 3, 5, '{\"backup\": \"weekly\", \"support\": \"email\", \"custom_domain\": false, \"priority_support\": false}', 1, '2025-05-21 18:52:46', '2025-05-21 21:31:56'),
(2, 'الخطة المتوسطة', 'Standard Plan', 'خطة اشتراك متوسطة مناسبة للشركات المتوسطة', 'Standard subscription plan suitable for medium-sized businesses', 100.00, 1000.00, 15, 7, 20, '{\"backup\": \"daily\", \"support\": \"email,phone\", \"custom_domain\": true, \"priority_support\": false}', 1, '2025-05-21 18:52:46', '2025-05-21 18:52:46'),
(3, 'الخطة المتقدمة', 'Premium Plan', 'خطة اشتراك متقدمة مناسبة للشركات الكبيرة والمؤسسات', 'Premium subscription plan suitable for large businesses and enterprises', 200.00, 2000.00, 50, 15, 100, '{\"backup\": \"realtime\", \"support\": \"email,phone,chat\", \"custom_domain\": true, \"priority_support\": true}', 1, '2025-05-21 18:52:46', '2025-05-21 18:52:46');

-- --------------------------------------------------------

--
-- بنية الجدول `system_modules`
--

CREATE TABLE `system_modules` (
  `module_id` int NOT NULL,
  `module_code` varchar(50) NOT NULL,
  `module_name_ar` varchar(100) NOT NULL,
  `module_name_en` varchar(100) NOT NULL,
  `module_description_ar` text,
  `module_description_en` text,
  `base_url` varchar(100) NOT NULL,
  `icon_name` varchar(50) DEFAULT NULL,
  `module_type` enum('core','addon','premium') DEFAULT 'addon',
  `version` varchar(20) DEFAULT '1.0.0',
  `is_active` tinyint(1) DEFAULT '1',
  `display_order` int DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- إرجاع أو استيراد بيانات الجدول `system_modules`
--

INSERT INTO `system_modules` (`module_id`, `module_code`, `module_name_ar`, `module_name_en`, `module_description_ar`, `module_description_en`, `base_url`, `icon_name`, `module_type`, `version`, `is_active`, `display_order`, `created_at`) VALUES
(1, 'inventory', 'المخزون', 'Inventory Management Module', 'إدارة شاملة للمنتجات والمستودعات وحركة المخزون', 'Complete management of products, warehouses and inventory movements', 'inventory', 'fas fa-boxes', 'addon', '1.0.0', 1, 1, '2025-05-28 18:59:59'),
(2, 'purchases', 'المشتريات', 'Purchases Management Module', 'إدارة طلبات الشراء والموردين وفواتير الشراء', 'Management of purchase orders, suppliers and purchase invoices', 'purchases', 'fas fa-shopping-cart', 'addon', '1.0.0', 1, 2, '2025-05-28 18:59:59'),
(3, 'sales', 'المبيعات', 'Sales Management Module', 'إدارة العملاء وعروض الأسعار وفواتير المبيعات', 'Management of customers, quotations and sales invoices', 'sales', 'fas fa-chart-line', 'addon', '1.0.0', 1, 3, '2025-05-28 18:59:59'),
(4, 'accounting', 'المحاسبة', 'Accounting Module', 'النظام المحاسبي الكامل مع دليل الحسابات والقيود', 'Complete accounting system with chart of accounts and journal entries', 'accounting', 'fas fa-calculator', 'addon', '1.0.0', 1, 4, '2025-05-28 18:59:59'),
(5, 'hr', 'الموارد البشرية', 'Human Resources Module', 'إدارة الموظفين والرواتب والحضور والإجازات', 'Management of employees, payroll, attendance and leaves', 'hr', 'fas fa-users', 'addon', '1.0.0', 1, 5, '2025-05-28 18:59:59'),
(6, 'reports', 'التقارير', 'Reports Module', 'تقارير شاملة لجميع أقسام النظام', 'Comprehensive reports for all system modules', 'reports', 'fas fa-chart-bar', 'core', '1.0.0', 1, 6, '2025-05-28 18:59:59');

-- --------------------------------------------------------

--
-- بنية الجدول `tasks`
--

CREATE TABLE `tasks` (
  `task_id` int NOT NULL,
  `company_id` int NOT NULL,
  `program_id` int DEFAULT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `status` enum('pending','in_progress','completed','canceled','deferred') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'pending',
  `priority` enum('low','medium','high','urgent') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'medium',
  `created_by` int NOT NULL,
  `assigned_to` int DEFAULT NULL,
  `due_date` datetime DEFAULT NULL,
  `start_date` datetime DEFAULT NULL,
  `completion_date` datetime DEFAULT NULL,
  `parent_task_id` int DEFAULT NULL,
  `progress_percentage` int DEFAULT '0',
  `is_recurring` tinyint(1) NOT NULL DEFAULT '0',
  `recurrence_pattern` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `recurrence_end_date` datetime DEFAULT NULL,
  `related_entity_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `related_entity_id` int DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `users`
--

CREATE TABLE `users` (
  `UserID` int NOT NULL,
  `RegistrationDate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UserCode` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `UserName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `FirstName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `LastName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `ProfilePicture` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `PhoneNumber` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `Email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `PasswordHash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `AccountStatus` enum('Active','Inactive') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'Active',
  `last_activity` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `theme` enum('light','dark') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'light',
  `language` enum('العربية','English') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'العربية',
  `sidebar_mode` enum('show','hide') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'hide',
  `current_company_id` int DEFAULT NULL,
  `Content_Mode` enum('large','small') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'large',
  `SoundNotifications` enum('Enabled','Disabled') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'Enabled',
  `email_verified` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `users`
--

INSERT INTO `users` (`UserID`, `RegistrationDate`, `UserCode`, `UserName`, `FirstName`, `LastName`, `ProfilePicture`, `PhoneNumber`, `Email`, `PasswordHash`, `AccountStatus`, `last_activity`, `theme`, `language`, `sidebar_mode`, `current_company_id`, `Content_Mode`, `SoundNotifications`, `email_verified`) VALUES
(32, '2025-05-17 19:08:37', '0e7df9606820012ccdbd50408e1ffade', 'alwazai', 'abdulla', 'alboqami', 'public/uploads/profile_pictures/1748348519_صورة خبز.jpeg', '+53 *********', '<EMAIL>', '$2y$10$LF3QDlixDZlX9LR.K0gQTuyeRYZlNg0G.oIywVffUPDjmCX9.6v/2', 'Active', '2025-05-31 16:11:07', 'light', 'العربية', 'show', 4, 'large', 'Enabled', 0),
(35, '2025-05-19 21:11:40', '1897c7041673db55f68644db400a20e2', 'abdullaSh', 'abdullah', 'alboqami', NULL, '+53 *********', '<EMAIL>', '$2y$10$rylNXUO4gARPGRVD3jNYj.amKdJLmPnPK4YodoiaCxPJBFGr8soAW', 'Active', '2025-05-31 11:33:09', 'light', 'العربية', 'show', 4, 'large', 'Enabled', 0);

-- --------------------------------------------------------

--
-- بنية الجدول `user_notification_settings`
--

CREATE TABLE `user_notification_settings` (
  `setting_id` int NOT NULL,
  `user_id` int NOT NULL,
  `notification_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `in_app_enabled` tinyint(1) NOT NULL DEFAULT '1',
  `email_enabled` tinyint(1) NOT NULL DEFAULT '0',
  `sms_enabled` tinyint(1) NOT NULL DEFAULT '0',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Indexes for dumped tables
--

--
-- فهارس للجدول `activity_log`
--
ALTER TABLE `activity_log`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `company_id` (`company_id`);

--
-- فهارس للجدول `chat_attachments`
--
ALTER TABLE `chat_attachments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_message_id` (`message_id`),
  ADD KEY `idx_file_type` (`file_type`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- فهارس للجدول `chat_conversations`
--
ALTER TABLE `chat_conversations`
  ADD PRIMARY KEY (`conversation_id`),
  ADD KEY `company_id` (`company_id`),
  ADD KEY `created_by` (`created_by`);

--
-- فهارس للجدول `chat_conversation_participants`
--
ALTER TABLE `chat_conversation_participants`
  ADD PRIMARY KEY (`participant_id`),
  ADD UNIQUE KEY `conversation_user_unique` (`conversation_id`,`user_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `last_read_message_id` (`last_read_message_id`);

--
-- فهارس للجدول `chat_messages`
--
ALTER TABLE `chat_messages`
  ADD PRIMARY KEY (`message_id`),
  ADD KEY `conversation_id` (`conversation_id`),
  ADD KEY `sender_id` (`sender_id`),
  ADD KEY `reply_to_message_id` (`reply_to_message_id`);

--
-- فهارس للجدول `chat_message_reactions`
--
ALTER TABLE `chat_message_reactions`
  ADD PRIMARY KEY (`reaction_id`),
  ADD UNIQUE KEY `message_user_reaction_unique` (`message_id`,`user_id`,`reaction_type`),
  ADD KEY `user_id` (`user_id`);

--
-- فهارس للجدول `chat_message_reads`
--
ALTER TABLE `chat_message_reads`
  ADD PRIMARY KEY (`read_id`),
  ADD UNIQUE KEY `message_user_unique` (`message_id`,`user_id`),
  ADD KEY `user_id` (`user_id`);

--
-- فهارس للجدول `chat_notifications`
--
ALTER TABLE `chat_notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_conversation_id` (`conversation_id`),
  ADD KEY `idx_message_id` (`message_id`),
  ADD KEY `idx_type` (`type`),
  ADD KEY `idx_is_read` (`is_read`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- فهارس للجدول `chat_notification_settings`
--
ALTER TABLE `chat_notification_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_conversation` (`user_id`,`conversation_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_conversation_id` (`conversation_id`);

--
-- فهارس للجدول `chat_typing_status`
--
ALTER TABLE `chat_typing_status`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_conversation_user` (`conversation_id`,`user_id`),
  ADD KEY `idx_conversation_typing` (`conversation_id`,`is_typing`),
  ADD KEY `idx_updated_at` (`updated_at`);

--
-- فهارس للجدول `chat_user_status`
--
ALTER TABLE `chat_user_status`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user` (`user_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_last_seen` (`last_seen`);

--
-- فهارس للجدول `companies`
--
ALTER TABLE `companies`
  ADD PRIMARY KEY (`CompanyID`),
  ADD KEY `OwnerID` (`OwnerID`),
  ADD KEY `subscription_id` (`subscription_id`),
  ADD KEY `idx_subscription_status` (`subscription_status`),
  ADD KEY `idx_industry_type` (`industry_type`),
  ADD KEY `idx_company_size` (`company_size`),
  ADD KEY `idx_deactivation_date` (`deactivation_date`);

--
-- فهارس للجدول `company_modules`
--
ALTER TABLE `company_modules`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `company_module_unique` (`company_id`,`module_id`),
  ADD KEY `module_id` (`module_id`),
  ADD KEY `installed_by` (`installed_by`);

--
-- فهارس للجدول `company_users`
--
ALTER TABLE `company_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_company_unique` (`user_id`,`company_id`),
  ADD KEY `company_id` (`company_id`),
  ADD KEY `position_id` (`position_id`),
  ADD KEY `added_by` (`added_by`);

--
-- فهارس للجدول `inventory_audits`
--
ALTER TABLE `inventory_audits`
  ADD PRIMARY KEY (`audit_id`),
  ADD UNIQUE KEY `company_audit_number_unique` (`company_id`,`audit_number`),
  ADD KEY `company_id` (`company_id`),
  ADD KEY `warehouse_id` (`warehouse_id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `updated_by` (`updated_by`),
  ADD KEY `completed_by` (`completed_by`),
  ADD KEY `module_code` (`module_code`);

--
-- فهارس للجدول `inventory_audit_items`
--
ALTER TABLE `inventory_audit_items`
  ADD PRIMARY KEY (`audit_item_id`),
  ADD KEY `company_id` (`company_id`),
  ADD KEY `audit_id` (`audit_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `counted_by` (`counted_by`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `updated_by` (`updated_by`),
  ADD KEY `module_code` (`module_code`);

--
-- فهارس للجدول `inventory_categories`
--
ALTER TABLE `inventory_categories`
  ADD PRIMARY KEY (`category_id`),
  ADD UNIQUE KEY `company_category_code_unique` (`company_id`,`category_code`),
  ADD KEY `company_id` (`company_id`),
  ADD KEY `parent_category_id` (`parent_category_id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `updated_by` (`updated_by`),
  ADD KEY `module_code` (`module_code`);

--
-- فهارس للجدول `inventory_movements`
--
ALTER TABLE `inventory_movements`
  ADD PRIMARY KEY (`movement_id`),
  ADD UNIQUE KEY `company_movement_number_unique` (`company_id`,`movement_number`),
  ADD KEY `company_id` (`company_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `warehouse_id` (`warehouse_id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `updated_by` (`updated_by`),
  ADD KEY `module_code` (`module_code`),
  ADD KEY `reference_type_id` (`reference_type`,`reference_id`);

--
-- فهارس للجدول `inventory_products`
--
ALTER TABLE `inventory_products`
  ADD PRIMARY KEY (`product_id`),
  ADD UNIQUE KEY `company_product_code_unique` (`company_id`,`product_code`),
  ADD UNIQUE KEY `company_barcode_unique` (`company_id`,`barcode`),
  ADD KEY `company_id` (`company_id`),
  ADD KEY `category_id` (`category_id`),
  ADD KEY `unit_id` (`unit_id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `updated_by` (`updated_by`),
  ADD KEY `module_code` (`module_code`);

--
-- فهارس للجدول `inventory_product_prices`
--
ALTER TABLE `inventory_product_prices`
  ADD PRIMARY KEY (`price_id`),
  ADD KEY `company_id` (`company_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `updated_by` (`updated_by`),
  ADD KEY `module_code` (`module_code`),
  ADD KEY `price_type_active` (`price_type`,`is_active`);

--
-- فهارس للجدول `inventory_stock`
--
ALTER TABLE `inventory_stock`
  ADD PRIMARY KEY (`stock_id`),
  ADD UNIQUE KEY `company_product_warehouse_unique` (`company_id`,`product_id`,`warehouse_id`),
  ADD KEY `company_id` (`company_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `warehouse_id` (`warehouse_id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `updated_by` (`updated_by`),
  ADD KEY `module_code` (`module_code`);

--
-- فهارس للجدول `inventory_transfers`
--
ALTER TABLE `inventory_transfers`
  ADD PRIMARY KEY (`transfer_id`),
  ADD UNIQUE KEY `company_transfer_number_unique` (`company_id`,`transfer_number`),
  ADD KEY `company_id` (`company_id`),
  ADD KEY `from_warehouse_id` (`from_warehouse_id`),
  ADD KEY `to_warehouse_id` (`to_warehouse_id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `updated_by` (`updated_by`),
  ADD KEY `approved_by` (`approved_by`),
  ADD KEY `module_code` (`module_code`);

--
-- فهارس للجدول `inventory_transfer_items`
--
ALTER TABLE `inventory_transfer_items`
  ADD PRIMARY KEY (`transfer_item_id`),
  ADD KEY `company_id` (`company_id`),
  ADD KEY `transfer_id` (`transfer_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `updated_by` (`updated_by`),
  ADD KEY `module_code` (`module_code`);

--
-- فهارس للجدول `inventory_units`
--
ALTER TABLE `inventory_units`
  ADD PRIMARY KEY (`unit_id`),
  ADD UNIQUE KEY `company_unit_code_unique` (`company_id`,`unit_code`),
  ADD KEY `company_id` (`company_id`),
  ADD KEY `base_unit_id` (`base_unit_id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `updated_by` (`updated_by`),
  ADD KEY `module_code` (`module_code`);

--
-- فهارس للجدول `inventory_warehouses`
--
ALTER TABLE `inventory_warehouses`
  ADD PRIMARY KEY (`warehouse_id`),
  ADD UNIQUE KEY `company_warehouse_code_unique` (`company_id`,`warehouse_code`),
  ADD KEY `company_id` (`company_id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `updated_by` (`updated_by`),
  ADD KEY `module_code` (`module_code`);

--
-- فهارس للجدول `inventory_adjustments`
--
ALTER TABLE `inventory_adjustments`
  ADD PRIMARY KEY (`adjustment_id`),
  ADD UNIQUE KEY `company_adjustment_number_unique` (`company_id`,`adjustment_number`),
  ADD KEY `company_id` (`company_id`),
  ADD KEY `warehouse_id` (`warehouse_id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `updated_by` (`updated_by`),
  ADD KEY `approved_by` (`approved_by`),
  ADD KEY `module_code` (`module_code`);

--
-- فهارس للجدول `inventory_adjustment_items`
--
ALTER TABLE `inventory_adjustment_items`
  ADD PRIMARY KEY (`adjustment_item_id`),
  ADD KEY `company_id` (`company_id`),
  ADD KEY `adjustment_id` (`adjustment_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `updated_by` (`updated_by`),
  ADD KEY `module_code` (`module_code`);

--
-- فهارس للجدول `inventory_locations`
--
ALTER TABLE `inventory_locations`
  ADD PRIMARY KEY (`location_id`),
  ADD UNIQUE KEY `company_warehouse_location_code_unique` (`company_id`,`warehouse_id`,`location_code`),
  ADD KEY `company_id` (`company_id`),
  ADD KEY `warehouse_id` (`warehouse_id`),
  ADD KEY `parent_location_id` (`parent_location_id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `updated_by` (`updated_by`),
  ADD KEY `module_code` (`module_code`);

--
-- فهارس للجدول `inventory_alerts`
--
ALTER TABLE `inventory_alerts`
  ADD PRIMARY KEY (`alert_id`),
  ADD KEY `company_id` (`company_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `warehouse_id` (`warehouse_id`),
  ADD KEY `resolved_by` (`resolved_by`),
  ADD KEY `module_code` (`module_code`),
  ADD KEY `alert_type_severity` (`alert_type`,`severity`);

--
-- فهارس للجدول `inventory_reorder_requests`
--
ALTER TABLE `inventory_reorder_requests`
  ADD PRIMARY KEY (`request_id`),
  ADD UNIQUE KEY `company_request_number_unique` (`company_id`,`request_number`),
  ADD KEY `company_id` (`company_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `warehouse_id` (`warehouse_id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `updated_by` (`updated_by`),
  ADD KEY `approved_by` (`approved_by`),
  ADD KEY `module_code` (`module_code`);

--
-- فهارس للجدول `inventory_reports`
--
ALTER TABLE `inventory_reports`
  ADD PRIMARY KEY (`report_id`),
  ADD KEY `company_id` (`company_id`),
  ADD KEY `generated_by` (`generated_by`),
  ADD KEY `module_code` (`module_code`),
  ADD KEY `report_type_status` (`report_type`,`status`);

--
-- فهارس للجدول `module_programs`
--
ALTER TABLE `module_programs`
  ADD PRIMARY KEY (`program_id`),
  ADD KEY `company_id` (`company_id`),
  ADD KEY `module_id` (`module_id`),
  ADD KEY `parent_program_id` (`parent_program_id`);

--
-- فهارس للجدول `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`notification_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `company_id` (`company_id`),
  ADD KEY `template_id` (`template_id`),
  ADD KEY `notification_type` (`notification_type`),
  ADD KEY `related_entity` (`related_entity_type`,`related_entity_id`);

--
-- فهارس للجدول `notification_templates`
--
ALTER TABLE `notification_templates`
  ADD PRIMARY KEY (`template_id`),
  ADD UNIQUE KEY `template_code` (`template_code`);

--
-- فهارس للجدول `passwordreset`
--
ALTER TABLE `passwordreset`
  ADD PRIMARY KEY (`ID`),
  ADD KEY `UserID` (`UserID`);

--
-- فهارس للجدول `payments`
--
ALTER TABLE `payments`
  ADD PRIMARY KEY (`payment_id`),
  ADD KEY `subscription_id` (`subscription_id`),
  ADD KEY `company_id` (`company_id`);

--
-- فهارس للجدول `permissions`
--
ALTER TABLE `permissions`
  ADD PRIMARY KEY (`permission_id`),
  ADD UNIQUE KEY `position_program_unique` (`company_id`,`position_id`,`program_id`),
  ADD KEY `position_id` (`position_id`),
  ADD KEY `program_id` (`program_id`),
  ADD KEY `created_by` (`created_by`);

--
-- فهارس للجدول `plan_available_programs`
--
ALTER TABLE `plan_available_programs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `plan_program_unique` (`plan_id`,`program_id`),
  ADD KEY `program_id` (`program_id`);

--
-- فهارس للجدول `plan_features`
--
ALTER TABLE `plan_features`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `plan_feature_unique` (`plan_id`,`feature_id`),
  ADD KEY `feature_id` (`feature_id`);

--
-- فهارس للجدول `positions`
--
ALTER TABLE `positions`
  ADD PRIMARY KEY (`position_id`),
  ADD KEY `company_id` (`company_id`),
  ADD KEY `created_by` (`created_by`);

--
-- فهارس للجدول `programs`
--
ALTER TABLE `programs`
  ADD PRIMARY KEY (`ProgramID`);

--
-- فهارس للجدول `program_categories`
--
ALTER TABLE `program_categories`
  ADD PRIMARY KEY (`category_id`);

--
-- فهارس للجدول `subscriptions`
--
ALTER TABLE `subscriptions`
  ADD PRIMARY KEY (`subscription_id`),
  ADD KEY `company_id` (`company_id`),
  ADD KEY `plan_id` (`plan_id`);

--
-- فهارس للجدول `subscription_features`
--
ALTER TABLE `subscription_features`
  ADD PRIMARY KEY (`feature_id`),
  ADD UNIQUE KEY `feature_code` (`feature_code`);

--
-- فهارس للجدول `subscription_plans`
--
ALTER TABLE `subscription_plans`
  ADD PRIMARY KEY (`plan_id`);

--
-- فهارس للجدول `system_modules`
--
ALTER TABLE `system_modules`
  ADD PRIMARY KEY (`module_id`),
  ADD UNIQUE KEY `module_code` (`module_code`);

--
-- فهارس للجدول `tasks`
--
ALTER TABLE `tasks`
  ADD PRIMARY KEY (`task_id`),
  ADD KEY `company_id` (`company_id`),
  ADD KEY `program_id` (`program_id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `assigned_to` (`assigned_to`),
  ADD KEY `parent_task_id` (`parent_task_id`),
  ADD KEY `status_priority` (`status`,`priority`);

--
-- فهارس للجدول `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`UserID`),
  ADD UNIQUE KEY `UserCode` (`UserCode`),
  ADD UNIQUE KEY `UserName` (`UserName`),
  ADD UNIQUE KEY `Email` (`Email`);

--
-- فهارس للجدول `user_notification_settings`
--
ALTER TABLE `user_notification_settings`
  ADD PRIMARY KEY (`setting_id`),
  ADD UNIQUE KEY `user_notification_type_unique` (`user_id`,`notification_type`),
  ADD KEY `notification_type` (`notification_type`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `activity_log`
--
ALTER TABLE `activity_log`
  MODIFY `log_id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `chat_attachments`
--
ALTER TABLE `chat_attachments`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `chat_conversations`
--
ALTER TABLE `chat_conversations`
  MODIFY `conversation_id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `chat_conversation_participants`
--
ALTER TABLE `chat_conversation_participants`
  MODIFY `participant_id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `chat_messages`
--
ALTER TABLE `chat_messages`
  MODIFY `message_id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `chat_message_reactions`
--
ALTER TABLE `chat_message_reactions`
  MODIFY `reaction_id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `chat_message_reads`
--
ALTER TABLE `chat_message_reads`
  MODIFY `read_id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `chat_notifications`
--
ALTER TABLE `chat_notifications`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `chat_notification_settings`
--
ALTER TABLE `chat_notification_settings`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `chat_typing_status`
--
ALTER TABLE `chat_typing_status`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `chat_user_status`
--
ALTER TABLE `chat_user_status`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `companies`
--
ALTER TABLE `companies`
  MODIFY `CompanyID` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `company_modules`
--
ALTER TABLE `company_modules`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `company_users`
--
ALTER TABLE `company_users`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `inventory_audits`
--
ALTER TABLE `inventory_audits`
  MODIFY `audit_id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `inventory_audit_items`
--
ALTER TABLE `inventory_audit_items`
  MODIFY `audit_item_id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `inventory_categories`
--
ALTER TABLE `inventory_categories`
  MODIFY `category_id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `inventory_movements`
--
ALTER TABLE `inventory_movements`
  MODIFY `movement_id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `inventory_products`
--
ALTER TABLE `inventory_products`
  MODIFY `product_id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `inventory_product_prices`
--
ALTER TABLE `inventory_product_prices`
  MODIFY `price_id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `inventory_stock`
--
ALTER TABLE `inventory_stock`
  MODIFY `stock_id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `inventory_transfers`
--
ALTER TABLE `inventory_transfers`
  MODIFY `transfer_id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `inventory_transfer_items`
--
ALTER TABLE `inventory_transfer_items`
  MODIFY `transfer_item_id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `inventory_units`
--
ALTER TABLE `inventory_units`
  MODIFY `unit_id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT for table `inventory_warehouses`
--
ALTER TABLE `inventory_warehouses`
  MODIFY `warehouse_id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `inventory_adjustments`
--
ALTER TABLE `inventory_adjustments`
  MODIFY `adjustment_id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `inventory_adjustment_items`
--
ALTER TABLE `inventory_adjustment_items`
  MODIFY `adjustment_item_id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `inventory_locations`
--
ALTER TABLE `inventory_locations`
  MODIFY `location_id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `inventory_alerts`
--
ALTER TABLE `inventory_alerts`
  MODIFY `alert_id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `inventory_reorder_requests`
--
ALTER TABLE `inventory_reorder_requests`
  MODIFY `request_id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `inventory_reports`
--
ALTER TABLE `inventory_reports`
  MODIFY `report_id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `module_programs`
--
ALTER TABLE `module_programs`
  MODIFY `program_id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=34;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `notification_id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `notification_templates`
--
ALTER TABLE `notification_templates`
  MODIFY `template_id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `passwordreset`
--
ALTER TABLE `passwordreset`
  MODIFY `ID` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=127;

--
-- AUTO_INCREMENT for table `payments`
--
ALTER TABLE `payments`
  MODIFY `payment_id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=80;

--
-- AUTO_INCREMENT for table `permissions`
--
ALTER TABLE `permissions`
  MODIFY `permission_id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=68;

--
-- AUTO_INCREMENT for table `plan_available_programs`
--
ALTER TABLE `plan_available_programs`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `plan_features`
--
ALTER TABLE `plan_features`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT for table `positions`
--
ALTER TABLE `positions`
  MODIFY `position_id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `programs`
--
ALTER TABLE `programs`
  MODIFY `ProgramID` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `program_categories`
--
ALTER TABLE `program_categories`
  MODIFY `category_id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `subscriptions`
--
ALTER TABLE `subscriptions`
  MODIFY `subscription_id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=82;

--
-- AUTO_INCREMENT for table `subscription_features`
--
ALTER TABLE `subscription_features`
  MODIFY `feature_id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `subscription_plans`
--
ALTER TABLE `subscription_plans`
  MODIFY `plan_id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `system_modules`
--
ALTER TABLE `system_modules`
  MODIFY `module_id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `tasks`
--
ALTER TABLE `tasks`
  MODIFY `task_id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `UserID` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=36;

--
-- AUTO_INCREMENT for table `user_notification_settings`
--
ALTER TABLE `user_notification_settings`
  MODIFY `setting_id` int NOT NULL AUTO_INCREMENT;

--
-- القيود المفروضة على الجداول الملقاة
--

--
-- قيود الجداول `company_modules`
--
ALTER TABLE `company_modules`
  ADD CONSTRAINT `company_modules_ibfk_1` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`),
  ADD CONSTRAINT `company_modules_ibfk_2` FOREIGN KEY (`module_id`) REFERENCES `system_modules` (`module_id`),
  ADD CONSTRAINT `company_modules_ibfk_3` FOREIGN KEY (`installed_by`) REFERENCES `users` (`UserID`);

--
-- قيود الجداول `company_users`
--
ALTER TABLE `company_users`
  ADD CONSTRAINT `company_users_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `company_users_ibfk_2` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`),
  ADD CONSTRAINT `company_users_ibfk_3` FOREIGN KEY (`position_id`) REFERENCES `positions` (`position_id`),
  ADD CONSTRAINT `company_users_ibfk_4` FOREIGN KEY (`added_by`) REFERENCES `users` (`UserID`);

--
-- قيود الجداول `inventory_audits`
--
ALTER TABLE `inventory_audits`
  ADD CONSTRAINT `fk_inv_audits_company` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_audits_completed_by` FOREIGN KEY (`completed_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_audits_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_audits_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_audits_warehouse` FOREIGN KEY (`warehouse_id`) REFERENCES `inventory_warehouses` (`warehouse_id`) ON DELETE CASCADE;

--
-- قيود الجداول `inventory_audit_items`
--
ALTER TABLE `inventory_audit_items`
  ADD CONSTRAINT `fk_inv_audit_items_audit` FOREIGN KEY (`audit_id`) REFERENCES `inventory_audits` (`audit_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_audit_items_company` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_audit_items_counted_by` FOREIGN KEY (`counted_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_audit_items_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_audit_items_product` FOREIGN KEY (`product_id`) REFERENCES `inventory_products` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_audit_items_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`UserID`);

--
-- قيود الجداول `inventory_categories`
--
ALTER TABLE `inventory_categories`
  ADD CONSTRAINT `fk_inv_categories_company` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_categories_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_categories_parent` FOREIGN KEY (`parent_category_id`) REFERENCES `inventory_categories` (`category_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_inv_categories_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`UserID`);

--
-- قيود الجداول `inventory_movements`
--
ALTER TABLE `inventory_movements`
  ADD CONSTRAINT `fk_inv_movements_company` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_movements_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_movements_product` FOREIGN KEY (`product_id`) REFERENCES `inventory_products` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_movements_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_movements_warehouse` FOREIGN KEY (`warehouse_id`) REFERENCES `inventory_warehouses` (`warehouse_id`) ON DELETE CASCADE;

--
-- قيود الجداول `inventory_products`
--
ALTER TABLE `inventory_products`
  ADD CONSTRAINT `fk_inv_products_category` FOREIGN KEY (`category_id`) REFERENCES `inventory_categories` (`category_id`),
  ADD CONSTRAINT `fk_inv_products_company` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_products_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_products_unit` FOREIGN KEY (`unit_id`) REFERENCES `inventory_units` (`unit_id`),
  ADD CONSTRAINT `fk_inv_products_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`UserID`);

--
-- قيود الجداول `inventory_product_prices`
--
ALTER TABLE `inventory_product_prices`
  ADD CONSTRAINT `fk_inv_prices_company` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_prices_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_prices_product` FOREIGN KEY (`product_id`) REFERENCES `inventory_products` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_prices_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`UserID`);

--
-- قيود الجداول `inventory_stock`
--
ALTER TABLE `inventory_stock`
  ADD CONSTRAINT `fk_inv_stock_company` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_stock_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_stock_product` FOREIGN KEY (`product_id`) REFERENCES `inventory_products` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_stock_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_stock_warehouse` FOREIGN KEY (`warehouse_id`) REFERENCES `inventory_warehouses` (`warehouse_id`) ON DELETE CASCADE;

--
-- قيود الجداول `inventory_transfers`
--
ALTER TABLE `inventory_transfers`
  ADD CONSTRAINT `fk_inv_transfers_approved_by` FOREIGN KEY (`approved_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_transfers_company` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_transfers_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_transfers_from_warehouse` FOREIGN KEY (`from_warehouse_id`) REFERENCES `inventory_warehouses` (`warehouse_id`),
  ADD CONSTRAINT `fk_inv_transfers_to_warehouse` FOREIGN KEY (`to_warehouse_id`) REFERENCES `inventory_warehouses` (`warehouse_id`),
  ADD CONSTRAINT `fk_inv_transfers_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`UserID`);

--
-- قيود الجداول `inventory_transfer_items`
--
ALTER TABLE `inventory_transfer_items`
  ADD CONSTRAINT `fk_inv_transfer_items_company` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_transfer_items_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_transfer_items_product` FOREIGN KEY (`product_id`) REFERENCES `inventory_products` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_transfer_items_transfer` FOREIGN KEY (`transfer_id`) REFERENCES `inventory_transfers` (`transfer_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_transfer_items_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`UserID`);

--
-- قيود الجداول `inventory_units`
--
ALTER TABLE `inventory_units`
  ADD CONSTRAINT `fk_inv_units_base_unit` FOREIGN KEY (`base_unit_id`) REFERENCES `inventory_units` (`unit_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_inv_units_company` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_units_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_units_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`UserID`);

--
-- قيود الجداول `inventory_warehouses`
--
ALTER TABLE `inventory_warehouses`
  ADD CONSTRAINT `fk_inv_warehouses_company` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_warehouses_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_warehouses_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`UserID`);

--
-- قيود الجداول `inventory_adjustments`
--
ALTER TABLE `inventory_adjustments`
  ADD CONSTRAINT `fk_inv_adjustments_company` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_adjustments_warehouse` FOREIGN KEY (`warehouse_id`) REFERENCES `inventory_warehouses` (`warehouse_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_adjustments_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_adjustments_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_adjustments_approved_by` FOREIGN KEY (`approved_by`) REFERENCES `users` (`UserID`);

--
-- قيود الجداول `inventory_adjustment_items`
--
ALTER TABLE `inventory_adjustment_items`
  ADD CONSTRAINT `fk_inv_adjustment_items_company` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_adjustment_items_adjustment` FOREIGN KEY (`adjustment_id`) REFERENCES `inventory_adjustments` (`adjustment_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_adjustment_items_product` FOREIGN KEY (`product_id`) REFERENCES `inventory_products` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_adjustment_items_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_adjustment_items_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`UserID`);

--
-- قيود الجداول `inventory_locations`
--
ALTER TABLE `inventory_locations`
  ADD CONSTRAINT `fk_inv_locations_company` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_locations_warehouse` FOREIGN KEY (`warehouse_id`) REFERENCES `inventory_warehouses` (`warehouse_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_locations_parent` FOREIGN KEY (`parent_location_id`) REFERENCES `inventory_locations` (`location_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_inv_locations_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_locations_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`UserID`);

--
-- قيود الجداول `inventory_alerts`
--
ALTER TABLE `inventory_alerts`
  ADD CONSTRAINT `fk_inv_alerts_company` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_alerts_product` FOREIGN KEY (`product_id`) REFERENCES `inventory_products` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_alerts_warehouse` FOREIGN KEY (`warehouse_id`) REFERENCES `inventory_warehouses` (`warehouse_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_alerts_resolved_by` FOREIGN KEY (`resolved_by`) REFERENCES `users` (`UserID`);

--
-- قيود الجداول `inventory_reorder_requests`
--
ALTER TABLE `inventory_reorder_requests`
  ADD CONSTRAINT `fk_inv_reorder_requests_company` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_reorder_requests_product` FOREIGN KEY (`product_id`) REFERENCES `inventory_products` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_reorder_requests_warehouse` FOREIGN KEY (`warehouse_id`) REFERENCES `inventory_warehouses` (`warehouse_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_reorder_requests_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_reorder_requests_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_reorder_requests_approved_by` FOREIGN KEY (`approved_by`) REFERENCES `users` (`UserID`);

--
-- قيود الجداول `inventory_reports`
--
ALTER TABLE `inventory_reports`
  ADD CONSTRAINT `fk_inv_reports_company` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_reports_generated_by` FOREIGN KEY (`generated_by`) REFERENCES `users` (`UserID`);

--
-- تحديث قيود جدول `inventory_stock` لإضافة location_id
--
ALTER TABLE `inventory_stock`
  ADD CONSTRAINT `fk_inv_stock_location` FOREIGN KEY (`location_id`) REFERENCES `inventory_locations` (`location_id`) ON DELETE SET NULL;

--
-- قيود الجداول `module_programs`
--
ALTER TABLE `module_programs`
  ADD CONSTRAINT `module_programs_ibfk_1` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`),
  ADD CONSTRAINT `module_programs_ibfk_2` FOREIGN KEY (`module_id`) REFERENCES `system_modules` (`module_id`),
  ADD CONSTRAINT `module_programs_ibfk_3` FOREIGN KEY (`parent_program_id`) REFERENCES `module_programs` (`program_id`);

--
-- قيود الجداول `permissions`
--
ALTER TABLE `permissions`
  ADD CONSTRAINT `permissions_ibfk_1` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`),
  ADD CONSTRAINT `permissions_ibfk_2` FOREIGN KEY (`position_id`) REFERENCES `positions` (`position_id`),
  ADD CONSTRAINT `permissions_ibfk_3` FOREIGN KEY (`program_id`) REFERENCES `module_programs` (`program_id`),
  ADD CONSTRAINT `permissions_ibfk_4` FOREIGN KEY (`created_by`) REFERENCES `users` (`UserID`);

--
-- قيود الجداول `positions`
--
ALTER TABLE `positions`
  ADD CONSTRAINT `positions_ibfk_1` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`),
  ADD CONSTRAINT `positions_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`UserID`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
