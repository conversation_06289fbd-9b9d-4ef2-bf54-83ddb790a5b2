# نظام ERP المتكامل

نظام إدارة موارد المؤسسات (ERP) متكامل مع دعم الوحدات والشركات المتعددة، مبني بـ PHP مع بنية معمارية حديثة.

## 🚀 المميزات الرئيسية

- **نظام وحدات قابل للتوسع**: إضافة وحدات جديدة بسهولة
- **دعم الشركات المتعددة**: Multi-tenant architecture
- **نظام صلاحيات متقدم**: إدارة دقيقة للصلاحيات والأدوار
- **نظام اشتراكات**: خطط اشتراك مرنة مع بوابات دفع
- **نظام إشعارات**: إشعارات في التطبيق والبريد الإلكتروني
- **دعم متعدد اللغات**: العربية والإنجليزية
- **واجهة مستخدم حديثة**: تصميم متجاوب وسهل الاستخدام

## 📋 المتطلبات

- PHP >= 8.0
- MySQL >= 5.7
- Apache/Nginx
- Composer
- Node.js (للأصول الأمامية)

## 🛠️ التثبيت

1. **استنساخ المشروع:**
```bash
git clone https://github.com/your-repo/erp-system.git
cd erp-system
```

2. **تثبيت التبعيات:**
```bash
composer install
```

3. **إعداد البيئة:**
```bash
cp .env.example .env
# قم بتحرير ملف .env وإضافة إعدادات قاعدة البيانات
```

4. **إنشاء قاعدة البيانات:**
```bash
mysql -u root -p < database_sql_fixed.sql
```

5. **تعيين الصلاحيات:**
```bash
chmod -R 755 erpapp/storage/
chmod -R 755 erpapp/public/uploads/
```

## 📁 هيكل المشروع

```
erpapp/
├── App/
│   ├── Core/           # النواة الأساسية
│   ├── Controllers/    # المتحكمات العامة
│   ├── Helpers/        # المساعدات والوظائف
│   ├── Layouts/        # قوالب التخطيط
│   ├── Modules/        # الوحدات القابلة للتوسع
│   ├── System/         # وحدات النظام الأساسية
│   └── Views/          # العروض العامة
├── config/             # ملفات الإعدادات
├── public/             # الملفات العامة
├── resources/          # الموارد (اللغات، إلخ)
└── storage/            # التخزين (السجلات، الجلسات)
```

## 🔧 الاستخدام

### إضافة وحدة جديدة:

1. إنشاء مجلد الوحدة في `App/Modules/`
2. إنشاء فئة Module تمتد من `App\Core\Module`
3. تسجيل المسارات في `registerRoutes()`

### إدارة الصلاحيات:

```php
// التحقق من الصلاحية
if (PermissionManager::hasPermission('module_name', 'action')) {
    // تنفيذ العملية
}
```

## 🧪 الاختبارات

```bash
composer test
```

## 📝 المساهمة

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. Commit التغييرات
4. Push إلى الفرع
5. إنشاء Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة خاصة. راجع ملف LICENSE للتفاصيل.

## 📞 الدعم

للدعم والاستفسارات، يرجى التواصل معنا على:
- البريد الإلكتروني: <EMAIL>
- الموقع: https://systemfuture.com
