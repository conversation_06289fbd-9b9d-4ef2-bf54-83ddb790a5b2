<?php
$title = 'لوحة تحكم المشتريات';
$breadcrumb = [
    ['title' => 'الرئيسية', 'url' => '/'],
    ['title' => 'المشتريات', 'url' => '']
];
?>

<div class="container-fluid">
    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <?php foreach ($breadcrumb as $item): ?>
                            <?php if ($item['url']): ?>
                                <li class="breadcrumb-item"><a href="<?= base_url($item['url']) ?>"><?= $item['title'] ?></a></li>
                            <?php else: ?>
                                <li class="breadcrumb-item active"><?= $item['title'] ?></li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </ol>
                </div>
                <h4 class="page-title"><?= $title ?></h4>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row">
        <div class="col-md-6 col-xl-3">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate">إجمالي الموردين</h5>
                            <h3 class="my-2 py-1"><?= $stats['total_suppliers'] ?></h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="mdi mdi-truck text-primary" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-xl-3">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate">الموردين النشطين</h5>
                            <h3 class="my-2 py-1"><?= $stats['active_suppliers'] ?></h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="mdi mdi-check-circle text-success" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-xl-3">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate">مجموعات الموردين</h5>
                            <h3 class="my-2 py-1"><?= $stats['total_groups'] ?></h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="mdi mdi-folder-multiple text-info" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-xl-3">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate">موردين غير نشطين</h5>
                            <h3 class="my-2 py-1"><?= $stats['inactive_suppliers'] ?></h3>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="mdi mdi-close-circle text-warning" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">الإجراءات السريعة</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="d-grid">
                                <a href="<?= base_url('purchases/suppliers/create') ?>" class="btn btn-primary">
                                    <i class="mdi mdi-plus-circle me-2"></i>
                                    إضافة مورد جديد
                                </a>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-grid">
                                <a href="<?= base_url('purchases/supplier-groups/create') ?>" class="btn btn-success">
                                    <i class="mdi mdi-folder-plus me-2"></i>
                                    إضافة مجموعة موردين
                                </a>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-grid">
                                <a href="<?= base_url('purchases/suppliers') ?>" class="btn btn-info">
                                    <i class="mdi mdi-view-list me-2"></i>
                                    عرض جميع الموردين
                                </a>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-grid">
                                <a href="<?= base_url('purchases/supplier-groups') ?>" class="btn btn-warning">
                                    <i class="mdi mdi-folder-open me-2"></i>
                                    عرض مجموعات الموردين
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Suppliers -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">آخر الموردين المضافين</h4>
                </div>
                <div class="card-body">
                    <?php if (empty($recentSuppliers)): ?>
                        <div class="text-center py-4">
                            <i class="mdi mdi-truck-outline mdi-48px text-muted"></i>
                            <p class="text-muted mt-2">لا توجد موردين مضافين حتى الآن</p>
                            <a href="<?= base_url('purchases/suppliers/create') ?>" class="btn btn-primary btn-sm">
                                إضافة مورد جديد
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-centered table-nowrap table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>الرقم</th>
                                        <th>اسم المورد</th>
                                        <th>اسم الشركة</th>
                                        <th>المجموعة</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإضافة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recentSuppliers as $supplier): ?>
                                        <tr>
                                            <td><?= $supplier['entity_number'] ?></td>
                                            <td>
                                                <a href="<?= base_url('purchases/suppliers/' . $supplier['entity_number']) ?>" 
                                                   class="text-body fw-bold">
                                                    <?= htmlspecialchars($supplier['G_name_ar']) ?>
                                                </a>
                                            </td>
                                            <td><?= htmlspecialchars($supplier['S_company_name'] ?: '-') ?></td>
                                            <td><?= htmlspecialchars($supplier['group_name'] ?: '-') ?></td>
                                            <td>
                                                <?php
                                                $statusClass = [
                                                    'active' => 'success',
                                                    'inactive' => 'secondary',
                                                    'suspended' => 'warning'
                                                ];
                                                $statusText = [
                                                    'active' => 'نشط',
                                                    'inactive' => 'غير نشط',
                                                    'suspended' => 'معلق'
                                                ];
                                                ?>
                                                <span class="badge bg-<?= $statusClass[$supplier['G_status']] ?>">
                                                    <?= $statusText[$supplier['G_status']] ?>
                                                </span>
                                            </td>
                                            <td><?= date('Y-m-d', strtotime($supplier['created_at'])) ?></td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('purchases/suppliers/' . $supplier['entity_number']) ?>" 
                                                       class="btn btn-primary btn-sm" title="عرض">
                                                        <i class="mdi mdi-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('purchases/suppliers/' . $supplier['entity_number'] . '/edit') ?>" 
                                                       class="btn btn-success btn-sm" title="تعديل">
                                                        <i class="mdi mdi-pencil"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="text-center mt-3">
                            <a href="<?= base_url('purchases/suppliers') ?>" class="btn btn-light">
                                عرض جميع الموردين
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
