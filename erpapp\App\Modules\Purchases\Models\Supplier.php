<?php
namespace App\Modules\Purchases\Models;

use PDO;
use Exception;

/**
 * Supplier Model - نموذج الموردين
 */
class Supplier
{
    /**
     * Database connection
     */
    protected $db;

    /**
     * Table name
     */
    protected $table = 'concerned_entities';

    /**
     * Section ID for suppliers
     */
    protected $section_id;

    /**
     * Constructor
     */
    public function __construct()
    {
        global $db;
        $this->db = $db;
        
        // الحصول على section_id للموردين
        $stmt = $this->db->prepare("SELECT id FROM sections WHERE code = 'suppliers'");
        $stmt->execute();
        $this->section_id = $stmt->fetchColumn();
    }

    /**
     * الحصول على جميع الموردين للشركة
     */
    public function getByCompany($company_id, $filters = [])
    {
        $sql = "SELECT s.*, g.name_ar as group_name 
                FROM {$this->table} s
                LEFT JOIN entity_groups g ON s.group_id = g.id
                WHERE s.company_id = ? AND s.section_id = ?";
        $params = [$company_id, $this->section_id];

        // تطبيق الفلاتر
        if (isset($filters['status'])) {
            $sql .= " AND s.G_status = ?";
            $params[] = $filters['status'];
        }

        if (!empty($filters['group_id'])) {
            $sql .= " AND s.group_id = ?";
            $params[] = $filters['group_id'];
        }

        if (!empty($filters['search'])) {
            $sql .= " AND (s.G_name_ar LIKE ? OR s.G_name_en LIKE ? OR s.S_company_name LIKE ?)";
            $search = '%' . $filters['search'] . '%';
            $params[] = $search;
            $params[] = $search;
            $params[] = $search;
        }

        // ترتيب النتائج
        if (isset($filters['order_by'])) {
            $sql .= " ORDER BY " . $filters['order_by'];
        } else {
            $sql .= " ORDER BY s.G_name_ar";
        }

        // تحديد عدد النتائج
        if (isset($filters['limit'])) {
            $sql .= " LIMIT " . (int)$filters['limit'];
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على مورد بالرقم الداخلي
     */
    public function getByNumber($entity_number, $company_id)
    {
        $sql = "SELECT s.*, g.name_ar as group_name 
                FROM {$this->table} s
                LEFT JOIN entity_groups g ON s.group_id = g.id
                WHERE s.entity_number = ? AND s.company_id = ? AND s.section_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$entity_number, $company_id, $this->section_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * إنشاء مورد جديد
     */
    public function create($data)
    {
        // الحصول على الرقم التالي
        $nextNumber = $this->getNextNumber($data['company_id']);

        $sql = "INSERT INTO {$this->table} (
                    company_id, section_id, entity_number, group_id,
                    G_name_ar, G_name_en, G_phone, G_mobile, G_website, G_notes, G_status,
                    S_company_name, S_contact_person, S_email, S_tax_number, S_commercial_register,
                    S_payment_terms, S_credit_limit, S_discount_rate, S_delivery_time,
                    S_minimum_order, S_currency, S_rating,
                    S_license_number, S_license_expiry, S_establishment_date, S_legal_form,
                    S_internal_notes, S_special_instructions,
                    created_by, created_at
                ) VALUES (
                    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW()
                )";

        $stmt = $this->db->prepare($sql);
        $result = $stmt->execute([
            $data['company_id'],
            $this->section_id,
            $nextNumber,
            $data['group_id'] ?: null,
            $data['G_name_ar'],
            $data['G_name_en'] ?: null,
            $data['G_phone'] ?: null,
            $data['G_mobile'] ?: null,
            $data['G_website'] ?: null,
            $data['G_notes'] ?: null,
            $data['G_status'] ?: 'active',
            $data['S_company_name'] ?: null,
            $data['S_contact_person'] ?: null,
            $data['S_email'] ?: null,
            $data['S_tax_number'] ?: null,
            $data['S_commercial_register'] ?: null,
            $data['S_payment_terms'] ?: 30,
            $data['S_credit_limit'] ?: 0,
            $data['S_discount_rate'] ?: 0,
            $data['S_delivery_time'] ?: null,
            $data['S_minimum_order'] ?: null,
            $data['S_currency'] ?: 'SAR',
            $data['S_rating'] ?: 'C',
            $data['S_license_number'] ?: null,
            $data['S_license_expiry'] ?: null,
            $data['S_establishment_date'] ?: null,
            $data['S_legal_form'] ?: null,
            $data['S_internal_notes'] ?: null,
            $data['S_special_instructions'] ?: null,
            $data['created_by']
        ]);

        return $result ? $nextNumber : false;
    }

    /**
     * تحديث مورد
     */
    public function update($entity_number, $data, $company_id)
    {
        $sql = "UPDATE {$this->table} SET
                    group_id = ?, G_name_ar = ?, G_name_en = ?, G_phone = ?, G_mobile = ?,
                    G_website = ?, G_notes = ?, G_status = ?,
                    S_company_name = ?, S_contact_person = ?, S_email = ?, S_tax_number = ?,
                    S_commercial_register = ?, S_payment_terms = ?, S_credit_limit = ?,
                    S_discount_rate = ?, S_delivery_time = ?, S_minimum_order = ?,
                    S_currency = ?, S_rating = ?,
                    S_license_number = ?, S_license_expiry = ?, S_establishment_date = ?, S_legal_form = ?,
                    S_internal_notes = ?, S_special_instructions = ?,
                    updated_by = ?, updated_at = NOW()
                WHERE entity_number = ? AND company_id = ? AND section_id = ?";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            $data['group_id'] ?: null,
            $data['G_name_ar'],
            $data['G_name_en'] ?: null,
            $data['G_phone'] ?: null,
            $data['G_mobile'] ?: null,
            $data['G_website'] ?: null,
            $data['G_notes'] ?: null,
            $data['G_status'] ?: 'active',
            $data['S_company_name'] ?: null,
            $data['S_contact_person'] ?: null,
            $data['S_email'] ?: null,
            $data['S_tax_number'] ?: null,
            $data['S_commercial_register'] ?: null,
            $data['S_payment_terms'] ?: 30,
            $data['S_credit_limit'] ?: 0,
            $data['S_discount_rate'] ?: 0,
            $data['S_delivery_time'] ?: null,
            $data['S_minimum_order'] ?: null,
            $data['S_currency'] ?: 'SAR',
            $data['S_rating'] ?: 'C',
            $data['S_license_number'] ?: null,
            $data['S_license_expiry'] ?: null,
            $data['S_establishment_date'] ?: null,
            $data['S_legal_form'] ?: null,
            $data['S_internal_notes'] ?: null,
            $data['S_special_instructions'] ?: null,
            $data['updated_by'],
            $entity_number,
            $company_id,
            $this->section_id
        ]);
    }

    /**
     * حذف مورد
     */
    public function delete($entity_number, $company_id)
    {
        // التحقق من عدم وجود معاملات مع هذا المورد
        // هذا سيتم تطبيقه لاحقاً عند إنشاء جداول المعاملات

        // حذف المورد
        $sql = "DELETE FROM {$this->table} 
                WHERE entity_number = ? AND company_id = ? AND section_id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$entity_number, $company_id, $this->section_id]);
    }

    /**
     * الحصول على الرقم التالي للمورد
     */
    private function getNextNumber($company_id)
    {
        $sql = "SELECT MAX(entity_number) FROM {$this->table} 
                WHERE company_id = ? AND section_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id, $this->section_id]);
        $lastNumber = $stmt->fetchColumn();
        
        return ($lastNumber ?: 0) + 1;
    }

    /**
     * الحصول على عدد الموردين مع الفلاتر
     */
    public function getCountByCompany($company_id, $filters = [])
    {
        $sql = "SELECT COUNT(*) FROM {$this->table} s
                LEFT JOIN entity_groups g ON s.group_id = g.group_number
                    AND g.company_id = s.company_id AND g.section_id = s.section_id
                WHERE s.company_id = ? AND s.section_id = ?";

        $params = [$company_id, $this->section_id];

        // تطبيق الفلاتر
        if (isset($filters['status']) && $filters['status'] !== '') {
            $sql .= " AND s.G_status = ?";
            $params[] = $filters['status'];
        }

        if (isset($filters['group_id']) && $filters['group_id'] !== '') {
            $sql .= " AND s.group_id = ?";
            $params[] = $filters['group_id'];
        }

        if (isset($filters['search']) && $filters['search'] !== '') {
            $sql .= " AND (s.G_name_ar LIKE ? OR s.G_name_en LIKE ? OR s.S_company_name LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return (int)$stmt->fetchColumn();
    }

    /**
     * الحصول على إحصائيات الموردين
     */
    public function getStats($company_id)
    {
        $stats = [];

        // إجمالي الموردين
        $sql = "SELECT COUNT(*) FROM {$this->table}
                WHERE company_id = ? AND section_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id, $this->section_id]);
        $stats['total_suppliers'] = $stmt->fetchColumn();

        // الموردين النشطين
        $sql = "SELECT COUNT(*) FROM {$this->table}
                WHERE company_id = ? AND section_id = ? AND G_status = 'active'";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id, $this->section_id]);
        $stats['active_suppliers'] = $stmt->fetchColumn();

        return $stats;
    }

    /**
     * الحصول على الموردين للاختيار
     */
    public function getForSelect($company_id)
    {
        $sql = "SELECT entity_number, G_name_ar FROM {$this->table}
                WHERE company_id = ? AND section_id = ? AND G_status = 'active'
                ORDER BY G_name_ar";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id, $this->section_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * بدء معاملة قاعدة البيانات
     */
    public function beginTransaction()
    {
        return $this->db->beginTransaction();
    }

    /**
     * تأكيد معاملة قاعدة البيانات
     */
    public function commit()
    {
        return $this->db->commit();
    }

    /**
     * إلغاء معاملة قاعدة البيانات
     */
    public function rollback()
    {
        return $this->db->rollback();
    }

    /**
     * الحصول على اتصال قاعدة البيانات
     */
    public function getDb()
    {
        return $this->db;
    }
}
