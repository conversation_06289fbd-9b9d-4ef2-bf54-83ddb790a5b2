<?php
namespace App\Modules\home\Controllers;

use App\Modules\home\Services\HomeService;

/**
 * متحكم لوحة التحكم
 */
class HomeController
{
    /**
     * معلمات المسار
     */
    protected $params = [];

    /**
     * خدمة لوحة التحكم
     */
    protected $homeService;

    /**
     * Constructor
     *
     * @param array $params معلمات المسار
     */
    public function __construct($params = [])
    {
        $this->params = $params;
        $this->homeService = new HomeService();

        // التحقق من تسجيل الدخول
        if (!is_logged_in()) {
            redirect(base_url('login'));
        }
    }

    /**
     * عرض لوحة التحكم
     *
     * @return void
     */
    public function index()
    {
        // الحصول على بيانات المستخدم الحالي
        $user = current_user();

        // الحصول على إحصائيات لوحة التحكم
        $stats = $this->homeService->getHomeStats($user);

        // الحصول على آخر الأنشطة
        $activities = $this->homeService->getRecentActivities(10);

        // الحصول على الشركات التي يملكها المستخدم
        $companies = $this->homeService->getUserCompanies($user, 5);

        // عرض لوحة التحكم
        view('home::index', [
            'title' => __('لوحة التحكم'),
            'user' => $user,
            'stats' => $stats,
            'activities' => $activities,
            'companies' => $companies,
            'notifications' => [],
            'tasks' => []
        ]);
    }
}
