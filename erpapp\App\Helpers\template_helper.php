<?php
/**
 * مساعد Templates الموحد
 * يعمل مع css_helper.php و loader.php الموجودين
 */

/**
 * تحديد نوع الصفحة الحالية
 *
 * @return string
 */
function get_current_page_type() {
    $url = $_SERVER['REQUEST_URI'];
    
    // إزالة base path من URL
    $base_path = parse_url(APP_URL, PHP_URL_PATH);
    if ($base_path && strpos($url, $base_path) === 0) {
        $url = substr($url, strlen($base_path));
    }
    
    // إزالة query string
    if ($pos = strpos($url, '?')) {
        $url = substr($url, 0, $pos);
    }
    
    $url = trim($url, '/');
    $parts = explode('/', $url);
    
    // تحديد نوع الصفحة
    if (count($parts) >= 2) {
        $last_part = end($parts);
        
        // صفحات خاصة
        if (in_array($last_part, ['create', 'add', 'new'])) {
            return 'form';
        } elseif (in_array($last_part, ['edit', 'update'])) {
            return 'form';
        } elseif (is_numeric($last_part) || preg_match('/^[A-Z0-9]+$/', $last_part)) {
            return 'detail';
        }
    }
    
    // افتراضي: صفحة قائمة/جدول
    return 'datatable';
}

/**
 * تحميل Template حسب نوع الصفحة
 *
 * @param array $data بيانات الصفحة
 * @return void
 */
function load_template($data) {
    $page_type = get_current_page_type();
    $template_path = BASE_PATH . "/App/Templates/{$page_type}/index.php";
    
    if (file_exists($template_path)) {
        // استخراج المتغيرات للـ template
        extract($data);
        
        // تحميل Template
        include $template_path;
    } else {
        throw new Exception("Template not found: {$template_path}");
    }
}

/**
 * تحميل ملفات CSS المطلوبة للـ Template
 *
 * @param string $template_type نوع Template
 * @return array
 */
function get_template_css_files($template_type) {
    $css_files = [];
    
    // ملفات CSS الأساسية للـ Templates
    $template_css_map = [
        'datatable' => [
            'templates/datatable.css',
            'components/filters.css',
            'components/pagination.css'
        ],
        'form' => [
            'templates/form.css',
            'components/tabs.css',
            'components/validation.css'
        ],
        'detail' => [
            'templates/detail.css',
            'components/tabs.css',
            'components/info-cards.css'
        ]
    ];
    
    if (isset($template_css_map[$template_type])) {
        foreach ($template_css_map[$template_type] as $file) {
            if (file_exists(BASE_PATH . "/public/css/{$file}")) {
                $css_files[] = $file;
            }
        }
    }
    
    return $css_files;
}

/**
 * تحميل ملفات JavaScript المطلوبة للـ Template
 *
 * @param string $template_type نوع Template
 * @return array
 */
function get_template_js_files($template_type) {
    $js_files = [];
    
    // ملفات JavaScript الأساسية للـ Templates
    $template_js_map = [
        'datatable' => [
            'templates/datatable.js',
            'components/filters.js',
            'components/pagination.js'
        ],
        'form' => [
            'templates/form.js',
            'components/validation.js',
            'components/tabs.js'
        ],
        'detail' => [
            'templates/detail.js',
            'components/tabs.js'
        ]
    ];
    
    if (isset($template_js_map[$template_type])) {
        foreach ($template_js_map[$template_type] as $file) {
            if (file_exists(BASE_PATH . "/public/js/{$file}")) {
                $js_files[] = $file;
            }
        }
    }
    
    return $js_files;
}

/**
 * طباعة تاجات script لملفات JavaScript الخاصة بالـ Template
 *
 * @param string $template_type نوع Template
 * @return void
 */
function load_template_js_files($template_type) {
    $js_files = get_template_js_files($template_type);
    $base_url = rtrim(APP_URL, '/');
    
    foreach ($js_files as $file) {
        $file_path = BASE_PATH . "/public/js/{$file}";
        
        if (file_exists($file_path)) {
            $version = filemtime($file_path);
            echo "<script src=\"{$base_url}/public/js/{$file}?v={$version}\"></script>\n";
        }
    }
}

/**
 * تحديد الوحدة والكيان من URL
 *
 * @return array
 */
function get_current_module_entity() {
    $url = $_SERVER['REQUEST_URI'];
    
    // إزالة base path من URL
    $base_path = parse_url(APP_URL, PHP_URL_PATH);
    if ($base_path && strpos($url, $base_path) === 0) {
        $url = substr($url, strlen($base_path));
    }
    
    // إزالة query string
    if ($pos = strpos($url, '?')) {
        $url = substr($url, 0, $pos);
    }
    
    $url = trim($url, '/');
    $parts = explode('/', $url);
    
    $module = $parts[0] ?? 'dashboard';
    $entity = $parts[1] ?? null;
    
    return [
        'module' => $module,
        'entity' => $entity,
        'full_path' => $url
    ];
}

/**
 * إنشاء configuration للـ DataTable Template
 *
 * @param array $config إعدادات مخصصة
 * @return array
 */
function create_datatable_config($config = []) {
    $module_entity = get_current_module_entity();
    
    $default_config = [
        'module_name' => $module_entity['module'],
        'entity_name' => $module_entity['entity'],
        'title' => $config['title'] ?? 'قائمة البيانات',
        'columns' => $config['columns'] ?? [],
        'data' => $config['data'] ?? [],
        'filters_config' => $config['filters_config'] ?? [],
        'actions' => $config['actions'] ?? [],
        'pagination' => $config['pagination'] ?? [],
        'filters' => $config['filters'] ?? [],
        'breadcrumb' => $config['breadcrumb'] ?? [],
        'stats' => $config['stats'] ?? [],
        'empty_state' => $config['empty_state'] ?? [
            'icon' => 'mdi mdi-database-outline',
            'message' => 'لا توجد بيانات',
            'action' => null
        ],
        'custom_css' => $config['custom_css'] ?? [],
        'custom_js' => $config['custom_js'] ?? [],
        'custom_script' => $config['custom_script'] ?? ''
    ];
    
    return array_merge($default_config, $config);
}

/**
 * عرض صفحة DataTable باستخدام Template
 *
 * @param array $config إعدادات الصفحة
 * @return void
 */
function render_datatable($config) {
    $template_config = create_datatable_config($config);
    load_template($template_config);
}

/**
 * تحميل Component منفصل
 *
 * @param string $component_name اسم Component
 * @param array $data البيانات
 * @return void
 */
function load_component($component_name, $data = []) {
    $component_path = BASE_PATH . "/App/Templates/Components/{$component_name}.php";
    
    if (file_exists($component_path)) {
        extract($data);
        include $component_path;
    } else {
        echo "<!-- Component not found: {$component_name} -->";
    }
}

/**
 * تحديد ما إذا كانت الصفحة تحتاج تحميل Template CSS
 *
 * @return bool
 */
function should_load_template_css() {
    $page_type = get_current_page_type();
    return in_array($page_type, ['datatable', 'form', 'detail']);
}

/**
 * دمج Template CSS مع النظام الحالي
 * يتم استدعاؤها من css_helper.php
 *
 * @param array $css_files ملفات CSS الحالية
 * @return array
 */
function merge_template_css($css_files) {
    if (should_load_template_css()) {
        $page_type = get_current_page_type();
        $template_css = get_template_css_files($page_type);
        $css_files = array_merge($css_files, $template_css);
    }
    
    return $css_files;
}
