<?php

namespace App\Modules\Inventory\Models;

use App\Core\Model;

class Movement extends Model
{
    protected $table = 'inventory_movements';
    protected $primaryKey = 'movement_id';
    
    protected $fillable = [
        'company_id',
        'module_code',
        'movement_number',
        'product_id',
        'warehouse_id',
        'movement_type',
        'quantity',
        'unit_cost',
        'total_cost',
        'reference_type',
        'reference_id',
        'reference_number',
        'movement_date',
        'notes',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'quantity' => 'decimal:3',
        'unit_cost' => 'decimal:2',
        'total_cost' => 'decimal:2'
    ];

    protected $dates = [
        'movement_date',
        'created_at',
        'updated_at'
    ];

    // العلاقات
    public function company()
    {
        return $this->belongsTo('App\Models\Company', 'company_id', 'CompanyID');
    }

    public function product()
    {
        return $this->belongsTo('App\Modules\Inventory\Models\Product', 'product_id', 'product_id');
    }

    public function warehouse()
    {
        return $this->belongsTo('App\Modules\Inventory\Models\Warehouse', 'warehouse_id', 'warehouse_id');
    }

    public function createdBy()
    {
        return $this->belongsTo('App\Models\User', 'created_by', 'UserID');
    }

    public function updatedBy()
    {
        return $this->belongsTo('App\Models\User', 'updated_by', 'UserID');
    }

    // الدوال المساعدة
    public function isInbound()
    {
        return in_array($this->movement_type, ['purchase', 'transfer_in', 'adjustment_in', 'return_in', 'opening_balance']);
    }

    public function isOutbound()
    {
        return in_array($this->movement_type, ['sale', 'transfer_out', 'adjustment_out', 'return_out', 'damage', 'loss']);
    }

    public function getMovementTypeText()
    {
        $types = [
            'purchase' => 'شراء',
            'sale' => 'بيع',
            'transfer_in' => 'تحويل وارد',
            'transfer_out' => 'تحويل صادر',
            'adjustment_in' => 'تسوية زيادة',
            'adjustment_out' => 'تسوية نقص',
            'return_in' => 'مرتجع وارد',
            'return_out' => 'مرتجع صادر',
            'damage' => 'تالف',
            'loss' => 'فقدان',
            'opening_balance' => 'رصيد افتتاحي'
        ];
        
        return $types[$this->movement_type] ?? $this->movement_type;
    }

    public function getMovementTypeColor()
    {
        if ($this->isInbound()) {
            return 'success';
        } elseif ($this->isOutbound()) {
            return 'danger';
        } else {
            return 'secondary';
        }
    }

    public function getMovementIcon()
    {
        $icons = [
            'purchase' => 'fas fa-shopping-cart',
            'sale' => 'fas fa-cash-register',
            'transfer_in' => 'fas fa-arrow-right',
            'transfer_out' => 'fas fa-arrow-left',
            'adjustment_in' => 'fas fa-plus-circle',
            'adjustment_out' => 'fas fa-minus-circle',
            'return_in' => 'fas fa-undo',
            'return_out' => 'fas fa-redo',
            'damage' => 'fas fa-exclamation-triangle',
            'loss' => 'fas fa-times-circle',
            'opening_balance' => 'fas fa-balance-scale'
        ];
        
        return $icons[$this->movement_type] ?? 'fas fa-exchange-alt';
    }

    public function getSignedQuantity()
    {
        return $this->isInbound() ? $this->quantity : -$this->quantity;
    }

    public function getSignedCost()
    {
        return $this->isInbound() ? $this->total_cost : -$this->total_cost;
    }

    // النطاقات (Scopes)
    public function scopeByCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    public function scopeByWarehouse($query, $warehouseId)
    {
        return $query->where('warehouse_id', $warehouseId);
    }

    public function scopeByProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('movement_type', $type);
    }

    public function scopeInbound($query)
    {
        return $query->whereIn('movement_type', ['purchase', 'transfer_in', 'adjustment_in', 'return_in', 'opening_balance']);
    }

    public function scopeOutbound($query)
    {
        return $query->whereIn('movement_type', ['sale', 'transfer_out', 'adjustment_out', 'return_out', 'damage', 'loss']);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('movement_date', [$startDate, $endDate]);
    }

    public function scopeByReference($query, $referenceType, $referenceId = null)
    {
        $query->where('reference_type', $referenceType);
        
        if ($referenceId) {
            $query->where('reference_id', $referenceId);
        }
        
        return $query;
    }

    public function scopeSearch($query, $search)
    {
        return $query->where(function($q) use ($search) {
            $q->where('movement_number', 'LIKE', "%{$search}%")
              ->orWhere('reference_number', 'LIKE', "%{$search}%")
              ->orWhere('notes', 'LIKE', "%{$search}%")
              ->orWhereHas('product', function($productQuery) use ($search) {
                  $productQuery->where('product_code', 'LIKE', "%{$search}%")
                              ->orWhere('product_name_ar', 'LIKE', "%{$search}%")
                              ->orWhere('product_name_en', 'LIKE', "%{$search}%");
              });
        });
    }

    // التحقق من الصحة
    public static function getValidationRules($id = null)
    {
        return [
            'movement_number' => 'required|string|max:50|unique:inventory_movements,movement_number,' . $id . ',movement_id,company_id,' . session('company_id'),
            'product_id' => 'required|exists:inventory_products,product_id',
            'warehouse_id' => 'required|exists:inventory_warehouses,warehouse_id',
            'movement_type' => 'required|in:purchase,sale,transfer_in,transfer_out,adjustment_in,adjustment_out,return_in,return_out,damage,loss,opening_balance',
            'quantity' => 'required|numeric|min:0.001',
            'unit_cost' => 'nullable|numeric|min:0',
            'movement_date' => 'required|date',
            'reference_type' => 'nullable|string|max:50',
            'reference_id' => 'nullable|integer',
            'reference_number' => 'nullable|string|max:100',
            'notes' => 'nullable|string|max:1000'
        ];
    }

    public static function getValidationMessages()
    {
        return [
            'movement_number.required' => 'رقم الحركة مطلوب',
            'movement_number.unique' => 'رقم الحركة موجود مسبقاً',
            'product_id.required' => 'المنتج مطلوب',
            'product_id.exists' => 'المنتج المحدد غير موجود',
            'warehouse_id.required' => 'المستودع مطلوب',
            'warehouse_id.exists' => 'المستودع المحدد غير موجود',
            'movement_type.required' => 'نوع الحركة مطلوب',
            'movement_type.in' => 'نوع الحركة غير صحيح',
            'quantity.required' => 'الكمية مطلوبة',
            'quantity.numeric' => 'الكمية يجب أن تكون رقماً',
            'quantity.min' => 'الكمية يجب أن تكون أكبر من صفر',
            'unit_cost.numeric' => 'تكلفة الوحدة يجب أن تكون رقماً',
            'movement_date.required' => 'تاريخ الحركة مطلوب',
            'movement_date.date' => 'تاريخ الحركة غير صحيح'
        ];
    }

    // الأحداث
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->company_id = session('company_id');
            $model->module_code = 'inventory';
            $model->created_by = auth()->id();
            
            // حساب التكلفة الإجمالية
            if ($model->unit_cost && $model->quantity) {
                $model->total_cost = $model->unit_cost * $model->quantity;
            }
            
            // توليد رقم الحركة إذا لم يكن موجوداً
            if (!$model->movement_number) {
                $model->movement_number = self::generateMovementNumber();
            }
        });

        static::updating(function ($model) {
            $model->updated_by = auth()->id();
            
            // إعادة حساب التكلفة الإجمالية
            if ($model->unit_cost && $model->quantity) {
                $model->total_cost = $model->unit_cost * $model->quantity;
            }
        });

        static::created(function ($model) {
            // تحديث المخزون بعد إنشاء الحركة
            $model->updateStock();
        });
    }

    // دوال مساعدة
    public static function generateMovementNumber()
    {
        $prefix = 'MOV';
        $date = date('Ymd');
        $companyId = session('company_id');
        
        $lastNumber = self::where('company_id', $companyId)
                         ->where('movement_number', 'LIKE', $prefix . $date . '%')
                         ->orderBy('movement_number', 'desc')
                         ->first();
        
        if ($lastNumber) {
            $lastSequence = intval(substr($lastNumber->movement_number, -4));
            $newSequence = $lastSequence + 1;
        } else {
            $newSequence = 1;
        }
        
        return $prefix . $date . str_pad($newSequence, 4, '0', STR_PAD_LEFT);
    }

    public function updateStock()
    {
        $stock = Stock::where('company_id', $this->company_id)
                     ->where('product_id', $this->product_id)
                     ->where('warehouse_id', $this->warehouse_id)
                     ->first();
        
        if (!$stock) {
            $stock = new Stock([
                'company_id' => $this->company_id,
                'product_id' => $this->product_id,
                'warehouse_id' => $this->warehouse_id,
                'quantity_on_hand' => 0,
                'quantity_reserved' => 0,
                'quantity_available' => 0,
                'average_cost' => 0,
                'last_cost' => 0
            ]);
        }
        
        if ($this->isInbound()) {
            $stock->addStock($this->quantity, $this->unit_cost);
        } elseif ($this->isOutbound()) {
            $stock->removeStock($this->quantity);
        }
    }

    // دوال التقارير
    public static function getMovementSummary($companyId, $startDate = null, $endDate = null, $warehouseId = null)
    {
        $query = self::where('company_id', $companyId);
        
        if ($startDate && $endDate) {
            $query->whereBetween('movement_date', [$startDate, $endDate]);
        }
        
        if ($warehouseId) {
            $query->where('warehouse_id', $warehouseId);
        }
        
        return $query->selectRaw('
            movement_type,
            COUNT(*) as count,
            SUM(quantity) as total_quantity,
            SUM(total_cost) as total_cost
        ')->groupBy('movement_type')->get();
    }
}
