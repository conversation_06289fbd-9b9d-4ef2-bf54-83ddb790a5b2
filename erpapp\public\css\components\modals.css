/* ===== MODALS (بديل Bootstrap) ===== */

/* Modal Backdrop */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 1040;
    opacity: 0;
    transition: opacity var(--transition-normal) ease;
    backdrop-filter: blur(3px);
}

.modal-backdrop.show {
    opacity: 1;
}

/* Modal Container */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1050;
    display: none;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-4);
    overflow-x: hidden;
    overflow-y: auto;
}

.modal.show {
    display: flex;
}

/* Modal Dialog */
.modal-dialog {
    position: relative;
    width: 100%;
    max-width: 600px;
    margin: var(--spacing-4) auto;
    pointer-events: auto;
    transform: scale(0.8) translateY(-50px);
    transition: all var(--transition-normal) ease;
    opacity: 0;
}

.modal.show .modal-dialog {
    transform: scale(1) translateY(0);
    opacity: 1;
}

/* Modal Content */
.modal-content {
    position: relative;
    background: var(--light-card-bg);
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    backdrop-filter: blur(10px);
}

body.dark-theme .modal-content {
    background: var(--dark-card-bg);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.6);
}

/* Modal Header */
.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-5) var(--spacing-6);
    border-bottom: 1px solid var(--light-card-border);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
}

body.dark-theme .modal-header {
    border-bottom-color: var(--dark-card-border);
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
}

.modal-title {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: white;
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.modal-title i {
    font-size: 18px;
}

/* Modal Close Button */
.modal-close {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    font-size: 18px;
    color: white;
    cursor: pointer;
    padding: var(--spacing-2);
    border-radius: var(--border-radius);
    transition: all var(--transition-fast) ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.modal-close:active {
    transform: scale(0.95);
}

/* Modal Body */
.modal-body {
    padding: var(--spacing-5);
    flex: 1;
    overflow-y: auto;
    color: var(--light-text-color);
}

body.dark-theme .modal-body {
    color: var(--dark-text-color);
}

/* Modal Footer */
.modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--spacing-3);
    padding: var(--spacing-5) var(--spacing-6);
    border-top: 1px solid var(--light-card-border);
    background: var(--gray-50);
}

body.dark-theme .modal-footer {
    border-top-color: var(--dark-card-border);
    background: var(--gray-900);
}

/* Modal Sizes */
.modal-sm .modal-dialog {
    max-width: 300px;
}

.modal-lg .modal-dialog {
    max-width: 800px;
}

.modal-xl .modal-dialog {
    max-width: 1140px;
}

.modal-fullscreen .modal-dialog {
    width: 100%;
    max-width: none;
    height: 100%;
    margin: 0;
}

.modal-fullscreen .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
}

/* Filter Modal Specific */
.filter-modal .modal-dialog {
    max-width: 600px;
}

.filter-modal .modal-body {
    padding: var(--spacing-4);
}

.filter-modal .form-group {
    margin-bottom: var(--spacing-4);
}

.filter-modal .form-row {
    display: flex;
    gap: var(--spacing-3);
    margin-bottom: var(--spacing-4);
}

.filter-modal .form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

/* Confirmation Modal */
.confirmation-modal .modal-dialog {
    max-width: 400px;
}

.confirmation-modal .modal-body {
    text-align: center;
    padding: var(--spacing-6);
}

.confirmation-modal .modal-icon {
    font-size: 48px;
    margin-bottom: var(--spacing-4);
}

.confirmation-modal .modal-icon.warning {
    color: var(--warning-color);
}

.confirmation-modal .modal-icon.danger {
    color: var(--danger-color);
}

.confirmation-modal .modal-icon.success {
    color: var(--success-color);
}

.confirmation-modal .modal-icon.info {
    color: var(--info-color);
}

/* Loading Modal */
.loading-modal .modal-dialog {
    max-width: 300px;
}

.loading-modal .modal-body {
    text-align: center;
    padding: var(--spacing-6);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--gray-300);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-4);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Modal Backdrop */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 1040;
    opacity: 0;
    transition: opacity var(--transition-normal) ease;
    backdrop-filter: blur(5px);
}

.modal-backdrop.show {
    opacity: 1;
}

/* Responsive Modals */
@media (max-width: 768px) {
    .modal {
        padding: var(--spacing-2);
    }
    
    .modal-dialog {
        margin: var(--spacing-2) auto;
        max-width: calc(100% - var(--spacing-4));
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: var(--spacing-4);
    }
    
    .modal-lg .modal-dialog,
    .modal-xl .modal-dialog {
        max-width: calc(100% - var(--spacing-4));
    }
    
    .filter-modal .form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .filter-modal .form-row .form-group {
        margin-bottom: var(--spacing-4);
    }
}

/* Animation Classes */
.modal-fade-in {
    animation: modalFadeIn var(--transition-normal) ease;
}

.modal-fade-out {
    animation: modalFadeOut var(--transition-normal) ease;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes modalFadeOut {
    from {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    to {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
}

/* Prevent Body Scroll */
body.modal-open {
    overflow: hidden;
    padding-right: 15px; /* Compensate for scrollbar */
}

/* RTL Support */
[dir="rtl"] .modal-footer {
    justify-content: flex-start;
}

[dir="rtl"] body.modal-open {
    padding-right: 0;
    padding-left: 15px;
}
