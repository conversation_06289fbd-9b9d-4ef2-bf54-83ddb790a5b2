# Application Configuration
APP_NAME="ERP System"
APP_URL=http://localhost
APP_VERSION=1.0.0
APP_ENV=production
APP_DEBUG=false

# Database Configuration
DB_HOST=localhost
DB_NAME=erp_database
DB_USER=root
DB_PASS=

# Email Configuration
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="ERP System"

# Localization
DEFAULT_LANG=ar
DEFAULT_TIMEZONE=Asia/Riyadh
DEFAULT_THEME=light

# Session Configuration
SESSION_LIFETIME=120

# File Upload Configuration
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,zip

# Security Configuration
CSRF_TOKEN_NAME=csrf_token
ENCRYPTION_KEY=your-32-character-secret-key-here
HASH_ALGORITHM=sha256

# Payment Gateway Configuration
TAP_SECRET_KEY=
TAP_PUBLIC_KEY=
TAP_WEBHOOK_SECRET=

# SMS Configuration
SMS_PROVIDER=
SMS_API_KEY=
SMS_SENDER_ID=

# Cache Configuration
CACHE_DRIVER=file
CACHE_TTL=3600

# Log Configuration
LOG_LEVEL=error
LOG_MAX_FILES=30
