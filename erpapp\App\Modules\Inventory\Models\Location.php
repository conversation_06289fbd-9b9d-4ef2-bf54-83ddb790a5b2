<?php

namespace App\Modules\Inventory\Models;

use App\Core\Model;

class Location extends Model
{
    protected $table = 'inventory_locations';
    protected $primaryKey = 'location_id';
    
    protected $fillable = [
        'company_id',
        'module_code',
        'warehouse_id',
        'location_code',
        'location_name_ar',
        'location_name_en',
        'location_type',
        'parent_location_id',
        'capacity',
        'current_usage',
        'is_active',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'capacity' => 'decimal:2',
        'current_usage' => 'decimal:2',
        'is_active' => 'boolean'
    ];

    protected $dates = [
        'created_at',
        'updated_at'
    ];

    // العلاقات
    public function company()
    {
        return $this->belongsTo('App\Models\Company', 'company_id', 'CompanyID');
    }

    public function warehouse()
    {
        return $this->belongsTo('App\Modules\Inventory\Models\Warehouse', 'warehouse_id', 'warehouse_id');
    }

    public function parentLocation()
    {
        return $this->belongsTo('App\Modules\Inventory\Models\Location', 'parent_location_id', 'location_id');
    }

    public function childLocations()
    {
        return $this->hasMany('App\Modules\Inventory\Models\Location', 'parent_location_id', 'location_id');
    }

    public function createdBy()
    {
        return $this->belongsTo('App\Models\User', 'created_by', 'UserID');
    }

    public function updatedBy()
    {
        return $this->belongsTo('App\Models\User', 'updated_by', 'UserID');
    }

    public function stock()
    {
        return $this->hasMany('App\Modules\Inventory\Models\Stock', 'location_id', 'location_id');
    }

    // الدوال المساعدة
    public function getDisplayName()
    {
        return $this->location_name_ar ?: $this->location_name_en;
    }

    public function getFullPath()
    {
        $path = [$this->getDisplayName()];
        $parent = $this->parentLocation;
        
        while ($parent) {
            array_unshift($path, $parent->getDisplayName());
            $parent = $parent->parentLocation;
        }
        
        return implode(' > ', $path);
    }

    public function getUsagePercentage()
    {
        if (!$this->capacity || $this->capacity == 0) {
            return 0;
        }
        return round(($this->current_usage / $this->capacity) * 100, 2);
    }

    public function getAvailableCapacity()
    {
        if (!$this->capacity) {
            return null;
        }
        return $this->capacity - $this->current_usage;
    }

    public function isNearCapacity($threshold = 80)
    {
        return $this->getUsagePercentage() >= $threshold;
    }

    public function isOverCapacity()
    {
        return $this->current_usage > $this->capacity;
    }

    public function getLocationTypeText()
    {
        $types = [
            'zone' => 'منطقة',
            'aisle' => 'ممر',
            'shelf' => 'رف',
            'bin' => 'صندوق'
        ];
        
        return $types[$this->location_type] ?? $this->location_type;
    }

    public function getLocationTypeIcon()
    {
        $icons = [
            'zone' => 'fas fa-map',
            'aisle' => 'fas fa-road',
            'shelf' => 'fas fa-layer-group',
            'bin' => 'fas fa-box'
        ];
        
        return $icons[$this->location_type] ?? 'fas fa-map-marker-alt';
    }

    public function canHaveChildren()
    {
        return in_array($this->location_type, ['zone', 'aisle', 'shelf']);
    }

    public function getAllDescendants()
    {
        $descendants = collect();
        
        foreach ($this->childLocations as $child) {
            $descendants->push($child);
            $descendants = $descendants->merge($child->getAllDescendants());
        }
        
        return $descendants;
    }

    public function getTotalCapacity()
    {
        $total = $this->capacity ?: 0;
        
        foreach ($this->childLocations as $child) {
            $total += $child->getTotalCapacity();
        }
        
        return $total;
    }

    public function getTotalUsage()
    {
        $total = $this->current_usage ?: 0;
        
        foreach ($this->childLocations as $child) {
            $total += $child->getTotalUsage();
        }
        
        return $total;
    }

    // النطاقات (Scopes)
    public function scopeActive($query)
    {
        return $query->where('is_active', 1);
    }

    public function scopeByCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    public function scopeByWarehouse($query, $warehouseId)
    {
        return $query->where('warehouse_id', $warehouseId);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('location_type', $type);
    }

    public function scopeRootLocations($query)
    {
        return $query->whereNull('parent_location_id');
    }

    public function scopeChildrenOf($query, $parentId)
    {
        return $query->where('parent_location_id', $parentId);
    }

    public function scopeSearch($query, $search)
    {
        return $query->where(function($q) use ($search) {
            $q->where('location_code', 'LIKE', "%{$search}%")
              ->orWhere('location_name_ar', 'LIKE', "%{$search}%")
              ->orWhere('location_name_en', 'LIKE', "%{$search}%");
        });
    }

    public function scopeNearCapacity($query, $threshold = 80)
    {
        return $query->whereRaw('(current_usage / capacity) * 100 >= ?', [$threshold]);
    }

    public function scopeOverCapacity($query)
    {
        return $query->whereRaw('current_usage > capacity');
    }

    // التحقق من الصحة
    public static function getValidationRules($id = null)
    {
        return [
            'warehouse_id' => 'required|exists:inventory_warehouses,warehouse_id',
            'location_code' => 'required|string|max:50|unique:inventory_locations,location_code,' . $id . ',location_id,company_id,' . session('company_id') . ',warehouse_id,' . request('warehouse_id'),
            'location_name_ar' => 'required|string|max:100',
            'location_name_en' => 'nullable|string|max:100',
            'location_type' => 'required|in:zone,aisle,shelf,bin',
            'parent_location_id' => 'nullable|exists:inventory_locations,location_id',
            'capacity' => 'nullable|numeric|min:0',
            'is_active' => 'boolean'
        ];
    }

    public static function getValidationMessages()
    {
        return [
            'warehouse_id.required' => 'المستودع مطلوب',
            'warehouse_id.exists' => 'المستودع المحدد غير موجود',
            'location_code.required' => 'رمز الموقع مطلوب',
            'location_code.unique' => 'رمز الموقع موجود مسبقاً في هذا المستودع',
            'location_name_ar.required' => 'اسم الموقع بالعربية مطلوب',
            'location_type.required' => 'نوع الموقع مطلوب',
            'location_type.in' => 'نوع الموقع غير صحيح',
            'parent_location_id.exists' => 'الموقع الأب المحدد غير موجود',
            'capacity.numeric' => 'السعة يجب أن تكون رقماً',
            'capacity.min' => 'السعة يجب أن تكون أكبر من أو تساوي صفر'
        ];
    }

    // الأحداث
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->company_id = session('company_id');
            $model->module_code = 'inventory';
            $model->created_by = auth()->id();
            $model->current_usage = 0.00;
        });

        static::updating(function ($model) {
            $model->updated_by = auth()->id();
        });

        static::deleting(function ($model) {
            // التحقق من وجود مخزون في هذا الموقع
            if ($model->stock()->exists()) {
                throw new \Exception('لا يمكن حذف الموقع لوجود مخزون به');
            }
            
            // التحقق من وجود مواقع فرعية
            if ($model->childLocations()->exists()) {
                throw new \Exception('لا يمكن حذف الموقع لوجود مواقع فرعية');
            }
        });
    }

    // دوال إضافية للإحصائيات
    public function getProductCount()
    {
        return $this->stock()->distinct('product_id')->count();
    }

    public function getTotalStockValue()
    {
        return $this->stock()->sum(\DB::raw('quantity_on_hand * average_cost'));
    }

    public function getStockItems()
    {
        return $this->stock()->with(['product', 'product.category', 'product.unit'])->get();
    }

    // دوال للتنظيم الهرمي
    public static function getLocationTree($warehouseId, $companyId = null)
    {
        $companyId = $companyId ?: session('company_id');
        
        $locations = self::where('company_id', $companyId)
                        ->where('warehouse_id', $warehouseId)
                        ->where('is_active', 1)
                        ->orderBy('location_type')
                        ->orderBy('location_code')
                        ->get();
        
        return self::buildTree($locations);
    }

    private static function buildTree($locations, $parentId = null)
    {
        $tree = [];
        
        foreach ($locations as $location) {
            if ($location->parent_location_id == $parentId) {
                $location->children = self::buildTree($locations, $location->location_id);
                $tree[] = $location;
            }
        }
        
        return $tree;
    }

    public function updateUsage()
    {
        $totalUsage = $this->stock()->sum('quantity_on_hand');
        $this->current_usage = $totalUsage;
        $this->save();
        
        // تحديث الموقع الأب إذا وجد
        if ($this->parentLocation) {
            $this->parentLocation->updateUsage();
        }
    }
}
