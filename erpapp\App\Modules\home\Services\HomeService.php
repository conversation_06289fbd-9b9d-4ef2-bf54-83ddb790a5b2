<?php
namespace App\Modules\home\Services;

/**
 * خدمة لوحة التحكم
 */
class HomeService
{
    /**
     * اتصال قاعدة البيانات
     */
    protected $db;

    /**
     * Constructor
     */
    public function __construct()
    {
        global $db;
        $this->db = $db;
    }

    /**
     * الحصول على إحصائيات لوحة التحكم
     *
     * @param array $user بيانات المستخدم
     * @return array
     */
    public function getHomeStats($user)
    {
        $stats = [
            'companies' => 0,
            'subscriptions' => 0
        ];

        // عدد الشركات التي يملكها المستخدم فقط
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM companies WHERE OwnerID = :user_id");
        $stmt->bindParam(':user_id', $user['UserID']);
        $stmt->execute();
        $result = $stmt->fetch(\PDO::FETCH_ASSOC);
        $stats['companies'] = $result['count'] ?? 0;

        // عدد الاشتراكات للشركات التي يملكها المستخدم
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as count
            FROM subscriptions s
            JOIN companies c ON s.company_id = c.CompanyID
            WHERE c.OwnerID = :user_id
        ");
        $stmt->bindParam(':user_id', $user['UserID']);
        $stmt->execute();
        $result = $stmt->fetch(\PDO::FETCH_ASSOC);
        $stats['subscriptions'] = $result['count'] ?? 0;

        return $stats;
    }

    /**
     * الحصول على آخر الأنشطة
     *
     * @param int $limit عدد الأنشطة
     * @return array
     */
    public function getRecentActivities($limit = 10)
    {
        $activities = [];

        // يمكن تنفيذ هذه الدالة حسب هيكل قاعدة البيانات الخاصة بك

        return $activities;
    }

    /**
     * الحصول على الشركات التي يملكها المستخدم
     *
     * @param array $user بيانات المستخدم
     * @param int $limit عدد الشركات
     * @return array
     */
    public function getUserCompanies($user, $limit = 5)
    {
        $stmt = $this->db->prepare("
            SELECT CompanyID, CompanyName, CompanyNameEN, CompanyEmail, CompanyLogo, CompanyStatus
            FROM companies
            WHERE OwnerID = :user_id
            ORDER BY created_at DESC
            LIMIT :limit
        ");
        $stmt->bindParam(':user_id', $user['UserID']);
        $stmt->bindParam(':limit', $limit, \PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }
}
