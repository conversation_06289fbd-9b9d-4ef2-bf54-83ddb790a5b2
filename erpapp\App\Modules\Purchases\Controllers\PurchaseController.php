<?php
namespace App\Modules\Purchases\Controllers;

use App\Core\Controller;
use App\Modules\Purchases\Models\Supplier;
use App\Modules\Purchases\Models\SupplierGroup;

/**
 * PurchaseController - متحكم لوحة تحكم المشتريات
 */
class PurchaseController extends Controller
{
    protected $supplierModel;
    protected $supplierGroupModel;

    public function __construct()
    {
        parent::__construct();
        $this->supplierModel = new Supplier();
        $this->supplierGroupModel = new SupplierGroup();
    }

    /**
     * لوحة تحكم المشتريات
     */
    public function index()
    {
        $company_id = current_user()['current_company_id'];

        // الحصول على الإحصائيات
        $supplierStats = $this->supplierModel->getStats($company_id);
        $groupStats = $this->supplierGroupModel->getStats($company_id);

        // الحصول على آخر الموردين المضافين
        $recentSuppliers = $this->supplierModel->getByCompany($company_id, ['limit' => 5]);

        // إحصائيات عامة
        $stats = [
            'total_suppliers' => $supplierStats['total_suppliers'],
            'active_suppliers' => $supplierStats['active_suppliers'],
            'total_groups' => $groupStats['total_groups'],
            'inactive_suppliers' => $supplierStats['total_suppliers'] - $supplierStats['active_suppliers']
        ];

        // عرض لوحة التحكم
        $this->view('Purchases::dashboard/index', [
            'stats' => $stats,
            'recentSuppliers' => $recentSuppliers
        ]);
    }

    /**
     * لوحة تحكم المشتريات (نفس index)
     */
    public function dashboard()
    {
        return $this->index();
    }
}
