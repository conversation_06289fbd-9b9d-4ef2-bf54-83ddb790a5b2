<?php
$title = 'إضافة مورد جديد';
$breadcrumb = [
    ['title' => 'الرئيسية', 'url' => '/'],
    ['title' => 'المشتريات', 'url' => '/purchases'],
    ['title' => 'الموردين', 'url' => '/purchases/suppliers'],
    ['title' => 'إضافة مورد', 'url' => '']
];
?>

<div class="container-fluid">
    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <?php foreach ($breadcrumb as $item): ?>
                            <?php if ($item['url']): ?>
                                <li class="breadcrumb-item"><a href="<?= base_url($item['url']) ?>"><?= $item['title'] ?></a></li>
                            <?php else: ?>
                                <li class="breadcrumb-item active"><?= $item['title'] ?></li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </ol>
                </div>
                <h4 class="page-title"><?= $title ?></h4>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">معلومات المورد</h4>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('purchases/suppliers/store') ?>" method="POST">
                        
                        <!-- معلومات أساسية -->
                        <div class="row">
                            <div class="col-12">
                                <h5 class="mb-3 text-uppercase bg-light p-2"><i class="mdi mdi-account-circle me-1"></i> المعلومات الأساسية</h5>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="G_name_ar" class="form-label">اسم المورد بالعربية <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="G_name_ar" name="G_name_ar" 
                                           value="<?= $_POST['G_name_ar'] ?? '' ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="G_name_en" class="form-label">اسم المورد بالإنجليزية</label>
                                    <input type="text" class="form-control" id="G_name_en" name="G_name_en" 
                                           value="<?= $_POST['G_name_en'] ?? '' ?>">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="S_company_name" class="form-label">اسم الشركة</label>
                                    <input type="text" class="form-control" id="S_company_name" name="S_company_name" 
                                           value="<?= $_POST['S_company_name'] ?? '' ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="S_contact_person" class="form-label">الشخص المسؤول</label>
                                    <input type="text" class="form-control" id="S_contact_person" name="S_contact_person" 
                                           value="<?= $_POST['S_contact_person'] ?? '' ?>">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="group_id" class="form-label">المجموعة</label>
                                    <select class="form-select" id="group_id" name="group_id">
                                        <option value="">اختر المجموعة</option>
                                        <?php foreach ($supplierGroups as $group): ?>
                                            <option value="<?= $group['group_number'] ?>" 
                                                    <?= ($_POST['group_id'] ?? '') == $group['group_number'] ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($group['name_ar']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="G_status" class="form-label">الحالة</label>
                                    <select class="form-select" id="G_status" name="G_status">
                                        <option value="active" <?= ($_POST['G_status'] ?? 'active') === 'active' ? 'selected' : '' ?>>نشط</option>
                                        <option value="inactive" <?= ($_POST['G_status'] ?? '') === 'inactive' ? 'selected' : '' ?>>غير نشط</option>
                                        <option value="suspended" <?= ($_POST['G_status'] ?? '') === 'suspended' ? 'selected' : '' ?>>معلق</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات الاتصال -->
                        <div class="row">
                            <div class="col-12">
                                <h5 class="mb-3 text-uppercase bg-light p-2"><i class="mdi mdi-phone me-1"></i> معلومات الاتصال</h5>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="G_phone" class="form-label">الهاتف</label>
                                    <input type="text" class="form-control" id="G_phone" name="G_phone" 
                                           value="<?= $_POST['G_phone'] ?? '' ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="G_mobile" class="form-label">الجوال</label>
                                    <input type="text" class="form-control" id="G_mobile" name="G_mobile" 
                                           value="<?= $_POST['G_mobile'] ?? '' ?>">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="S_email" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="S_email" name="S_email" 
                                           value="<?= $_POST['S_email'] ?? '' ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="G_website" class="form-label">الموقع الإلكتروني</label>
                                    <input type="url" class="form-control" id="G_website" name="G_website" 
                                           value="<?= $_POST['G_website'] ?? '' ?>">
                                </div>
                            </div>
                        </div>

                        <!-- معلومات قانونية -->
                        <div class="row">
                            <div class="col-12">
                                <h5 class="mb-3 text-uppercase bg-light p-2"><i class="mdi mdi-file-document me-1"></i> المعلومات القانونية</h5>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="S_tax_number" class="form-label">الرقم الضريبي</label>
                                    <input type="text" class="form-control" id="S_tax_number" name="S_tax_number" 
                                           value="<?= $_POST['S_tax_number'] ?? '' ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="S_commercial_register" class="form-label">السجل التجاري</label>
                                    <input type="text" class="form-control" id="S_commercial_register" name="S_commercial_register" 
                                           value="<?= $_POST['S_commercial_register'] ?? '' ?>">
                                </div>
                            </div>
                        </div>

                        <!-- شروط التعامل -->
                        <div class="row">
                            <div class="col-12">
                                <h5 class="mb-3 text-uppercase bg-light p-2"><i class="mdi mdi-handshake me-1"></i> شروط التعامل</h5>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="S_payment_terms" class="form-label">شروط الدفع (بالأيام)</label>
                                    <input type="number" class="form-control" id="S_payment_terms" name="S_payment_terms" 
                                           value="<?= $_POST['S_payment_terms'] ?? '30' ?>" min="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="S_credit_limit" class="form-label">الحد الائتماني</label>
                                    <input type="number" class="form-control" id="S_credit_limit" name="S_credit_limit" 
                                           value="<?= $_POST['S_credit_limit'] ?? '0' ?>" min="0" step="0.01">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="S_discount_rate" class="form-label">معدل الخصم (%)</label>
                                    <input type="number" class="form-control" id="S_discount_rate" name="S_discount_rate" 
                                           value="<?= $_POST['S_discount_rate'] ?? '0' ?>" min="0" max="100" step="0.01">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="S_currency" class="form-label">العملة</label>
                                    <select class="form-select" id="S_currency" name="S_currency">
                                        <option value="SAR" <?= ($_POST['S_currency'] ?? 'SAR') === 'SAR' ? 'selected' : '' ?>>ريال سعودي</option>
                                        <option value="USD" <?= ($_POST['S_currency'] ?? '') === 'USD' ? 'selected' : '' ?>>دولار أمريكي</option>
                                        <option value="EUR" <?= ($_POST['S_currency'] ?? '') === 'EUR' ? 'selected' : '' ?>>يورو</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="S_delivery_time" class="form-label">مدة التسليم (بالأيام)</label>
                                    <input type="number" class="form-control" id="S_delivery_time" name="S_delivery_time" 
                                           value="<?= $_POST['S_delivery_time'] ?? '' ?>" min="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="S_rating" class="form-label">التقييم</label>
                                    <select class="form-select" id="S_rating" name="S_rating">
                                        <option value="A" <?= ($_POST['S_rating'] ?? '') === 'A' ? 'selected' : '' ?>>ممتاز (A)</option>
                                        <option value="B" <?= ($_POST['S_rating'] ?? '') === 'B' ? 'selected' : '' ?>>جيد (B)</option>
                                        <option value="C" <?= ($_POST['S_rating'] ?? 'C') === 'C' ? 'selected' : '' ?>>متوسط (C)</option>
                                        <option value="D" <?= ($_POST['S_rating'] ?? '') === 'D' ? 'selected' : '' ?>>ضعيف (D)</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="S_minimum_order" class="form-label">الحد الأدنى للطلب</label>
                                    <input type="number" class="form-control" id="S_minimum_order" name="S_minimum_order" 
                                           value="<?= $_POST['S_minimum_order'] ?? '' ?>" min="0" step="0.01">
                                </div>
                            </div>
                        </div>

                        <!-- ملاحظات -->
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="G_notes" class="form-label">ملاحظات</label>
                                    <textarea class="form-control" id="G_notes" name="G_notes" rows="3"><?= $_POST['G_notes'] ?? '' ?></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="text-end">
                                    <a href="<?= base_url('purchases/suppliers') ?>" class="btn btn-light me-2">إلغاء</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="mdi mdi-content-save me-1"></i> حفظ المورد
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Focus on first input
document.getElementById('G_name_ar').focus();

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const nameAr = document.getElementById('G_name_ar').value.trim();
    
    if (!nameAr) {
        e.preventDefault();
        alert('يرجى إدخال اسم المورد بالعربية');
        document.getElementById('G_name_ar').focus();
        return false;
    }
});
</script>
