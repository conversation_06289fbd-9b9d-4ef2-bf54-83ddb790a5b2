<?php
$title = 'إضافة مورد جديد';
$breadcrumb = [
    ['title' => 'الرئيسية', 'url' => '/'],
    ['title' => 'المشتريات', 'url' => '/purchases'],
    ['title' => 'الموردين', 'url' => '/purchases/suppliers'],
    ['title' => 'إضافة مورد', 'url' => '']
];
?>

<div class="container-fluid">
    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <?php foreach ($breadcrumb as $item): ?>
                            <?php if ($item['url']): ?>
                                <li class="breadcrumb-item"><a href="<?= base_url($item['url']) ?>"><?= $item['title'] ?></a></li>
                            <?php else: ?>
                                <li class="breadcrumb-item active"><?= $item['title'] ?></li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </ol>
                </div>
                <h4 class="page-title"><?= $title ?></h4>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">إضافة مورد جديد</h4>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('purchases/suppliers/store') ?>" method="POST" id="supplierForm">
                        
                        <!-- Navigation Tabs -->
                        <ul class="nav nav-tabs nav-bordered mb-3">
                            <li class="nav-item">
                                <a href="#basic-info" data-bs-toggle="tab" aria-expanded="false" class="nav-link active">
                                    <i class="mdi mdi-account-circle d-md-none d-block"></i>
                                    <span class="d-none d-md-block">المعلومات الأساسية</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#addresses" data-bs-toggle="tab" aria-expanded="true" class="nav-link">
                                    <i class="mdi mdi-map-marker d-md-none d-block"></i>
                                    <span class="d-none d-md-block">العناوين</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#bank-accounts" data-bs-toggle="tab" aria-expanded="false" class="nav-link">
                                    <i class="mdi mdi-bank d-md-none d-block"></i>
                                    <span class="d-none d-md-block">الحسابات البنكية</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#terms-notes" data-bs-toggle="tab" aria-expanded="false" class="nav-link">
                                    <i class="mdi mdi-file-document d-md-none d-block"></i>
                                    <span class="d-none d-md-block">شروط التعامل</span>
                                </a>
                            </li>
                        </ul>

                        <div class="tab-content">
                            
                            <!-- التبويب الأول: المعلومات الأساسية -->
                            <div class="tab-pane show active" id="basic-info">
                                
                                <!-- معلومات أساسية -->
                                <div class="row">
                                    <div class="col-12">
                                        <h5 class="mb-3 text-uppercase bg-light p-2">
                                            <i class="mdi mdi-account-circle me-1"></i> المعلومات الأساسية
                                        </h5>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="G_name_ar" class="form-label">اسم المورد بالعربية <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="G_name_ar" name="G_name_ar" 
                                                   value="<?= $_POST['G_name_ar'] ?? '' ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="G_name_en" class="form-label">اسم المورد بالإنجليزية</label>
                                            <input type="text" class="form-control" id="G_name_en" name="G_name_en" 
                                                   value="<?= $_POST['G_name_en'] ?? '' ?>">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="S_company_name" class="form-label">اسم الشركة</label>
                                            <input type="text" class="form-control" id="S_company_name" name="S_company_name" 
                                                   value="<?= $_POST['S_company_name'] ?? '' ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="S_contact_person" class="form-label">الشخص المسؤول</label>
                                            <input type="text" class="form-control" id="S_contact_person" name="S_contact_person" 
                                                   value="<?= $_POST['S_contact_person'] ?? '' ?>">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="group_id" class="form-label">المجموعة</label>
                                            <select class="form-select" id="group_id" name="group_id">
                                                <option value="">اختر المجموعة</option>
                                                <?php foreach ($supplierGroups as $group): ?>
                                                    <option value="<?= $group['group_number'] ?>" 
                                                            <?= ($_POST['group_id'] ?? '') == $group['group_number'] ? 'selected' : '' ?>>
                                                        <?= htmlspecialchars($group['name_ar']) ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="G_status" class="form-label">الحالة</label>
                                            <select class="form-select" id="G_status" name="G_status">
                                                <option value="active" <?= ($_POST['G_status'] ?? 'active') === 'active' ? 'selected' : '' ?>>نشط</option>
                                                <option value="inactive" <?= ($_POST['G_status'] ?? '') === 'inactive' ? 'selected' : '' ?>>غير نشط</option>
                                                <option value="suspended" <?= ($_POST['G_status'] ?? '') === 'suspended' ? 'selected' : '' ?>>معلق</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- معلومات الاتصال -->
                                <div class="row">
                                    <div class="col-12">
                                        <h5 class="mb-3 text-uppercase bg-light p-2">
                                            <i class="mdi mdi-phone me-1"></i> معلومات الاتصال
                                        </h5>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="G_phone" class="form-label">الهاتف</label>
                                            <input type="text" class="form-control" id="G_phone" name="G_phone" 
                                                   value="<?= $_POST['G_phone'] ?? '' ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="G_mobile" class="form-label">الجوال</label>
                                            <input type="text" class="form-control" id="G_mobile" name="G_mobile" 
                                                   value="<?= $_POST['G_mobile'] ?? '' ?>">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="S_email" class="form-label">البريد الإلكتروني</label>
                                            <input type="email" class="form-control" id="S_email" name="S_email" 
                                                   value="<?= $_POST['S_email'] ?? '' ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="G_website" class="form-label">الموقع الإلكتروني</label>
                                            <input type="url" class="form-control" id="G_website" name="G_website" 
                                                   value="<?= $_POST['G_website'] ?? '' ?>">
                                        </div>
                                    </div>
                                </div>

                                <!-- معلومات قانونية -->
                                <div class="row">
                                    <div class="col-12">
                                        <h5 class="mb-3 text-uppercase bg-light p-2">
                                            <i class="mdi mdi-file-document me-1"></i> المعلومات القانونية
                                        </h5>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="S_tax_number" class="form-label">الرقم الضريبي</label>
                                            <input type="text" class="form-control" id="S_tax_number" name="S_tax_number" 
                                                   value="<?= $_POST['S_tax_number'] ?? '' ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="S_commercial_register" class="form-label">السجل التجاري</label>
                                            <input type="text" class="form-control" id="S_commercial_register" name="S_commercial_register" 
                                                   value="<?= $_POST['S_commercial_register'] ?? '' ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- التبويب الثاني: العناوين -->
                            <div class="tab-pane" id="addresses">
                                <div class="row">
                                    <div class="col-12">
                                        <h5 class="mb-3 text-uppercase bg-light p-2">
                                            <i class="mdi mdi-map-marker me-1"></i> عناوين المورد
                                        </h5>
                                        <p class="text-muted">يمكنك إضافة عدة عناوين للمورد (مكتب رئيسي، فرع، مستودع، إلخ)</p>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-12">
                                        <button type="button" class="btn btn-primary btn-sm" onclick="addAddressRow()">
                                            <i class="mdi mdi-plus me-1"></i> إضافة عنوان جديد
                                        </button>
                                    </div>
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-bordered" id="addresses-table">
                                        <thead class="table-light">
                                            <tr>
                                                <th style="width: 12%;">نوع العنوان</th>
                                                <th style="width: 12%;">اسم العنوان</th>
                                                <th style="width: 20%;">العنوان التفصيلي</th>
                                                <th style="width: 10%;">المدينة</th>
                                                <th style="width: 10%;">المنطقة</th>
                                                <th style="width: 8%;">الرمز البريدي</th>
                                                <th style="width: 10%;">الدولة</th>
                                                <th style="width: 10%;">الهاتف</th>
                                                <th style="width: 5%;">افتراضي</th>
                                                <th style="width: 3%;">حذف</th>
                                            </tr>
                                        </thead>
                                        <tbody id="addresses-tbody">
                                            <!-- سيتم إضافة الصفوف هنا بواسطة JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- التبويب الثالث: الحسابات البنكية -->
                            <div class="tab-pane" id="bank-accounts">
                                <div class="row">
                                    <div class="col-12">
                                        <h5 class="mb-3 text-uppercase bg-light p-2">
                                            <i class="mdi mdi-bank me-1"></i> الحسابات البنكية للمورد
                                        </h5>
                                        <p class="text-muted">يمكنك إضافة عدة حسابات بنكية للمورد بعملات مختلفة</p>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-12">
                                        <button type="button" class="btn btn-primary btn-sm" onclick="addBankAccountRow()">
                                            <i class="mdi mdi-plus me-1"></i> إضافة حساب بنكي جديد
                                        </button>
                                    </div>
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-bordered" id="bank-accounts-table">
                                        <thead class="table-light">
                                            <tr>
                                                <th style="width: 20%;">اسم البنك</th>
                                                <th style="width: 18%;">رقم الحساب</th>
                                                <th style="width: 18%;">اسم صاحب الحساب</th>
                                                <th style="width: 20%;">رقم الآيبان (IBAN)</th>
                                                <th style="width: 10%;">العملة</th>
                                                <th style="width: 8%;">افتراضي</th>
                                                <th style="width: 6%;">حذف</th>
                                            </tr>
                                        </thead>
                                        <tbody id="bank-accounts-tbody">
                                            <!-- سيتم إضافة الصفوف هنا بواسطة JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- التبويب الرابع: شروط التعامل -->
                            <div class="tab-pane" id="terms-notes">
                                <div class="row">
                                    <div class="col-12">
                                        <h5 class="mb-3 text-uppercase bg-light p-2">
                                            <i class="mdi mdi-handshake me-1"></i> شروط التعامل
                                        </h5>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="S_payment_terms" class="form-label">شروط الدفع (بالأيام)</label>
                                            <input type="number" class="form-control" id="S_payment_terms" name="S_payment_terms"
                                                   value="<?= $_POST['S_payment_terms'] ?? '30' ?>" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="S_credit_limit" class="form-label">الحد الائتماني</label>
                                            <input type="number" class="form-control" id="S_credit_limit" name="S_credit_limit"
                                                   value="<?= $_POST['S_credit_limit'] ?? '0' ?>" min="0" step="0.01">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="S_discount_rate" class="form-label">معدل الخصم (%)</label>
                                            <input type="number" class="form-control" id="S_discount_rate" name="S_discount_rate"
                                                   value="<?= $_POST['S_discount_rate'] ?? '0' ?>" min="0" max="100" step="0.01">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="S_currency" class="form-label">العملة الافتراضية</label>
                                            <select class="form-select" id="S_currency" name="S_currency">
                                                <option value="SAR" <?= ($_POST['S_currency'] ?? 'SAR') === 'SAR' ? 'selected' : '' ?>>ريال سعودي</option>
                                                <option value="USD" <?= ($_POST['S_currency'] ?? '') === 'USD' ? 'selected' : '' ?>>دولار أمريكي</option>
                                                <option value="EUR" <?= ($_POST['S_currency'] ?? '') === 'EUR' ? 'selected' : '' ?>>يورو</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="S_delivery_time" class="form-label">مدة التسليم (بالأيام)</label>
                                            <input type="number" class="form-control" id="S_delivery_time" name="S_delivery_time"
                                                   value="<?= $_POST['S_delivery_time'] ?? '' ?>" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="S_rating" class="form-label">تقييم المورد</label>
                                            <select class="form-select" id="S_rating" name="S_rating">
                                                <option value="A" <?= ($_POST['S_rating'] ?? '') === 'A' ? 'selected' : '' ?>>ممتاز (A)</option>
                                                <option value="B" <?= ($_POST['S_rating'] ?? '') === 'B' ? 'selected' : '' ?>>جيد (B)</option>
                                                <option value="C" <?= ($_POST['S_rating'] ?? 'C') === 'C' ? 'selected' : '' ?>>متوسط (C)</option>
                                                <option value="D" <?= ($_POST['S_rating'] ?? '') === 'D' ? 'selected' : '' ?>>ضعيف (D)</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label for="S_minimum_order" class="form-label">الحد الأدنى للطلب</label>
                                            <input type="number" class="form-control" id="S_minimum_order" name="S_minimum_order"
                                                   value="<?= $_POST['S_minimum_order'] ?? '' ?>" min="0" step="0.01">
                                        </div>
                                    </div>
                                </div>

                                <!-- ملاحظات -->
                                <div class="row">
                                    <div class="col-12">
                                        <h5 class="mb-3 text-uppercase bg-light p-2">
                                            <i class="mdi mdi-note-text me-1"></i> ملاحظات
                                        </h5>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-12">
                                        <div class="mb-3">
                                            <label for="G_notes" class="form-label">ملاحظات عامة</label>
                                            <textarea class="form-control" id="G_notes" name="G_notes" rows="4"
                                                      placeholder="أي ملاحظات أو تعليقات خاصة بالمورد..."><?= $_POST['G_notes'] ?? '' ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="text-end">
                                    <a href="<?= base_url('purchases/suppliers') ?>" class="btn btn-light me-2">إلغاء</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="mdi mdi-content-save me-1"></i> حفظ المورد
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let addressIndex = 0;
let bankAccountIndex = 0;

// إضافة صف عنوان جديد
function addAddressRow() {
    const tbody = document.getElementById('addresses-tbody');
    const row = document.createElement('tr');
    row.innerHTML = `
        <td>
            <select class="form-select form-select-sm" name="addresses[${addressIndex}][address_type]" required>
                <option value="">اختر النوع</option>
                <option value="main">العنوان الرئيسي</option>
                <option value="billing">عنوان الفواتير</option>
                <option value="shipping">عنوان الشحن</option>
                <option value="warehouse">عنوان المستودع</option>
                <option value="office">عنوان المكتب</option>
            </select>
        </td>
        <td>
            <input type="text" class="form-control form-control-sm" name="addresses[${addressIndex}][address_label]"
                   placeholder="اسم العنوان" required>
        </td>
        <td>
            <textarea class="form-control form-control-sm" name="addresses[${addressIndex}][address_line1]"
                      rows="2" placeholder="العنوان التفصيلي" required></textarea>
        </td>
        <td>
            <input type="text" class="form-control form-control-sm" name="addresses[${addressIndex}][city]"
                   placeholder="المدينة" required>
        </td>
        <td>
            <input type="text" class="form-control form-control-sm" name="addresses[${addressIndex}][state_province]"
                   placeholder="المنطقة">
        </td>
        <td>
            <input type="text" class="form-control form-control-sm" name="addresses[${addressIndex}][postal_code]"
                   placeholder="الرمز البريدي">
        </td>
        <td>
            <select class="form-select form-select-sm" name="addresses[${addressIndex}][country]">
                <option value="Saudi Arabia" selected>السعودية</option>
                <option value="United Arab Emirates">الإمارات</option>
                <option value="Kuwait">الكويت</option>
                <option value="Qatar">قطر</option>
                <option value="Bahrain">البحرين</option>
                <option value="Oman">عمان</option>
                <option value="Jordan">الأردن</option>
                <option value="Egypt">مصر</option>
            </select>
        </td>
        <td>
            <input type="text" class="form-control form-control-sm" name="addresses[${addressIndex}][phone]"
                   placeholder="هاتف العنوان">
        </td>
        <td class="text-center">
            <div class="form-check">
                <input class="form-check-input" type="radio" name="default_address" value="${addressIndex}"
                       ${addressIndex === 0 ? 'checked' : ''}>
            </div>
        </td>
        <td class="text-center">
            <button type="button" class="btn btn-danger btn-sm" onclick="removeAddressRow(this)"
                    ${addressIndex === 0 ? 'style="display:none"' : ''}>
                <i class="mdi mdi-delete"></i>
            </button>
        </td>
    `;
    tbody.appendChild(row);
    addressIndex++;
}

// حذف صف عنوان
function removeAddressRow(button) {
    const row = button.closest('tr');
    row.remove();
}

// إضافة صف حساب بنكي جديد
function addBankAccountRow() {
    const tbody = document.getElementById('bank-accounts-tbody');
    const row = document.createElement('tr');
    row.innerHTML = `
        <td>
            <input type="text" class="form-control form-control-sm" name="bank_accounts[${bankAccountIndex}][bank_name]"
                   placeholder="اسم البنك" required>
        </td>
        <td>
            <input type="text" class="form-control form-control-sm" name="bank_accounts[${bankAccountIndex}][account_number]"
                   placeholder="رقم الحساب" required>
        </td>
        <td>
            <input type="text" class="form-control form-control-sm" name="bank_accounts[${bankAccountIndex}][account_name]"
                   placeholder="اسم صاحب الحساب" required>
        </td>
        <td>
            <input type="text" class="form-control form-control-sm" name="bank_accounts[${bankAccountIndex}][iban]"
                   placeholder="************************">
        </td>
        <td>
            <select class="form-select form-select-sm" name="bank_accounts[${bankAccountIndex}][currency]">
                <option value="SAR" selected>SAR</option>
                <option value="USD">USD</option>
                <option value="EUR">EUR</option>
                <option value="AED">AED</option>
            </select>
        </td>
        <td class="text-center">
            <div class="form-check">
                <input class="form-check-input" type="radio" name="default_bank_account" value="${bankAccountIndex}"
                       ${bankAccountIndex === 0 ? 'checked' : ''}>
            </div>
        </td>
        <td class="text-center">
            <button type="button" class="btn btn-danger btn-sm" onclick="removeBankAccountRow(this)"
                    ${bankAccountIndex === 0 ? 'style="display:none"' : ''}>
                <i class="mdi mdi-delete"></i>
            </button>
        </td>
    `;
    tbody.appendChild(row);
    bankAccountIndex++;
}

// حذف صف حساب بنكي
function removeBankAccountRow(button) {
    const row = button.closest('tr');
    row.remove();
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إضافة صف عنوان افتراضي
    addAddressRow();

    // إضافة صف حساب بنكي افتراضي
    addBankAccountRow();

    // التحقق من وجود hash في URL وتفعيل التبويب المناسب
    if (window.location.hash) {
        const hashTab = window.location.hash.substring(1);
        const tabExists = document.querySelector(`#${hashTab}`);
        if (tabExists) {
            // استخدام Bootstrap لتفعيل التبويب
            const tabTrigger = document.querySelector(`a[href="#${hashTab}"]`);
            if (tabTrigger) {
                const tab = new bootstrap.Tab(tabTrigger);
                tab.show();
            }
        }
    }

    // إضافة مستمع لحفظ التبويب النشط في URL
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', function(e) {
            const targetTab = this.getAttribute('href');
            // تحديث URL بدون إعادة تحميل الصفحة
            history.replaceState(null, null, targetTab);
        });
    });

    // Focus on first input (فقط إذا لم يكن هناك hash أو كان التبويب الأول نشط)
    if (!window.location.hash || window.location.hash === '#basic-info') {
        document.getElementById('G_name_ar').focus();
    }
});

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const nameAr = document.getElementById('G_name_ar').value.trim();

    if (!nameAr) {
        e.preventDefault();
        alert('يرجى إدخال اسم المورد بالعربية');
        // الانتقال للتبويب الأول
        window.location.hash = '#basic-info';
        document.getElementById('G_name_ar').focus();
        return false;
    }

    // التحقق من وجود عنوان واحد على الأقل
    const addressRows = document.querySelectorAll('#addresses-tbody tr');
    if (addressRows.length === 0) {
        e.preventDefault();
        alert('يرجى إضافة عنوان واحد على الأقل');
        // الانتقال لتبويب العناوين
        window.location.hash = '#addresses';
        return false;
    }

    // التحقق من وجود حساب بنكي واحد على الأقل
    const bankAccountRows = document.querySelectorAll('#bank-accounts-tbody tr');
    if (bankAccountRows.length === 0) {
        e.preventDefault();
        alert('يرجى إضافة حساب بنكي واحد على الأقل');
        // الانتقال لتبويب الحسابات البنكية
        window.location.hash = '#bank-accounts';
        return false;
    }
});
</script>
