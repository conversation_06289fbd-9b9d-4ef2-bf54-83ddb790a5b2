<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="page-title">
                    <i class="fas fa-plus-circle"></i> <?= __('إضافة مستودع جديد') ?>
                </h1>
                <div>
                    <a href="<?= base_url('inventory/warehouses') ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> <?= __('العودة للمستودعات') ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php
    // عرض رسائل الخطأ
    $error = flash('warehouse_error');
    if ($error) {
        echo '<script>
            document.addEventListener("DOMContentLoaded", function() {
                if (typeof toastr !== "undefined") {
                    toastr.error("' . addslashes($error['message']) . '", "' . __('خطأ') . '");
                }
            });
        </script>';
    }
    ?>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-warehouse me-2"></i> <?= __('بيانات المستودع الجديد') ?>
                    </h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('inventory/warehouses/store') ?>" method="POST" id="warehouseForm">
                        <div class="row">
                            <!-- كود المستودع -->
                            <div class="col-md-6 mb-3">
                                <label for="warehouse_code" class="form-label required">
                                    <i class="fas fa-barcode me-1"></i> <?= __('كود المستودع') ?>
                                </label>
                                <input type="text" class="form-control" id="warehouse_code" name="warehouse_code"
                                       value="<?= $_POST['warehouse_code'] ?? '' ?>" required maxlength="50">
                                <div class="form-text"><?= __('كود فريد للمستودع') ?></div>
                            </div>

                            <!-- نوع المستودع -->
                            <div class="col-md-6 mb-3">
                                <label for="warehouse_type" class="form-label required">
                                    <i class="fas fa-tags me-1"></i> <?= __('نوع المستودع') ?>
                                </label>
                                <select class="form-select" id="warehouse_type" name="warehouse_type" required>
                                    <option value=""><?= __('اختر نوع المستودع') ?></option>
                                    <option value="main" <?= ($_POST['warehouse_type'] ?? '') == 'main' ? 'selected' : '' ?>>
                                        <?= __('مستودع رئيسي') ?>
                                    </option>
                                    <option value="branch" <?= ($_POST['warehouse_type'] ?? '') == 'branch' ? 'selected' : '' ?>>
                                        <?= __('مستودع فرع') ?>
                                    </option>
                                    <option value="virtual" <?= ($_POST['warehouse_type'] ?? '') == 'virtual' ? 'selected' : '' ?>>
                                        <?= __('مستودع افتراضي') ?>
                                    </option>
                                    <option value="external" <?= ($_POST['warehouse_type'] ?? '') == 'external' ? 'selected' : '' ?>>
                                        <?= __('مستودع خارجي') ?>
                                    </option>
                                    <option value="temporary" <?= ($_POST['warehouse_type'] ?? '') == 'temporary' ? 'selected' : '' ?>>
                                        <?= __('مستودع مؤقت') ?>
                                    </option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <!-- اسم المستودع بالعربية -->
                            <div class="col-md-6 mb-3">
                                <label for="warehouse_name_ar" class="form-label required">
                                    <i class="fas fa-font me-1"></i> <?= __('اسم المستودع (عربي)') ?>
                                </label>
                                <input type="text" class="form-control" id="warehouse_name_ar" name="warehouse_name_ar"
                                       value="<?= $_POST['warehouse_name_ar'] ?? '' ?>" required maxlength="100">
                            </div>

                            <!-- اسم المستودع بالإنجليزية -->
                            <div class="col-md-6 mb-3">
                                <label for="warehouse_name_en" class="form-label">
                                    <i class="fas fa-font me-1"></i> <?= __('اسم المستودع (إنجليزي)') ?>
                                </label>
                                <input type="text" class="form-control" id="warehouse_name_en" name="warehouse_name_en"
                                       value="<?= $_POST['warehouse_name_en'] ?? '' ?>" maxlength="100">
                                <div class="form-text"><?= __('اختياري') ?></div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- العنوان -->
                            <div class="col-12 mb-3">
                                <label for="address" class="form-label">
                                    <i class="fas fa-map-marker-alt me-1"></i> <?= __('العنوان') ?>
                                </label>
                                <textarea class="form-control" id="address" name="address" rows="3"
                                          maxlength="500"><?= $_POST['address'] ?? '' ?></textarea>
                                <div class="form-text"><?= __('عنوان المستودع الفعلي') ?></div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- رقم الهاتف -->
                            <div class="col-md-4 mb-3">
                                <label for="phone" class="form-label">
                                    <i class="fas fa-phone me-1"></i> <?= __('رقم الهاتف') ?>
                                </label>
                                <input type="tel" class="form-control" id="phone" name="phone"
                                       value="<?= $_POST['phone'] ?? '' ?>" maxlength="20">
                            </div>

                            <!-- البريد الإلكتروني -->
                            <div class="col-md-4 mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i> <?= __('البريد الإلكتروني') ?>
                                </label>
                                <input type="email" class="form-control" id="email" name="email"
                                       value="<?= $_POST['email'] ?? '' ?>" maxlength="100">
                            </div>

                            <!-- اسم المدير -->
                            <div class="col-md-4 mb-3">
                                <label for="manager_name" class="form-label">
                                    <i class="fas fa-user-tie me-1"></i> <?= __('اسم المدير') ?>
                                </label>
                                <input type="text" class="form-control" id="manager_name" name="manager_name"
                                       value="<?= $_POST['manager_name'] ?? '' ?>" maxlength="100">
                            </div>
                        </div>

                        <div class="row">
                            <!-- السعة -->
                            <div class="col-md-6 mb-3">
                                <label for="capacity" class="form-label">
                                    <i class="fas fa-weight me-1"></i> <?= __('السعة القصوى') ?>
                                </label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="capacity" name="capacity"
                                           value="<?= $_POST['capacity'] ?? '' ?>" min="0" step="0.01">
                                    <span class="input-group-text"><?= __('وحدة') ?></span>
                                </div>
                                <div class="form-text"><?= __('السعة القصوى للمستودع (اختياري)') ?></div>
                            </div>

                            <!-- الحالة -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label"><?= __('الحالة') ?></label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                           value="1" <?= ($_POST['is_active'] ?? '1') ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="is_active">
                                        <i class="fas fa-toggle-on me-1"></i> <?= __('مستودع نشط') ?>
                                    </label>
                                </div>
                                <div class="form-text"><?= __('يمكن استخدام المستودع في العمليات') ?></div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <!-- أزرار الحفظ والإلغاء -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="submit" class="btn btn-primary btn-lg">
                                            <i class="fas fa-save me-2"></i> <?= __('حفظ المستودع') ?>
                                        </button>
                                        <button type="reset" class="btn btn-outline-secondary btn-lg ms-2">
                                            <i class="fas fa-undo me-2"></i> <?= __('إعادة تعيين') ?>
                                        </button>
                                    </div>
                                    <div>
                                        <a href="<?= base_url('inventory/warehouses') ?>" class="btn btn-outline-danger btn-lg">
                                            <i class="fas fa-times me-2"></i> <?= __('إلغاء') ?>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تولید کود تلقائي للمستودع
    const warehouseCodeInput = document.getElementById('warehouse_code');
    const warehouseTypeSelect = document.getElementById('warehouse_type');

    if (warehouseCodeInput.value === '') {
        generateWarehouseCode();
    }

    warehouseTypeSelect.addEventListener('change', function() {
        if (warehouseCodeInput.value === '' || confirm('<?= __('هل تريد إنشاء كود جديد بناءً على النوع المحدد؟') ?>')) {
            generateWarehouseCode();
        }
    });

    function generateWarehouseCode() {
        const type = warehouseTypeSelect.value;
        if (type) {
            const typePrefix = {
                'main': 'MAIN',
                'branch': 'BR',
                'virtual': 'VR',
                'external': 'EXT',
                'temporary': 'TMP'
            };

            const prefix = typePrefix[type] || 'WH';
            const timestamp = Date.now().toString().slice(-6);
            warehouseCodeInput.value = prefix + '-' + timestamp;
        }
    }

    // التحقق من صحة النموذج
    const form = document.getElementById('warehouseForm');
    form.addEventListener('submit', function(e) {
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(function(field) {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });

        if (!isValid) {
            e.preventDefault();
            if (typeof toastr !== 'undefined') {
                toastr.error('<?= __('يرجى ملء جميع الحقول المطلوبة') ?>', '<?= __('خطأ في النموذج') ?>');
            }
        }
    });

    // إزالة رسائل الخطأ عند الكتابة
    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach(function(input) {
        input.addEventListener('input', function() {
            this.classList.remove('is-invalid');
        });
    });
});
</script>

<style>
.required::after {
    content: " *";
    color: #dc3545;
    font-weight: bold;
}

.form-check-input:checked {
    background-color: #198754;
    border-color: #198754;
}

.is-invalid {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.form-control:focus, .form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}
</style>