<?php
/**
 * صفحة تعديل المورد
 */

// التحقق من وجود بيانات المورد
if (!isset($supplier) || empty($supplier)) {
    redirect(base_url('purchases/suppliers'));
    exit;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <title>تعديل المورد - <?= htmlspecialchars($supplier['G_name_ar']) ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="تعديل بيانات المورد" name="description">
    <meta content="ERP System" name="author">
    
    <!-- App favicon -->
    <link rel="shortcut icon" href="<?= base_url('public/images/favicon.ico') ?>">
    
    <!-- App css -->
    <link href="<?= base_url('public/css/icons.min.css') ?>" rel="stylesheet" type="text/css">
    <link href="<?= base_url('public/css/app.min.css') ?>" rel="stylesheet" type="text/css" id="app-style">
</head>

<body class="loading" data-layout-color="light" data-leftbar-theme="dark" data-layout-mode="fluid" data-rightbar-onstart="true">

    <!-- Begin page -->
    <div class="wrapper">
        
        <!-- ========== Left Sidebar Start ========== -->
        <?php include_once 'App/Views/partials/sidebar.php'; ?>
        <!-- Left Sidebar End -->

        <!-- ============================================================== -->
        <!-- Start Page Content here -->
        <!-- ============================================================== -->

        <div class="content-page">
            <div class="content">
                
                <!-- Topbar Start -->
                <?php include_once 'App/Views/partials/topbar.php'; ?>
                <!-- end Topbar -->

                <!-- Start Content-->
                <div class="container-fluid">
                    
                    <!-- start page title -->
                    <div class="row">
                        <div class="col-12">
                            <div class="page-title-box">
                                <div class="page-title-right">
                                    <ol class="breadcrumb m-0">
                                        <?php foreach ($breadcrumb as $item): ?>
                                            <?php if (isset($item['active']) && $item['active']): ?>
                                                <li class="breadcrumb-item active"><?= $item['title'] ?></li>
                                            <?php else: ?>
                                                <li class="breadcrumb-item"><a href="<?= $item['url'] ?>"><?= $item['title'] ?></a></li>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </ol>
                                </div>
                                <h4 class="page-title"><?= $title ?></h4>
                            </div>
                        </div>
                    </div>
                    <!-- end page title -->

                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <div class="row align-items-center">
                                        <div class="col">
                                            <h4 class="card-title mb-0">
                                                <i class="mdi mdi-pencil me-2"></i>
                                                تعديل بيانات المورد: <?= htmlspecialchars($supplier['G_name_ar']) ?>
                                            </h4>
                                        </div>
                                        <div class="col-auto">
                                            <div class="btn-group">
                                                <button type="submit" form="supplierForm" class="btn btn-success">
                                                    <i class="mdi mdi-content-save me-1"></i> حفظ التعديلات
                                                </button>
                                                <a href="<?= base_url('purchases/suppliers') ?>" class="btn btn-secondary">
                                                    <i class="mdi mdi-arrow-left me-1"></i> إلغاء
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <form action="<?= base_url('purchases/suppliers/' . $supplier['entity_number'] . '/update') ?>" method="POST" id="supplierForm">
                                        
                                        <!-- Navigation Tabs -->
                                        <ul class="nav nav-tabs nav-bordered mb-3">
                                            <li class="nav-item">
                                                <a href="#basic-info" data-bs-toggle="tab" aria-expanded="false" class="nav-link active">
                                                    <i class="mdi mdi-account-circle d-md-none d-block"></i>
                                                    <span class="d-none d-md-block">المعلومات الأساسية</span>
                                                </a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="#contact-info" data-bs-toggle="tab" aria-expanded="false" class="nav-link">
                                                    <i class="mdi mdi-phone d-md-none d-block"></i>
                                                    <span class="d-none d-md-block">معلومات الاتصال</span>
                                                </a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="#legal-info" data-bs-toggle="tab" aria-expanded="false" class="nav-link">
                                                    <i class="mdi mdi-file-document d-md-none d-block"></i>
                                                    <span class="d-none d-md-block">المعلومات القانونية</span>
                                                </a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="#addresses" data-bs-toggle="tab" aria-expanded="true" class="nav-link">
                                                    <i class="mdi mdi-map-marker d-md-none d-block"></i>
                                                    <span class="d-none d-md-block">العناوين</span>
                                                </a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="#bank-accounts" data-bs-toggle="tab" aria-expanded="false" class="nav-link">
                                                    <i class="mdi mdi-bank d-md-none d-block"></i>
                                                    <span class="d-none d-md-block">الحسابات البنكية</span>
                                                </a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="#terms-notes" data-bs-toggle="tab" aria-expanded="false" class="nav-link">
                                                    <i class="mdi mdi-handshake d-md-none d-block"></i>
                                                    <span class="d-none d-md-block">شروط التعامل</span>
                                                </a>
                                            </li>
                                        </ul>

                                        <div class="tab-content">
                                            
                                            <!-- التبويب الأول: المعلومات الأساسية -->
                                            <div class="tab-pane show active" id="basic-info">
                                                
                                                <!-- معلومات أساسية -->
                                                <div class="row">
                                                    <div class="col-12">
                                                        <h5 class="mb-3 text-uppercase bg-light p-2">
                                                            <i class="mdi mdi-account-circle me-1"></i> المعلومات الأساسية
                                                        </h5>
                                                    </div>
                                                </div>

                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="G_name_ar" class="form-label">اسم المورد بالعربية <span class="text-danger">*</span></label>
                                                            <input type="text" class="form-control" id="G_name_ar" name="G_name_ar" 
                                                                   value="<?= htmlspecialchars($supplier['G_name_ar']) ?>" required>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="G_name_en" class="form-label">اسم المورد بالإنجليزية</label>
                                                            <input type="text" class="form-control" id="G_name_en" name="G_name_en" 
                                                                   value="<?= htmlspecialchars($supplier['G_name_en'] ?? '') ?>">
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="S_company_name" class="form-label">اسم الشركة</label>
                                                            <input type="text" class="form-control" id="S_company_name" name="S_company_name" 
                                                                   value="<?= htmlspecialchars($supplier['S_company_name'] ?? '') ?>">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="S_contact_person" class="form-label">الشخص المسؤول</label>
                                                            <input type="text" class="form-control" id="S_contact_person" name="S_contact_person" 
                                                                   value="<?= htmlspecialchars($supplier['S_contact_person'] ?? '') ?>">
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="group_id" class="form-label">المجموعة</label>
                                                            <select class="form-select" id="group_id" name="group_id">
                                                                <option value="">اختر المجموعة</option>
                                                                <?php foreach ($supplierGroups as $group): ?>
                                                                    <option value="<?= $group['group_number'] ?>"
                                                                            <?= $supplier['group_number'] == $group['group_number'] ? 'selected' : '' ?>>
                                                                        <?= htmlspecialchars($group['name_ar']) ?>
                                                                        <?= isset($group['is_default']) && $group['is_default'] ? ' (افتراضي)' : '' ?>
                                                                    </option>
                                                                <?php endforeach; ?>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="G_status" class="form-label">الحالة</label>
                                                            <select class="form-select" id="G_status" name="G_status">
                                                                <option value="active" <?= $supplier['G_status'] === 'active' ? 'selected' : '' ?>>نشط</option>
                                                                <option value="inactive" <?= $supplier['G_status'] === 'inactive' ? 'selected' : '' ?>>غير نشط</option>
                                                                <option value="suspended" <?= $supplier['G_status'] === 'suspended' ? 'selected' : '' ?>>معلق</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- ملاحظات عامة -->
                                                <div class="row">
                                                    <div class="col-12">
                                                        <h5 class="mb-3 text-uppercase bg-light p-2">
                                                            <i class="mdi mdi-note-text me-1"></i> ملاحظات عامة
                                                        </h5>
                                                    </div>
                                                </div>

                                                <div class="row">
                                                    <div class="col-12">
                                                        <div class="mb-3">
                                                            <label for="G_notes" class="form-label">ملاحظات</label>
                                                            <textarea class="form-control" id="G_notes" name="G_notes" rows="4"
                                                                      placeholder="أي ملاحظات أو تعليقات خاصة بالمورد..."><?= htmlspecialchars($supplier['G_notes'] ?? '') ?></textarea>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- سيتم إضافة باقي التبويبات هنا -->
                                            
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                </div> <!-- container -->

            </div> <!-- content -->

            <!-- Footer Start -->
            <?php include_once 'App/Views/partials/footer.php'; ?>
            <!-- end Footer -->

        </div>

        <!-- ============================================================== -->
        <!-- End Page content -->
        <!-- ============================================================== -->

    </div>
    <!-- END wrapper -->

    <!-- Right Sidebar -->
    <?php include_once 'App/Views/partials/right-sidebar.php'; ?>

    <!-- /Right-bar -->

    <!-- bundle -->
    <script src="<?= base_url('public/js/vendor.min.js') ?>"></script>
    <script src="<?= base_url('public/js/app.min.js') ?>"></script>

</body>
</html>
