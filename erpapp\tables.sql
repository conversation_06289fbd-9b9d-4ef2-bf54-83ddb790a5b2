CREATE TABLE concerned_entities (
    id INT AUTO_INCREMENT PRIMARY KEY,

    section_id INT NOT NULL,                    -- القسم (كود القسم: صنف، عميل، مورد، ... )
    group_id INT,                               -- المجموعة التابع لها
    unit_id INT,                                -- وحدة القياس (للأصناف أو حسب الحاجة)
    company_id INT NOT NULL,                    -- الشركة المالكة للبيان

    -- ✅ الرقم الفريد البسيط (داخلي للشركة والقسم)
    entity_number INT NOT NULL,                 -- الرقم الفريد داخل الشركة والقسم (هذا ما يظهر في المسارات)

    -- ✅ معلومات عامة مشتركة

    G_name_ar VARCHAR(150) NOT NULL,            -- الاسم بالعربية
    G_name_en VARCHAR(150),                     -- الاسم بالإنجليزية
    G_description_ar TEXT,                      -- وصف بالعربية
    G_description_en TEXT,                      -- وصف بالإنجليزية
    G_phone VARCHAR(50),                        -- رقم الهاتف الرئيسي
    G_mobile VARCHAR(50),                       -- رقم الجوال الرئيسي
    G_website VARCHAR(255),                     -- الموقع الإلكتروني
    G_notes TEXT,                               -- ملاحظات عامة
    G_image VARCHAR(255),                       -- صورة الكيان
    G_status ENUM('active', 'inactive', 'suspended') DEFAULT 'active', -- الحالة

    -- ✅ معلومات خاصة بالأصناف (Inventory)
    I_purchase_price DECIMAL(15,4),             -- سعر الشراء
    I_selling_price DECIMAL(15,4),              -- سعر البيع
    I_wholesale_price DECIMAL(15,4),            -- سعر الجملة
    I_retail_price DECIMAL(15,4),               -- سعر التجزئة
    I_cost_price DECIMAL(15,4),                 -- سعر التكلفة
    I_tax_rate DECIMAL(5,2) DEFAULT 0,          -- معدل الضريبة %
    I_discount_rate DECIMAL(5,2) DEFAULT 0,     -- معدل الخصم %

    I_min_stock DECIMAL(15,4) DEFAULT 0,        -- الحد الأدنى للمخزون
    I_max_stock DECIMAL(15,4),                  -- الحد الأقصى للمخزون
    I_reorder_point DECIMAL(15,4),              -- نقطة إعادة الطلب
    I_current_stock DECIMAL(15,4) DEFAULT 0,    -- المخزون الحالي
    I_reserved_stock DECIMAL(15,4) DEFAULT 0,   -- المخزون المحجوز

    I_barcode VARCHAR(100),                     -- الباركود
    I_sku VARCHAR(100),                         -- رمز التخزين SKU
    I_weight DECIMAL(10,3),                     -- الوزن
    I_dimensions VARCHAR(100),                  -- الأبعاد (طول×عرض×ارتفاع)
    I_color VARCHAR(50),                        -- اللون
    I_size VARCHAR(50),                         -- الحجم
    I_brand VARCHAR(100),                       -- العلامة التجارية
    I_model VARCHAR(100),                       -- الموديل
    I_serial_number VARCHAR(100),               -- الرقم التسلسلي
    I_expiry_date DATE,                         -- تاريخ الانتهاء
    I_manufacturing_date DATE,                  -- تاريخ التصنيع
    I_warranty_period INT,                      -- فترة الضمان (بالأشهر)
    I_track_serial BOOLEAN DEFAULT FALSE,       -- تتبع الأرقام التسلسلية
    I_track_expiry BOOLEAN DEFAULT FALSE,       -- تتبع تواريخ الانتهاء
    I_is_service BOOLEAN DEFAULT FALSE,         -- هل هو خدمة
    I_is_digital BOOLEAN DEFAULT FALSE,         -- هل هو منتج رقمي

    -- ✅ معلومات خاصة بالعملاء (Customers)
    C_email VARCHAR(150),                       -- البريد الإلكتروني
    C_tax_number VARCHAR(50),                   -- الرقم الضريبي
    C_commercial_register VARCHAR(50),          -- السجل التجاري
    C_credit_limit DECIMAL(15,2) DEFAULT 0,     -- الحد الائتماني
    C_payment_terms INT DEFAULT 0,              -- شروط الدفع (بالأيام)
    C_discount_rate DECIMAL(5,2) DEFAULT 0,     -- معدل الخصم %
    C_customer_type ENUM('individual', 'company') DEFAULT 'individual', -- نوع العميل
    C_price_list VARCHAR(50),                   -- قائمة الأسعار المخصصة
    C_sales_rep_id INT,                         -- مندوب المبيعات
    C_territory VARCHAR(100),                   -- المنطقة الجغرافية
    C_industry VARCHAR(100),                    -- القطاع/الصناعة
    C_source VARCHAR(100),                      -- مصدر العميل
    C_rating ENUM('A', 'B', 'C', 'D') DEFAULT 'C', -- تقييم العميل

    -- ✅ معلومات خاصة بالموردين (Suppliers)
    S_company_name VARCHAR(150),                -- اسم الشركة (للمورد)
    S_contact_person VARCHAR(150),              -- اسم الشخص المسؤول
    S_email VARCHAR(150),                       -- البريد الإلكتروني للمورد
    S_tax_number VARCHAR(50),                   -- الرقم الضريبي للمورد
    S_commercial_register VARCHAR(50),          -- السجل التجاري للمورد
    S_payment_terms INT DEFAULT 30,             -- شروط الدفع (بالأيام)
    S_credit_limit DECIMAL(15,2) DEFAULT 0,     -- الحد الائتماني
    S_discount_rate DECIMAL(5,2) DEFAULT 0,     -- معدل الخصم المتفق عليه
    S_delivery_time INT,                        -- مدة التسليم (بالأيام)
    S_minimum_order DECIMAL(15,2),              -- الحد الأدنى للطلب
    S_currency VARCHAR(3) DEFAULT 'SAR',        -- العملة المتعامل بها
    S_rating ENUM('A', 'B', 'C', 'D') DEFAULT 'C', -- تقييم المورد



    -- ✅ معلومات قانونية إضافية للموردين
    S_license_number VARCHAR(50),               -- رقم الترخيص
    S_license_expiry DATE,                      -- تاريخ انتهاء الترخيص
    S_establishment_date DATE,                  -- تاريخ التأسيس
    S_legal_form ENUM('individual', 'llc', 'corporation', 'partnership', 'other'), -- الشكل القانوني

    -- ✅ ملاحظات إضافية للموردين
    S_internal_notes TEXT,                      -- ملاحظات داخلية
    S_special_instructions TEXT,                -- تعليمات خاصة

    -- ✅ معلومات خاصة بالموظفين (Employees)
    E_national_id VARCHAR(50),                  -- رقم الهوية
    E_job_title VARCHAR(100),                   -- المسمى الوظيفي
    E_department VARCHAR(100),                  -- القسم
    E_salary DECIMAL(10,2),                     -- الراتب الأساسي
    E_commission_rate DECIMAL(5,2) DEFAULT 0,   -- معدل العمولة %
    E_hire_date DATE,                           -- تاريخ التوظيف
    E_birth_date DATE,                          -- تاريخ الميلاد
    E_gender ENUM('male', 'female'),            -- الجنس
    E_marital_status ENUM('single', 'married', 'divorced', 'widowed'), -- الحالة الاجتماعية
    E_emergency_contact VARCHAR(150),           -- جهة الاتصال في الطوارئ
    E_emergency_phone VARCHAR(50),              -- هاتف الطوارئ

  
    -- ✅ النظام
    created_by INT,
    updated_by INT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- ✅ الفهارس فقط (بدون علاقات خارجية)
    -- فهرس فريد لضمان عدم تكرار الرقم داخل نفس الشركة والقسم
    UNIQUE KEY unique_entity_number (company_id, section_id, entity_number),

    -- فهارس للبحث السريع
    INDEX idx_company_section (company_id, section_id),
    INDEX idx_entity_number (entity_number),
    INDEX idx_status (G_status),
    INDEX idx_section_id (section_id),
    INDEX idx_group_id (group_id),
    INDEX idx_unit_id (unit_id),
    INDEX idx_company_id (company_id)
);



CREATE TABLE units (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT NOT NULL,                    -- يدعم التعدد حسب الشركة

    -- ✅ الرقم الفريد البسيط (داخلي للشركة)
    unit_number INT NOT NULL,                   -- الرقم الفريد داخل الشركة (هذا ما يظهر في المسارات)

    name_ar VARCHAR(100) NOT NULL,              -- اسم الوحدة بالعربية
    name_en VARCHAR(100),                       -- اسم الوحدة بالإنجليزية
    code VARCHAR(50),                           -- كود داخلي للوحدة (اختياري)

    created_by INT,
    updated_by INT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- فهرس فريد لضمان عدم تكرار الرقم داخل نفس الشركة
    UNIQUE KEY unique_unit_number (company_id, unit_number),
    INDEX idx_company_id (company_id)
);



CREATE TABLE entity_groups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    section_id INT NOT NULL,                    -- القسم المرتبط
    company_id INT NOT NULL,                    -- للشركة المالكة

    -- ✅ الرقم الفريد البسيط (داخلي للشركة والقسم)
    group_number INT NOT NULL,                  -- الرقم الفريد داخل الشركة والقسم (هذا ما يظهر في المسارات)

    name_ar VARCHAR(150) NOT NULL,              -- الاسم بالعربي
    name_en VARCHAR(150),                       -- الاسم بالإنجليزي
    is_default BOOLEAN DEFAULT FALSE,           -- هل هي المجموعة الافتراضية
    created_by INT,
    updated_by INT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- فهرس فريد لضمان عدم تكرار الرقم داخل نفس الشركة والقسم
    UNIQUE KEY unique_group_number (company_id, section_id, group_number),
    INDEX idx_section_id (section_id),
    INDEX idx_company_id (company_id),
    INDEX idx_default_group (company_id, section_id, is_default)
);


CREATE TABLE sections (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,      -- كود القسم مثل: products, customers, ...
    description TEXT,                      -- وصف القسم وما يعنيه
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);





-- ✅ جدول العناوين المتعددة
CREATE TABLE entity_addresses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    entity_id INT NOT NULL,                     -- الكيان المرتبط
    company_id INT NOT NULL,

    -- نوع العنوان
    address_type ENUM('main', 'billing', 'shipping', 'office', 'home', 'warehouse', 'other') DEFAULT 'main',
    address_label VARCHAR(100),                 -- تسمية العنوان (مثل: المكتب الرئيسي، فرع الرياض)

    -- تفاصيل العنوان
    address_line1 VARCHAR(255) NOT NULL,        -- السطر الأول من العنوان
    address_line2 VARCHAR(255),                 -- السطر الثاني (اختياري)
    district VARCHAR(100),                      -- الحي/المنطقة
    city VARCHAR(100) NOT NULL,                 -- المدينة
    state_province VARCHAR(100),                -- المحافظة/الولاية
    postal_code VARCHAR(20),                    -- الرمز البريدي
    country VARCHAR(100) DEFAULT 'Saudi Arabia', -- الدولة

    -- معلومات إضافية
    latitude DECIMAL(10, 8),                    -- خط العرض (للخرائط)
    longitude DECIMAL(11, 8),                   -- خط الطول (للخرائط)
    phone VARCHAR(50),                          -- هاتف خاص بهذا العنوان
    contact_person VARCHAR(150),                -- الشخص المسؤول في هذا العنوان

    -- الحالة والأولوية
    is_default BOOLEAN DEFAULT FALSE,           -- هل هو العنوان الافتراضي
    is_active BOOLEAN DEFAULT TRUE,             -- هل العنوان نشط
    priority_order INT DEFAULT 1,              -- ترتيب الأولوية

    -- معلومات إضافية
    delivery_instructions TEXT,                 -- تعليمات التسليم
    access_notes TEXT,                          -- ملاحظات الوصول
    working_hours VARCHAR(255),                 -- ساعات العمل

    -- النظام
    created_by INT,
    updated_by INT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- فهارس للبحث السريع
    INDEX idx_entity_id (entity_id),
    INDEX idx_company_id (company_id),
    INDEX idx_entity_default (entity_id, is_default),
    INDEX idx_entity_type (entity_id, address_type)
);

-- ✅ جدول الحسابات البنكية المتعددة
CREATE TABLE entity_bank_accounts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    entity_id INT NOT NULL,                     -- الكيان المرتبط
    company_id INT NOT NULL,

    -- معلومات البنك
    bank_name VARCHAR(150) NOT NULL,            -- اسم البنك
    bank_code VARCHAR(20),                      -- كود البنك (SWIFT/BIC)
    branch_name VARCHAR(150),                   -- اسم الفرع
    branch_code VARCHAR(20),                    -- كود الفرع

    -- معلومات الحساب
    account_number VARCHAR(50) NOT NULL,        -- رقم الحساب
    account_name VARCHAR(150) NOT NULL,         -- اسم صاحب الحساب
    account_type ENUM('checking', 'savings', 'business', 'other') DEFAULT 'checking', -- نوع الحساب

    -- معلومات دولية
    iban VARCHAR(50),                           -- رقم الآيبان الدولي
    swift_code VARCHAR(20),                     -- كود السويفت
    routing_number VARCHAR(20),                 -- رقم التوجيه (للبنوك الأمريكية)

    -- العملة والحالة
    currency VARCHAR(3) DEFAULT 'SAR',          -- عملة الحساب
    is_default BOOLEAN DEFAULT FALSE,           -- هل هو الحساب الافتراضي
    is_active BOOLEAN DEFAULT TRUE,             -- هل الحساب نشط

    -- معلومات إضافية
    account_purpose ENUM('general', 'payroll', 'tax', 'investment', 'loan', 'other') DEFAULT 'general', -- غرض الحساب
    credit_limit DECIMAL(15,2),                 -- الحد الائتماني (إن وجد)
    minimum_balance DECIMAL(15,2),              -- الحد الأدنى للرصيد

    -- معلومات الاتصال البنكي
    bank_phone VARCHAR(50),                     -- هاتف البنك
    bank_email VARCHAR(150),                    -- بريد البنك الإلكتروني
    bank_address TEXT,                          -- عنوان البنك

    -- ملاحظات
    notes TEXT,                                 -- ملاحظات خاصة بالحساب

    -- النظام
    created_by INT,
    updated_by INT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- فهارس للبحث السريع
    UNIQUE KEY unique_account (entity_id, bank_name, account_number),
    INDEX idx_entity_id (entity_id),
    INDEX idx_company_id (company_id),
    INDEX idx_entity_default (entity_id, is_default),
    INDEX idx_iban (iban),
    INDEX idx_account_number (account_number)
);

-- ✅ جدول فلاتر المستخدمين المحفوظة
CREATE TABLE user_filters (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,                       -- المستخدم
    company_id INT NOT NULL,                    -- الشركة

    -- معلومات الفلتر
    page_name VARCHAR(100) NOT NULL,            -- اسم الصفحة (suppliers, customers, products, etc.)
    filter_name VARCHAR(100),                   -- اسم الفلتر (اختياري - للفلاتر المحفوظة)

    -- بيانات الفلتر (JSON)
    filter_data JSON NOT NULL,                  -- بيانات الفلتر بصيغة JSON

    -- إعدادات الفلتر
    is_default BOOLEAN DEFAULT FALSE,           -- هل هو الفلتر الافتراضي للصفحة
    is_auto_apply BOOLEAN DEFAULT TRUE,         -- هل يطبق تلقائياً عند دخول الصفحة

    -- معلومات إضافية
    description TEXT,                           -- وصف الفلتر
    usage_count INT DEFAULT 0,                  -- عدد مرات الاستخدام
    last_used_at DATETIME,                      -- آخر استخدام

    -- النظام
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- فهارس للبحث السريع
    UNIQUE KEY unique_user_page_default (user_id, company_id, page_name, is_default),
    INDEX idx_user_company (user_id, company_id),
    INDEX idx_page_name (page_name),
    INDEX idx_last_used (last_used_at),
    INDEX idx_usage_count (usage_count)
);

INSERT INTO sections (code, description) VALUES
('products', 'الأصناف التي يتم بيعها أو تصنيعها أو تخزينها'),
('customers', 'العملاء الذين يتم البيع لهم'),
('suppliers', 'الموردين الذين يتم الشراء منهم'),
('employees', 'الموظفين داخل الشركة'),
('agents', 'المناديب أو المندوبين الذين يروجون أو يبيعون'),
('warehouses', 'المستودعات ومواقع التخزين'),
('assets', 'الأصول الثابتة والمعدات');

-- ✅ مثال على الاستخدام:
-- الشركة 1 - العملاء: entity_number = 1, 2, 3, 4...
-- الشركة 1 - الموردين: entity_number = 1, 2, 3, 4...
-- الشركة 1 - المنتجات: entity_number = 1, 2, 3, 4...

-- الشركة 2 - العملاء: entity_number = 1, 2, 3, 4...
-- الشركة 2 - الموردين: entity_number = 1, 2, 3, 4...

-- المسارات ستكون:
-- /customers/edit/1    (الشركة الحالية، العميل رقم 1)
-- /customers/edit/2    (الشركة الحالية، العميل رقم 2)
-- /suppliers/view/1    (الشركة الحالية، المورد رقم 1)
