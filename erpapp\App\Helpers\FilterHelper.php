<?php
/**
 * مساعد الفلاتر - للتعامل مع فلاتر المستخدمين المحفوظة
 */

require_once __DIR__ . '/../Models/UserFilter.php';

use App\Models\UserFilter;

class FilterHelper
{
    private static $userFilterModel;

    /**
     * تهيئة النموذج
     */
    private static function initModel()
    {
        if (!self::$userFilterModel) {
            self::$userFilterModel = new UserFilter();
        }
    }

    /**
     * تحضير الفلاتر للصفحة مع دمج الفلاتر المحفوظة
     */
    public static function prepareFilters($page_name, $default_filters = [])
    {
        self::initModel();
        
        $user = current_user();
        if (!$user) {
            return $default_filters;
        }

        $user_id = $user['id'];
        $company_id = $user['current_company_id'];

        // الحصول على الفلاتر من الطلب الحالي
        $request_filters = [];
        foreach ($default_filters as $key => $default_value) {
            $request_filters[$key] = $_GET[$key] ?? $default_value;
        }

        // إذا كان هناك فلاتر في الطلب، احفظها
        $has_request_filters = false;
        foreach ($request_filters as $key => $value) {
            if (!empty($value) && $key !== 'page' && $key !== 'per_page') {
                $has_request_filters = true;
                break;
            }
        }

        if ($has_request_filters) {
            // حفظ الفلاتر الحالية
            self::$userFilterModel->saveFilter($user_id, $company_id, $page_name, $request_filters);
            return $request_filters;
        }

        // إذا لم تكن هناك فلاتر في الطلب، جرب الحصول على الفلاتر المحفوظة
        $saved_filter = self::$userFilterModel->getUserFilter($user_id, $company_id, $page_name);
        
        if ($saved_filter && $saved_filter['is_auto_apply']) {
            // تطبيق الفلاتر المحفوظة
            $applied_filters = self::$userFilterModel->applyFilter($saved_filter['filter_data'], $default_filters);
            
            // تحديث عداد الاستخدام
            self::$userFilterModel->updateUsageCount($saved_filter['id']);
            
            return $applied_filters;
        }

        return $default_filters;
    }

    /**
     * حفظ فلتر مسمى
     */
    public static function saveNamedFilter($page_name, $filter_name, $filter_data, $description = null)
    {
        self::initModel();
        
        $user = current_user();
        if (!$user) {
            return false;
        }

        return self::$userFilterModel->saveNamedFilter(
            $user['id'], 
            $user['current_company_id'], 
            $page_name, 
            $filter_name, 
            $filter_data, 
            $description
        );
    }

    /**
     * الحصول على الفلاتر المحفوظة للصفحة
     */
    public static function getSavedFilters($page_name)
    {
        self::initModel();
        
        $user = current_user();
        if (!$user) {
            return [];
        }

        return self::$userFilterModel->getSavedFilters(
            $user['id'], 
            $user['current_company_id'], 
            $page_name
        );
    }

    /**
     * حذف فلتر محفوظ
     */
    public static function deleteFilter($filter_id)
    {
        self::initModel();
        
        $user = current_user();
        if (!$user) {
            return false;
        }

        return self::$userFilterModel->deleteFilter(
            $filter_id, 
            $user['id'], 
            $user['current_company_id']
        );
    }

    /**
     * مسح جميع الفلاتر للصفحة
     */
    public static function clearPageFilters($page_name)
    {
        self::initModel();
        
        $user = current_user();
        if (!$user) {
            return false;
        }

        // مسح الفلتر الافتراضي فقط
        try {
            global $db;
            $sql = "DELETE FROM user_filters 
                    WHERE user_id = ? AND company_id = ? AND page_name = ? AND is_default = TRUE";
            $stmt = $db->prepare($sql);
            return $stmt->execute([$user['id'], $user['current_company_id'], $page_name]);
        } catch (Exception $e) {
            error_log("Error clearing page filters: " . $e->getMessage());
            return false;
        }
    }

    /**
     * الحصول على رابط نظيف بدون فلاتر
     */
    public static function getCleanUrl($base_url, $keep_params = ['per_page'])
    {
        $params = [];
        
        foreach ($keep_params as $param) {
            if (isset($_GET[$param]) && !empty($_GET[$param])) {
                $params[$param] = $_GET[$param];
            }
        }

        if (empty($params)) {
            return $base_url;
        }

        return $base_url . '?' . http_build_query($params);
    }

    /**
     * التحقق من وجود فلاتر نشطة
     */
    public static function hasActiveFilters($filters, $exclude_keys = ['page', 'per_page', 'limit', 'offset'])
    {
        foreach ($filters as $key => $value) {
            if (!in_array($key, $exclude_keys) && !empty($value)) {
                return true;
            }
        }
        return false;
    }

    /**
     * عد الفلاتر النشطة
     */
    public static function countActiveFilters($filters, $exclude_keys = ['page', 'per_page', 'limit', 'offset'])
    {
        $count = 0;
        foreach ($filters as $key => $value) {
            if (!in_array($key, $exclude_keys) && !empty($value)) {
                $count++;
            }
        }
        return $count;
    }

    /**
     * تحويل الفلاتر إلى نص وصفي
     */
    public static function filtersToDescription($filters, $options = [])
    {
        $descriptions = [];
        
        foreach ($filters as $key => $value) {
            if (empty($value) || in_array($key, ['page', 'per_page', 'limit', 'offset'])) {
                continue;
            }

            switch ($key) {
                case 'search':
                    $descriptions[] = "البحث: {$value}";
                    break;
                case 'status':
                    $status_labels = $options['status_labels'] ?? [
                        'active' => 'نشط',
                        'inactive' => 'غير نشط',
                        'suspended' => 'معلق'
                    ];
                    $descriptions[] = "الحالة: " . ($status_labels[$value] ?? $value);
                    break;
                case 'group_id':
                    if (isset($options['groups'])) {
                        foreach ($options['groups'] as $group) {
                            if ($group['group_number'] == $value) {
                                $descriptions[] = "المجموعة: {$group['name_ar']}";
                                break;
                            }
                        }
                    } else {
                        $descriptions[] = "المجموعة: {$value}";
                    }
                    break;
                default:
                    $descriptions[] = "{$key}: {$value}";
            }
        }

        return implode(' | ', $descriptions);
    }

    /**
     * إنشاء رابط مع الفلاتر
     */
    public static function buildFilterUrl($base_url, $filters, $additional_params = [])
    {
        $params = array_merge($filters, $additional_params);
        
        // إزالة القيم الفارغة
        $params = array_filter($params, function($value) {
            return $value !== '' && $value !== null;
        });

        if (empty($params)) {
            return $base_url;
        }

        return $base_url . '?' . http_build_query($params);
    }

    /**
     * تنظيف دوري للفلاتر القديمة
     */
    public static function cleanupOldFilters($days = 90)
    {
        self::initModel();
        return self::$userFilterModel->cleanOldFilters($days);
    }
}
