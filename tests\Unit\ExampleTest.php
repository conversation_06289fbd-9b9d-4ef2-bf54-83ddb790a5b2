<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;

class ExampleTest extends TestCase
{
    public function testBasicTest()
    {
        $this->assertTrue(true);
    }

    public function testApplicationConstants()
    {
        $this->assertTrue(defined('BASE_PATH'));
        $this->assertNotEmpty(BASE_PATH);
    }

    public function testComposerAutoloader()
    {
        $this->assertTrue(file_exists(dirname(dirname(__DIR__)) . '/vendor/autoload.php'));
    }

    public function testCoreClasses()
    {
        $this->assertTrue(class_exists('App\Core\Module'));
        $this->assertTrue(class_exists('App\Core\ModuleRouter'));
        $this->assertTrue(class_exists('App\Core\Router'));
    }
}
