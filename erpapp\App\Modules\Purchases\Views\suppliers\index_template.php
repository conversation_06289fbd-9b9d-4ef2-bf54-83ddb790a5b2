<?php
/**
 * صفحة الموردين الجديدة باستخدام النظام الموحد
 * مثال على كيفية استخدام Templates مع النظام الحالي
 */

// إعداد البيانات للـ Template
$title = 'الموردين';
$module_name = 'purchases';
$entity_name = 'suppliers';

// إعداد الأعمدة
$columns = [
    [
        'field' => 'entity_number',
        'title' => 'رقم المورد',
        'type' => 'text',
        'width' => '120px'
    ],
    [
        'field' => 'G_name_ar',
        'title' => 'اسم المورد',
        'type' => 'link',
        'url' => 'purchases/suppliers/{id}',
        'id_field' => 'entity_number',
        'subtitle_field' => 'G_name_en'
    ],
    [
        'field' => 'group_name_ar',
        'title' => 'المجموعة',
        'type' => 'badge',
        'badge_classes' => ['default' => 'info'],
        'badge_texts' => ['default' => 'group_name_ar']
    ],
    [
        'field' => 'G_status',
        'title' => 'الحالة',
        'type' => 'badge',
        'badge_classes' => [
            'active' => 'success',
            'inactive' => 'secondary',
            'suspended' => 'warning'
        ],
        'badge_texts' => [
            'active' => 'نشط',
            'inactive' => 'غير نشط',
            'suspended' => 'معلق'
        ]
    ],
    [
        'field' => 'actions',
        'title' => 'الإجراءات',
        'type' => 'actions',
        'width' => '150px',
        'buttons' => [
            [
                'type' => 'link',
                'url' => 'purchases/suppliers/{id}',
                'id_field' => 'entity_number',
                'class' => 'btn-primary',
                'icon' => 'mdi mdi-eye',
                'title' => 'عرض'
            ],
            [
                'type' => 'link',
                'url' => 'purchases/suppliers/{id}/edit',
                'id_field' => 'entity_number',
                'class' => 'btn-success',
                'icon' => 'mdi mdi-pencil',
                'title' => 'تعديل'
            ],
            [
                'type' => 'button',
                'class' => 'btn-danger',
                'icon' => 'mdi mdi-delete',
                'title' => 'حذف',
                'onclick' => 'confirmDelete({id})',
                'id_field' => 'entity_number'
            ]
        ]
    ]
];

// إعداد الفلاتر
$filters_config = [
    [
        'name' => 'search',
        'type' => 'search',
        'label' => 'البحث',
        'placeholder' => 'ابحث في أسماء الموردين...',
        'icon' => 'mdi mdi-magnify',
        'col_size' => 6
    ],
    [
        'name' => 'status',
        'type' => 'select',
        'label' => 'الحالة',
        'placeholder' => 'جميع الحالات',
        'icon' => 'mdi mdi-check-circle',
        'options' => [
            'active' => 'نشط',
            'inactive' => 'غير نشط',
            'suspended' => 'معلق'
        ],
        'col_size' => 3
    ],
    [
        'name' => 'group_id',
        'type' => 'select',
        'label' => 'المجموعة',
        'placeholder' => 'جميع المجموعات',
        'icon' => 'mdi mdi-folder',
        'options' => array_column($supplierGroups ?? [], 'name_ar', 'id'),
        'col_size' => 3
    ]
];

// إعداد الإجراءات
$actions = [
    [
        'type' => 'link',
        'url' => 'purchases/suppliers/create',
        'class' => 'btn-danger',
        'icon' => 'mdi mdi-plus-circle',
        'text' => 'إضافة مورد جديد',
        'position' => 'header'
    ],
    [
        'type' => 'dropdown',
        'class' => 'btn-secondary',
        'icon' => 'mdi mdi-download',
        'text' => 'تصدير',
        'position' => 'toolbar',
        'items' => [
            [
                'url' => 'purchases/suppliers/export/excel',
                'icon' => 'mdi mdi-file-excel',
                'text' => 'تصدير Excel'
            ],
            [
                'url' => 'purchases/suppliers/export/pdf',
                'icon' => 'mdi mdi-file-pdf',
                'text' => 'تصدير PDF'
            ],
            ['type' => 'divider'],
            [
                'url' => 'purchases/suppliers/import',
                'icon' => 'mdi mdi-upload',
                'text' => 'استيراد من Excel'
            ]
        ]
    ]
];

// إعداد Breadcrumb
$breadcrumb = [
    ['title' => 'المشتريات', 'url' => 'purchases'],
    ['title' => 'الموردين', 'active' => true]
];

// إعداد الإحصائيات
$stats = [
    [
        'title' => 'إجمالي الموردين',
        'value' => $stats_data['total'] ?? 0,
        'icon' => 'mdi mdi-account-group',
        'color' => 'primary'
    ],
    [
        'title' => 'الموردين النشطين',
        'value' => $stats_data['active'] ?? 0,
        'icon' => 'mdi mdi-check-circle',
        'color' => 'success'
    ],
    [
        'title' => 'الموردين المعلقين',
        'value' => $stats_data['suspended'] ?? 0,
        'icon' => 'mdi mdi-pause-circle',
        'color' => 'warning'
    ],
    [
        'title' => 'الموردين غير النشطين',
        'value' => $stats_data['inactive'] ?? 0,
        'icon' => 'mdi mdi-close-circle',
        'color' => 'secondary'
    ]
];

// إعداد Empty State
$empty_state = [
    'icon' => 'mdi mdi-truck-outline',
    'message' => 'لا توجد موردين',
    'action' => [
        'url' => 'purchases/suppliers/create',
        'text' => 'إضافة مورد جديد'
    ]
];

// البيانات (من Controller)
$data = $suppliers; // البيانات القادمة من Controller

// تضمين Template الأساسي
render_datatable([
    'title' => $title,
    'module_name' => $module_name,
    'entity_name' => $entity_name,
    'columns' => $columns,
    'data' => $data,
    'filters_config' => $filters_config,
    'actions' => $actions,
    'pagination' => $pagination,
    'filters' => $filters,
    'breadcrumb' => $breadcrumb,
    'stats' => $stats,
    'empty_state' => $empty_state
]);
?>
