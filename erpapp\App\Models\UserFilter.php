<?php
namespace App\Models;

use PDO;

class UserFilter
{
    private $db;
    private $table = 'user_filters';

    public function __construct()
    {
        global $db;
        $this->db = $db;
    }

    /**
     * حفظ فلتر المستخدم
     */
    public function saveFilter($user_id, $company_id, $page_name, $filter_data, $options = [])
    {
        try {
            // تحويل بيانات الفلتر إلى JSON
            $filter_json = json_encode($filter_data, JSON_UNESCAPED_UNICODE);
            
            // البحث عن فلتر موجود للصفحة
            $existing = $this->getUserFilter($user_id, $company_id, $page_name);
            
            if ($existing) {
                // تحديث الفلتر الموجود
                $sql = "UPDATE {$this->table} SET 
                        filter_data = ?, 
                        usage_count = usage_count + 1,
                        last_used_at = NOW(),
                        updated_at = NOW()
                        WHERE user_id = ? AND company_id = ? AND page_name = ? AND is_default = TRUE";
                
                $stmt = $this->db->prepare($sql);
                return $stmt->execute([$filter_json, $user_id, $company_id, $page_name]);
            } else {
                // إنشاء فلتر جديد
                $sql = "INSERT INTO {$this->table} 
                        (user_id, company_id, page_name, filter_data, is_default, is_auto_apply, usage_count, last_used_at) 
                        VALUES (?, ?, ?, ?, TRUE, TRUE, 1, NOW())";
                
                $stmt = $this->db->prepare($sql);
                return $stmt->execute([$user_id, $company_id, $page_name, $filter_json]);
            }
        } catch (Exception $e) {
            error_log("Error saving user filter: " . $e->getMessage());
            return false;
        }
    }

    /**
     * الحصول على فلتر المستخدم للصفحة
     */
    public function getUserFilter($user_id, $company_id, $page_name)
    {
        try {
            $sql = "SELECT * FROM {$this->table} 
                    WHERE user_id = ? AND company_id = ? AND page_name = ? AND is_default = TRUE
                    ORDER BY last_used_at DESC LIMIT 1";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id, $company_id, $page_name]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result && $result['filter_data']) {
                $result['filter_data'] = json_decode($result['filter_data'], true);
            }
            
            return $result;
        } catch (Exception $e) {
            error_log("Error getting user filter: " . $e->getMessage());
            return null;
        }
    }

    /**
     * الحصول على جميع الفلاتر المحفوظة للمستخدم في صفحة معينة
     */
    public function getSavedFilters($user_id, $company_id, $page_name)
    {
        try {
            $sql = "SELECT * FROM {$this->table} 
                    WHERE user_id = ? AND company_id = ? AND page_name = ? AND filter_name IS NOT NULL
                    ORDER BY usage_count DESC, last_used_at DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id, $company_id, $page_name]);
            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($results as &$result) {
                if ($result['filter_data']) {
                    $result['filter_data'] = json_decode($result['filter_data'], true);
                }
            }
            
            return $results;
        } catch (Exception $e) {
            error_log("Error getting saved filters: " . $e->getMessage());
            return [];
        }
    }

    /**
     * حفظ فلتر مسمى (للفلاتر المحفوظة)
     */
    public function saveNamedFilter($user_id, $company_id, $page_name, $filter_name, $filter_data, $description = null)
    {
        try {
            $filter_json = json_encode($filter_data, JSON_UNESCAPED_UNICODE);
            
            $sql = "INSERT INTO {$this->table} 
                    (user_id, company_id, page_name, filter_name, filter_data, description, is_default, is_auto_apply, usage_count, last_used_at) 
                    VALUES (?, ?, ?, ?, ?, ?, FALSE, FALSE, 1, NOW())";
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([$user_id, $company_id, $page_name, $filter_name, $filter_json, $description]);
        } catch (Exception $e) {
            error_log("Error saving named filter: " . $e->getMessage());
            return false;
        }
    }

    /**
     * حذف فلتر محفوظ
     */
    public function deleteFilter($filter_id, $user_id, $company_id)
    {
        try {
            $sql = "DELETE FROM {$this->table} 
                    WHERE id = ? AND user_id = ? AND company_id = ? AND filter_name IS NOT NULL";
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([$filter_id, $user_id, $company_id]);
        } catch (Exception $e) {
            error_log("Error deleting filter: " . $e->getMessage());
            return false;
        }
    }

    /**
     * تحديث عداد الاستخدام
     */
    public function updateUsageCount($filter_id)
    {
        try {
            $sql = "UPDATE {$this->table} SET 
                    usage_count = usage_count + 1,
                    last_used_at = NOW()
                    WHERE id = ?";
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([$filter_id]);
        } catch (Exception $e) {
            error_log("Error updating usage count: " . $e->getMessage());
            return false;
        }
    }

    /**
     * مسح الفلاتر القديمة (تنظيف دوري)
     */
    public function cleanOldFilters($days = 90)
    {
        try {
            $sql = "DELETE FROM {$this->table} 
                    WHERE filter_name IS NULL 
                    AND last_used_at < DATE_SUB(NOW(), INTERVAL ? DAY)";
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([$days]);
        } catch (Exception $e) {
            error_log("Error cleaning old filters: " . $e->getMessage());
            return false;
        }
    }

    /**
     * الحصول على إحصائيات الفلاتر
     */
    public function getFilterStats($user_id, $company_id)
    {
        try {
            $sql = "SELECT 
                        page_name,
                        COUNT(*) as total_filters,
                        SUM(usage_count) as total_usage,
                        MAX(last_used_at) as last_used
                    FROM {$this->table} 
                    WHERE user_id = ? AND company_id = ?
                    GROUP BY page_name
                    ORDER BY total_usage DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id, $company_id]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error getting filter stats: " . $e->getMessage());
            return [];
        }
    }

    /**
     * تطبيق الفلتر المحفوظ على البيانات
     */
    public function applyFilter($filter_data, $default_filters = [])
    {
        if (!$filter_data || !is_array($filter_data)) {
            return $default_filters;
        }

        // دمج الفلاتر المحفوظة مع الافتراضية
        $applied_filters = array_merge($default_filters, $filter_data);
        
        // تنظيف القيم الفارغة
        foreach ($applied_filters as $key => $value) {
            if ($value === '' || $value === null) {
                unset($applied_filters[$key]);
            }
        }

        return $applied_filters;
    }
}
