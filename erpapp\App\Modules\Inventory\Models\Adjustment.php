<?php

namespace App\Modules\Inventory\Models;

use App\Core\Model;

class Adjustment extends Model
{
    protected $table = 'inventory_adjustments';
    protected $primaryKey = 'adjustment_id';
    
    protected $fillable = [
        'company_id',
        'module_code',
        'adjustment_number',
        'adjustment_date',
        'warehouse_id',
        'adjustment_type',
        'reason',
        'reference_number',
        'total_items',
        'total_value',
        'status',
        'notes',
        'created_by',
        'updated_by',
        'approved_by',
        'approved_at'
    ];

    protected $casts = [
        'total_value' => 'decimal:2',
        'total_items' => 'integer'
    ];

    protected $dates = [
        'adjustment_date',
        'approved_at',
        'created_at',
        'updated_at'
    ];

    // العلاقات
    public function company()
    {
        return $this->belongsTo('App\Models\Company', 'company_id', 'CompanyID');
    }

    public function warehouse()
    {
        return $this->belongsTo('App\Modules\Inventory\Models\Warehouse', 'warehouse_id', 'warehouse_id');
    }

    public function createdBy()
    {
        return $this->belongsTo('App\Models\User', 'created_by', 'UserID');
    }

    public function updatedBy()
    {
        return $this->belongsTo('App\Models\User', 'updated_by', 'UserID');
    }

    public function approvedBy()
    {
        return $this->belongsTo('App\Models\User', 'approved_by', 'UserID');
    }

    public function items()
    {
        return $this->hasMany('App\Modules\Inventory\Models\AdjustmentItem', 'adjustment_id', 'adjustment_id');
    }

    // الدوال المساعدة
    public function getAdjustmentTypeText()
    {
        $types = [
            'increase' => 'زيادة',
            'decrease' => 'نقص',
            'correction' => 'تصحيح'
        ];
        
        return $types[$this->adjustment_type] ?? $this->adjustment_type;
    }

    public function getAdjustmentTypeColor()
    {
        $colors = [
            'increase' => 'success',
            'decrease' => 'danger',
            'correction' => 'warning'
        ];
        
        return $colors[$this->adjustment_type] ?? 'secondary';
    }

    public function getStatusText()
    {
        $statuses = [
            'draft' => 'مسودة',
            'approved' => 'معتمد',
            'cancelled' => 'ملغي'
        ];
        
        return $statuses[$this->status] ?? $this->status;
    }

    public function getStatusColor()
    {
        $colors = [
            'draft' => 'warning',
            'approved' => 'success',
            'cancelled' => 'danger'
        ];
        
        return $colors[$this->status] ?? 'secondary';
    }

    public function canEdit()
    {
        return $this->status === 'draft';
    }

    public function canApprove()
    {
        return $this->status === 'draft' && $this->items()->exists();
    }

    public function canCancel()
    {
        return in_array($this->status, ['draft', 'approved']);
    }

    public function approve($userId = null)
    {
        if (!$this->canApprove()) {
            return false;
        }

        $this->status = 'approved';
        $this->approved_by = $userId ?: auth()->id();
        $this->approved_at = now();
        $this->save();

        // تطبيق التسويات على المخزون
        $this->applyAdjustments();

        return true;
    }

    public function cancel()
    {
        if (!$this->canCancel()) {
            return false;
        }

        $this->status = 'cancelled';
        $this->save();

        return true;
    }

    public function applyAdjustments()
    {
        foreach ($this->items as $item) {
            $item->applyToStock();
        }

        // إنشاء حركات المخزون
        $this->createMovements();
    }

    public function createMovements()
    {
        foreach ($this->items as $item) {
            if ($item->difference_quantity != 0) {
                $movementType = $item->difference_quantity > 0 ? 'adjustment_in' : 'adjustment_out';
                
                Movement::create([
                    'company_id' => $this->company_id,
                    'movement_number' => Movement::generateMovementNumber(),
                    'product_id' => $item->product_id,
                    'warehouse_id' => $this->warehouse_id,
                    'movement_type' => $movementType,
                    'quantity' => abs($item->difference_quantity),
                    'unit_cost' => $item->unit_cost,
                    'total_cost' => abs($item->total_cost),
                    'reference_type' => 'adjustment',
                    'reference_id' => $this->adjustment_id,
                    'reference_number' => $this->adjustment_number,
                    'movement_date' => $this->adjustment_date,
                    'notes' => "تسوية مخزون: {$this->reason}",
                    'created_by' => $this->approved_by
                ]);
            }
        }
    }

    public function calculateTotals()
    {
        $this->total_items = $this->items()->count();
        $this->total_value = $this->items()->sum('total_cost');
        $this->save();
    }

    // النطاقات (Scopes)
    public function scopeByCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    public function scopeByWarehouse($query, $warehouseId)
    {
        return $query->where('warehouse_id', $warehouseId);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('adjustment_type', $type);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('adjustment_date', [$startDate, $endDate]);
    }

    public function scopeSearch($query, $search)
    {
        return $query->where(function($q) use ($search) {
            $q->where('adjustment_number', 'LIKE', "%{$search}%")
              ->orWhere('reference_number', 'LIKE', "%{$search}%")
              ->orWhere('reason', 'LIKE', "%{$search}%")
              ->orWhere('notes', 'LIKE', "%{$search}%");
        });
    }

    // التحقق من الصحة
    public static function getValidationRules($id = null)
    {
        return [
            'adjustment_number' => 'required|string|max:50|unique:inventory_adjustments,adjustment_number,' . $id . ',adjustment_id,company_id,' . session('company_id'),
            'adjustment_date' => 'required|date',
            'warehouse_id' => 'required|exists:inventory_warehouses,warehouse_id',
            'adjustment_type' => 'required|in:increase,decrease,correction',
            'reason' => 'required|string|max:200',
            'reference_number' => 'nullable|string|max:100',
            'notes' => 'nullable|string|max:1000'
        ];
    }

    public static function getValidationMessages()
    {
        return [
            'adjustment_number.required' => 'رقم التسوية مطلوب',
            'adjustment_number.unique' => 'رقم التسوية موجود مسبقاً',
            'adjustment_date.required' => 'تاريخ التسوية مطلوب',
            'adjustment_date.date' => 'تاريخ التسوية غير صحيح',
            'warehouse_id.required' => 'المستودع مطلوب',
            'warehouse_id.exists' => 'المستودع المحدد غير موجود',
            'adjustment_type.required' => 'نوع التسوية مطلوب',
            'adjustment_type.in' => 'نوع التسوية غير صحيح',
            'reason.required' => 'سبب التسوية مطلوب'
        ];
    }

    // الأحداث
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->company_id = session('company_id');
            $model->module_code = 'inventory';
            $model->created_by = auth()->id();
            $model->status = 'draft';
            $model->total_items = 0;
            $model->total_value = 0.00;
            
            // توليد رقم التسوية إذا لم يكن موجوداً
            if (!$model->adjustment_number) {
                $model->adjustment_number = self::generateAdjustmentNumber();
            }
        });

        static::updating(function ($model) {
            $model->updated_by = auth()->id();
        });

        static::deleting(function ($model) {
            if ($model->status === 'approved') {
                throw new \Exception('لا يمكن حذف تسوية معتمدة');
            }
            
            // حذف العناصر المرتبطة
            $model->items()->delete();
        });
    }

    // دوال مساعدة
    public static function generateAdjustmentNumber()
    {
        $prefix = 'ADJ';
        $date = date('Ymd');
        $companyId = session('company_id');
        
        $lastNumber = self::where('company_id', $companyId)
                         ->where('adjustment_number', 'LIKE', $prefix . $date . '%')
                         ->orderBy('adjustment_number', 'desc')
                         ->first();
        
        if ($lastNumber) {
            $lastSequence = intval(substr($lastNumber->adjustment_number, -4));
            $newSequence = $lastSequence + 1;
        } else {
            $newSequence = 1;
        }
        
        return $prefix . $date . str_pad($newSequence, 4, '0', STR_PAD_LEFT);
    }

    public function addItem($productId, $currentQuantity, $adjustedQuantity, $unitCost = null, $notes = null)
    {
        if ($this->status !== 'draft') {
            return false;
        }

        $differenceQuantity = $adjustedQuantity - $currentQuantity;
        $totalCost = $unitCost ? abs($differenceQuantity) * $unitCost : 0;

        $item = AdjustmentItem::create([
            'company_id' => $this->company_id,
            'adjustment_id' => $this->adjustment_id,
            'product_id' => $productId,
            'current_quantity' => $currentQuantity,
            'adjusted_quantity' => $adjustedQuantity,
            'difference_quantity' => $differenceQuantity,
            'unit_cost' => $unitCost,
            'total_cost' => $totalCost,
            'notes' => $notes,
            'created_by' => auth()->id()
        ]);

        $this->calculateTotals();

        return $item;
    }

    public function removeItem($itemId)
    {
        if ($this->status !== 'draft') {
            return false;
        }

        $item = $this->items()->find($itemId);
        if ($item) {
            $item->delete();
            $this->calculateTotals();
            return true;
        }

        return false;
    }

    // دوال التقارير
    public static function getAdjustmentSummary($companyId, $startDate = null, $endDate = null, $warehouseId = null)
    {
        $query = self::where('company_id', $companyId)->where('status', 'approved');
        
        if ($startDate && $endDate) {
            $query->whereBetween('adjustment_date', [$startDate, $endDate]);
        }
        
        if ($warehouseId) {
            $query->where('warehouse_id', $warehouseId);
        }
        
        return $query->selectRaw('
            adjustment_type,
            COUNT(*) as count,
            SUM(total_items) as total_items,
            SUM(total_value) as total_value
        ')->groupBy('adjustment_type')->get();
    }
}
