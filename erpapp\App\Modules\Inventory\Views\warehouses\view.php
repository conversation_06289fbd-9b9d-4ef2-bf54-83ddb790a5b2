<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="page-title">
                    <i class="fas fa-warehouse"></i> <?= htmlspecialchars($warehouse['warehouse_name_ar']) ?>
                </h1>
                <div>
                    <?php if (canEdit('warehouses')): ?>
                    <a href="<?= base_url('inventory/warehouses/' . $warehouse['warehouse_id'] . '/edit') ?>" class="btn btn-warning me-2">
                        <i class="fas fa-edit me-1"></i> <?= __('تعديل المستودع') ?>
                    </a>
                    <?php endif; ?>
                    <a href="<?= base_url('inventory/warehouses') ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> <?= __('العودة للمستودعات') ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- معلومات المستودع الأساسية -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i> <?= __('معلومات المستودع') ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted"><?= __('كود المستودع') ?></label>
                            <div class="fw-bold">
                                <span class="badge bg-secondary fs-6"><?= htmlspecialchars($warehouse['warehouse_code']) ?></span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted"><?= __('نوع المستودع') ?></label>
                            <div class="fw-bold">
                                <?php
                                $typeLabels = [
                                    'main' => ['text' => 'مستودع رئيسي', 'class' => 'primary'],
                                    'branch' => ['text' => 'مستودع فرع', 'class' => 'info'],
                                    'virtual' => ['text' => 'مستودع افتراضي', 'class' => 'warning'],
                                    'external' => ['text' => 'مستودع خارجي', 'class' => 'secondary'],
                                    'temporary' => ['text' => 'مستودع مؤقت', 'class' => 'dark']
                                ];
                                $type = $typeLabels[$warehouse['warehouse_type']] ?? ['text' => $warehouse['warehouse_type'], 'class' => 'secondary'];
                                ?>
                                <span class="badge bg-<?= $type['class'] ?> fs-6"><?= $type['text'] ?></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted"><?= __('اسم المستودع (عربي)') ?></label>
                            <div class="fw-bold fs-5"><?= htmlspecialchars($warehouse['warehouse_name_ar']) ?></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted"><?= __('اسم المستودع (إنجليزي)') ?></label>
                            <div class="fw-bold fs-5">
                                <?= !empty($warehouse['warehouse_name_en']) ? htmlspecialchars($warehouse['warehouse_name_en']) : '<span class="text-muted">غير محدد</span>' ?>
                            </div>
                        </div>
                    </div>

                    <?php if (!empty($warehouse['address'])): ?>
                    <div class="row">
                        <div class="col-12 mb-3">
                            <label class="form-label text-muted"><?= __('العنوان') ?></label>
                            <div class="fw-bold">
                                <i class="fas fa-map-marker-alt text-danger me-2"></i>
                                <?= nl2br(htmlspecialchars($warehouse['address'])) ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <div class="row">
                        <?php if (!empty($warehouse['phone'])): ?>
                        <div class="col-md-4 mb-3">
                            <label class="form-label text-muted"><?= __('رقم الهاتف') ?></label>
                            <div class="fw-bold">
                                <i class="fas fa-phone text-success me-2"></i>
                                <a href="tel:<?= htmlspecialchars($warehouse['phone']) ?>" class="text-decoration-none">
                                    <?= htmlspecialchars($warehouse['phone']) ?>
                                </a>
                            </div>
                        </div>
                        <?php endif; ?>

                        <?php if (!empty($warehouse['email'])): ?>
                        <div class="col-md-4 mb-3">
                            <label class="form-label text-muted"><?= __('البريد الإلكتروني') ?></label>
                            <div class="fw-bold">
                                <i class="fas fa-envelope text-primary me-2"></i>
                                <a href="mailto:<?= htmlspecialchars($warehouse['email']) ?>" class="text-decoration-none">
                                    <?= htmlspecialchars($warehouse['email']) ?>
                                </a>
                            </div>
                        </div>
                        <?php endif; ?>

                        <?php if (!empty($warehouse['manager_name'])): ?>
                        <div class="col-md-4 mb-3">
                            <label class="form-label text-muted"><?= __('اسم المدير') ?></label>
                            <div class="fw-bold">
                                <i class="fas fa-user-tie text-info me-2"></i>
                                <?= htmlspecialchars($warehouse['manager_name']) ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted"><?= __('الحالة') ?></label>
                            <div>
                                <?php if ($warehouse['is_active']): ?>
                                <span class="badge bg-success fs-6">
                                    <i class="fas fa-check-circle me-1"></i> <?= __('نشط') ?>
                                </span>
                                <?php else: ?>
                                <span class="badge bg-danger fs-6">
                                    <i class="fas fa-times-circle me-1"></i> <?= __('غير نشط') ?>
                                </span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted"><?= __('تاريخ الإنشاء') ?></label>
                            <div class="fw-bold">
                                <i class="fas fa-calendar text-warning me-2"></i>
                                <?= date('Y-m-d H:i', strtotime($warehouse['created_at'])) ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات المستودع -->
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i> <?= __('إحصائيات المستودع') ?>
                    </h5>
                </div>
                <div class="card-body">
                    <!-- السعة والاستخدام -->
                    <?php if ($warehouse['capacity']): ?>
                    <div class="mb-4">
                        <label class="form-label text-muted"><?= __('السعة والاستخدام') ?></label>
                        <?php
                        $usagePercentage = $warehouse['capacity'] > 0 ? ($warehouse['current_usage'] / $warehouse['capacity']) * 100 : 0;
                        $progressClass = $usagePercentage >= 90 ? 'danger' : ($usagePercentage >= 70 ? 'warning' : 'success');
                        ?>
                        <div class="progress mb-2" style="height: 25px;">
                            <div class="progress-bar bg-<?= $progressClass ?>" role="progressbar"
                                 style="width: <?= min($usagePercentage, 100) ?>%">
                                <?= number_format($usagePercentage, 1) ?>%
                            </div>
                        </div>
                        <div class="d-flex justify-content-between">
                            <small class="text-muted"><?= __('المستخدم') ?>: <?= number_format($warehouse['current_usage'], 2) ?></small>
                            <small class="text-muted"><?= __('السعة') ?>: <?= number_format($warehouse['capacity'], 2) ?></small>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- إحصائيات سريعة -->
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="border rounded p-3">
                                <div class="text-primary mb-1">
                                    <i class="fas fa-boxes fa-2x"></i>
                                </div>
                                <h4 class="mb-0">0</h4>
                                <small class="text-muted"><?= __('المنتجات') ?></small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="border rounded p-3">
                                <div class="text-success mb-1">
                                    <i class="fas fa-layer-group fa-2x"></i>
                                </div>
                                <h4 class="mb-0">0</h4>
                                <small class="text-muted"><?= __('إجمالي الكمية') ?></small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="border rounded p-3">
                                <div class="text-warning mb-1">
                                    <i class="fas fa-dollar-sign fa-2x"></i>
                                </div>
                                <h4 class="mb-0">0</h4>
                                <small class="text-muted"><?= __('إجمالي القيمة') ?></small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="border rounded p-3">
                                <div class="text-info mb-1">
                                    <i class="fas fa-exchange-alt fa-2x"></i>
                                </div>
                                <h4 class="mb-0">0</h4>
                                <small class="text-muted"><?= __('الحركات') ?></small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- أقسام إضافية -->
    <div class="row">
        <div class="col-12">
            <!-- تبويبات -->
            <ul class="nav nav-tabs" id="warehouseTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="stock-tab" data-bs-toggle="tab" data-bs-target="#stock" type="button" role="tab">
                        <i class="fas fa-boxes me-2"></i> <?= __('المخزون') ?>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="movements-tab" data-bs-toggle="tab" data-bs-target="#movements" type="button" role="tab">
                        <i class="fas fa-exchange-alt me-2"></i> <?= __('الحركات') ?>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="locations-tab" data-bs-toggle="tab" data-bs-target="#locations" type="button" role="tab">
                        <i class="fas fa-map-marked-alt me-2"></i> <?= __('المواقع') ?>
                    </button>
                </li>
            </ul>

            <!-- محتوى التبويبات -->
            <div class="tab-content" id="warehouseTabsContent">
                <!-- تبويب المخزون -->
                <div class="tab-pane fade show active" id="stock" role="tabpanel">
                    <div class="card border-top-0">
                        <div class="card-body">
                            <div class="text-center py-5">
                                <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted"><?= __('لا يوجد مخزون') ?></h5>
                                <p class="text-muted"><?= __('لم يتم إضافة أي منتجات لهذا المستودع بعد') ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تبويب الحركات -->
                <div class="tab-pane fade" id="movements" role="tabpanel">
                    <div class="card border-top-0">
                        <div class="card-body">
                            <div class="text-center py-5">
                                <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted"><?= __('لا توجد حركات') ?></h5>
                                <p class="text-muted"><?= __('لم يتم تسجيل أي حركات مخزون لهذا المستودع بعد') ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تبويب المواقع -->
                <div class="tab-pane fade" id="locations" role="tabpanel">
                    <div class="card border-top-0">
                        <div class="card-body">
                            <div class="text-center py-5">
                                <i class="fas fa-map-marked-alt fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted"><?= __('لا توجد مواقع') ?></h5>
                                <p class="text-muted"><?= __('لم يتم تحديد مواقع تخزين داخل هذا المستودع بعد') ?></p>
                                <button class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i> <?= __('إضافة موقع جديد') ?>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.badge.fs-6 {
    font-size: 0.9rem !important;
}

.progress {
    border-radius: 10px;
}

.progress-bar {
    border-radius: 10px;
    font-weight: bold;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.nav-tabs .nav-link {
    border-bottom: 3px solid transparent;
    color: #6c757d;
    font-weight: 500;
}

.nav-tabs .nav-link.active {
    border-bottom-color: #0d6efd;
    color: #0d6efd;
    background-color: transparent;
}

.nav-tabs .nav-link:hover {
    border-bottom-color: #0d6efd;
    color: #0d6efd;
}

.border.rounded {
    transition: all 0.3s ease;
}

.border.rounded:hover {
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

a.text-decoration-none:hover {
    text-decoration: underline !important;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تفعيل Bootstrap tabs
    var triggerTabList = [].slice.call(document.querySelectorAll('#warehouseTabs button'));
    triggerTabList.forEach(function (triggerEl) {
        var tabTrigger = new bootstrap.Tab(triggerEl);

        triggerEl.addEventListener('click', function (event) {
            event.preventDefault();
            tabTrigger.show();
        });
    });
});
</script>