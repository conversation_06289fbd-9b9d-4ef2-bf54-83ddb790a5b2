/* ===== BUTTONS (بديل Bootstrap) ===== */

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2) var(--spacing-4);
    font-size: 14px;
    font-weight: 500;
    line-height: 1.5;
    text-decoration: none;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition-fast) ease;
    gap: var(--spacing-2);
    white-space: nowrap;
    user-select: none;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

/* ===== PRIMARY BUTTON ===== */
.btn-primary {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--light-color);
}

.btn-primary:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    color: var(--light-color);
    transform: translateY(-1px);
    box-shadow: var(--box-shadow);
}

.btn-outline-primary {
    background: transparent;
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: var(--light-color);
}

/* ===== SUCCESS BUTTON ===== */
.btn-success {
    background: var(--success-color);
    border-color: var(--success-color);
    color: var(--light-color);
}

.btn-success:hover {
    background: var(--success-dark);
    border-color: var(--success-dark);
    color: var(--light-color);
    transform: translateY(-1px);
}

.btn-outline-success {
    background: transparent;
    border-color: var(--success-color);
    color: var(--success-color);
}

.btn-outline-success:hover {
    background: var(--success-color);
    color: var(--light-color);
}

/* ===== WARNING BUTTON ===== */
.btn-warning {
    background: var(--warning-color);
    border-color: var(--warning-color);
    color: var(--dark-color);
}

.btn-warning:hover {
    background: var(--warning-dark);
    border-color: var(--warning-dark);
    color: var(--dark-color);
    transform: translateY(-1px);
}

.btn-outline-warning {
    background: transparent;
    border-color: var(--warning-color);
    color: var(--warning-color);
}

.btn-outline-warning:hover {
    background: var(--warning-color);
    color: var(--dark-color);
}

/* ===== DANGER BUTTON ===== */
.btn-danger {
    background: var(--danger-color);
    border-color: var(--danger-color);
    color: var(--light-color);
}

.btn-danger:hover {
    background: var(--danger-dark);
    border-color: var(--danger-dark);
    color: var(--light-color);
    transform: translateY(-1px);
}

.btn-outline-danger {
    background: transparent;
    border-color: var(--danger-color);
    color: var(--danger-color);
}

.btn-outline-danger:hover {
    background: var(--danger-color);
    color: var(--light-color);
}

/* ===== INFO BUTTON ===== */
.btn-info {
    background: var(--info-color);
    border-color: var(--info-color);
    color: var(--light-color);
}

.btn-info:hover {
    background: var(--info-dark);
    border-color: var(--info-dark);
    color: var(--light-color);
    transform: translateY(-1px);
}

.btn-outline-info {
    background: transparent;
    border-color: var(--info-color);
    color: var(--info-color);
}

.btn-outline-info:hover {
    background: var(--info-color);
    color: var(--light-color);
}

/* ===== SECONDARY BUTTON ===== */
.btn-secondary {
    background: var(--gray-600);
    border-color: var(--gray-600);
    color: var(--light-color);
}

.btn-secondary:hover {
    background: var(--gray-700);
    border-color: var(--gray-700);
    color: var(--light-color);
    transform: translateY(-1px);
}

.btn-outline-secondary {
    background: transparent;
    border-color: var(--gray-600);
    color: var(--gray-600);
}

.btn-outline-secondary:hover {
    background: var(--gray-600);
    color: var(--light-color);
}

/* ===== LIGHT BUTTON ===== */
.btn-light {
    background: var(--gray-100);
    border-color: var(--gray-100);
    color: var(--gray-800);
}

.btn-light:hover {
    background: var(--gray-200);
    border-color: var(--gray-200);
    color: var(--gray-800);
    transform: translateY(-1px);
}

.btn-outline-light {
    background: transparent;
    border-color: var(--gray-300);
    color: var(--gray-600);
}

.btn-outline-light:hover {
    background: var(--gray-100);
    color: var(--gray-800);
}

/* ===== BUTTON SIZES ===== */
.btn-sm {
    padding: var(--spacing-1) var(--spacing-3);
    font-size: 12px;
}

.btn-lg {
    padding: var(--spacing-3) var(--spacing-6);
    font-size: 16px;
}

/* ===== BUTTON GROUPS ===== */
.btn-group {
    display: inline-flex;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.btn-group .btn {
    border-radius: 0;
    border-right-width: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: var(--border-radius);
    border-bottom-left-radius: var(--border-radius);
}

.btn-group .btn:last-child {
    border-top-right-radius: var(--border-radius);
    border-bottom-right-radius: var(--border-radius);
    border-right-width: 1px;
}

/* ===== DARK THEME BUTTONS ===== */
body.dark-theme .btn-light {
    background: var(--gray-700);
    border-color: var(--gray-700);
    color: var(--dark-text-color);
}

body.dark-theme .btn-light:hover {
    background: var(--gray-600);
    border-color: var(--gray-600);
}

body.dark-theme .btn-outline-light {
    border-color: var(--gray-600);
    color: var(--gray-300);
}

body.dark-theme .btn-outline-light:hover {
    background: var(--gray-700);
    color: var(--dark-text-color);
}

body.dark-theme .btn-secondary {
    background: var(--gray-600);
    border-color: var(--gray-600);
}

body.dark-theme .btn-secondary:hover {
    background: var(--gray-500);
    border-color: var(--gray-500);
}

/* ===== FLOATING ACTION BUTTON ===== */
.btn-fab {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    padding: 0;
    box-shadow: var(--box-shadow-lg);
    position: fixed;
    bottom: var(--spacing-6);
    right: var(--spacing-6);
    z-index: 1000;
}

[dir="rtl"] .btn-fab {
    right: auto;
    left: var(--spacing-6);
}

.btn-fab:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-xl);
}

/* ===== ICON BUTTONS ===== */
.btn-icon {
    width: 40px;
    height: 40px;
    padding: 0;
    border-radius: var(--border-radius);
}

.btn-icon-sm {
    width: 32px;
    height: 32px;
    padding: 0;
    border-radius: var(--border-radius-sm);
}

/* ===== LOADING STATE ===== */
.btn-loading {
    position: relative;
    pointer-events: none;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
