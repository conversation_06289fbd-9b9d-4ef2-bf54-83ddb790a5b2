/**
 * مكون النماذج - تحسينات وتفاعلات النماذج
 * Forms Component - Form enhancements and interactions
 */

(function() {
    'use strict';

    // انتظار تحميل DOM
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initForms);
    } else {
        initForms();
    }

    function initForms() {
        console.log('🚀 بدء تهيئة مكون النماذج...');

        initFormValidation();
        initFormSubmission();
        initFormFields();
        initFormTabs();

        console.log('✅ تم تهيئة مكون النماذج بنجاح');
    }

    // ===== التحقق من صحة النماذج ===== //
    function initFormValidation() {
        const forms = document.querySelectorAll('form[data-validate="true"]');
        
        forms.forEach(function(form) {
            form.addEventListener('submit', function(e) {
                if (!validateForm(form)) {
                    e.preventDefault();
                    return false;
                }
            });

            // التحقق الفوري عند الكتابة
            const inputs = form.querySelectorAll('input, select, textarea');
            inputs.forEach(function(input) {
                input.addEventListener('blur', function() {
                    validateField(input);
                });
            });
        });
    }

    // ===== إرسال النماذج ===== //
    function initFormSubmission() {
        const ajaxForms = document.querySelectorAll('form[data-ajax="true"]');
        
        ajaxForms.forEach(function(form) {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                submitFormAjax(form);
            });
        });
    }

    // ===== تحسينات الحقول ===== //
    function initFormFields() {
        // تحسين حقول الأرقام
        const numberInputs = document.querySelectorAll('input[type="number"], input[data-type="currency"]');
        numberInputs.forEach(function(input) {
            input.addEventListener('input', function() {
                formatNumberInput(input);
            });
        });

        // تحسين حقول التاريخ
        const dateInputs = document.querySelectorAll('input[type="date"]');
        dateInputs.forEach(function(input) {
            input.addEventListener('change', function() {
                formatDateInput(input);
            });
        });

        // تحسين حقول البحث
        const searchInputs = document.querySelectorAll('input[data-search="true"]');
        searchInputs.forEach(function(input) {
            let timeout;
            input.addEventListener('input', function() {
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    performSearch(input);
                }, 300);
            });
        });
    }

    // ===== تبويبات النماذج ===== //
    function initFormTabs() {
        const tabContainers = document.querySelectorAll('.form-tabs');
        
        tabContainers.forEach(function(container) {
            const tabs = container.querySelectorAll('.tab-button');
            const contents = container.querySelectorAll('.tab-content');

            tabs.forEach(function(tab, index) {
                tab.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // إزالة الحالة النشطة من جميع التبويبات
                    tabs.forEach(t => t.classList.remove('active'));
                    contents.forEach(c => c.classList.remove('active'));
                    
                    // تفعيل التبويب المحدد
                    tab.classList.add('active');
                    if (contents[index]) {
                        contents[index].classList.add('active');
                    }
                });
            });
        });
    }

    // ===== وظائف مساعدة ===== //

    function validateForm(form) {
        let isValid = true;
        const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
        
        inputs.forEach(function(input) {
            if (!validateField(input)) {
                isValid = false;
            }
        });

        return isValid;
    }

    function validateField(field) {
        const value = field.value.trim();
        const type = field.type;
        const required = field.hasAttribute('required');
        
        // إزالة رسائل الخطأ السابقة
        clearFieldError(field);

        // التحقق من الحقول المطلوبة
        if (required && !value) {
            showFieldError(field, 'هذا الحقل مطلوب');
            return false;
        }

        // التحقق من البريد الإلكتروني
        if (type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                showFieldError(field, 'البريد الإلكتروني غير صحيح');
                return false;
            }
        }

        // التحقق من رقم الهاتف
        if (field.dataset.type === 'phone' && value) {
            const phoneRegex = /^[0-9+\-\s()]+$/;
            if (!phoneRegex.test(value)) {
                showFieldError(field, 'رقم الهاتف غير صحيح');
                return false;
            }
        }

        // التحقق من الحد الأدنى للطول
        const minLength = field.getAttribute('minlength');
        if (minLength && value.length < parseInt(minLength)) {
            showFieldError(field, `الحد الأدنى ${minLength} أحرف`);
            return false;
        }

        return true;
    }

    function showFieldError(field, message) {
        field.classList.add('is-invalid');
        
        let errorDiv = field.parentNode.querySelector('.invalid-feedback');
        if (!errorDiv) {
            errorDiv = document.createElement('div');
            errorDiv.className = 'invalid-feedback';
            field.parentNode.appendChild(errorDiv);
        }
        
        errorDiv.textContent = message;
    }

    function clearFieldError(field) {
        field.classList.remove('is-invalid');
        const errorDiv = field.parentNode.querySelector('.invalid-feedback');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    function submitFormAjax(form) {
        const formData = new FormData(form);
        const url = form.action || window.location.href;
        const method = form.method || 'POST';

        // إظهار مؤشر التحميل
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

        fetch(url, {
            method: method,
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(data.message || 'تم الحفظ بنجاح');
                
                // إعادة توجيه إذا كان مطلوباً
                if (data.redirect) {
                    setTimeout(() => {
                        window.location.href = data.redirect;
                    }, 1000);
                }
            } else {
                showError(data.message || 'حدث خطأ أثناء الحفظ');
                
                // إظهار أخطاء الحقول
                if (data.errors) {
                    Object.keys(data.errors).forEach(fieldName => {
                        const field = form.querySelector(`[name="${fieldName}"]`);
                        if (field) {
                            showFieldError(field, data.errors[fieldName]);
                        }
                    });
                }
            }
        })
        .catch(error => {
            console.error('Form submission error:', error);
            showError('حدث خطأ في الاتصال');
        })
        .finally(() => {
            // إعادة تفعيل الزر
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        });
    }

    function formatNumberInput(input) {
        let value = input.value.replace(/[^\d.-]/g, '');
        
        if (input.dataset.type === 'currency') {
            const number = parseFloat(value);
            if (!isNaN(number)) {
                input.value = formatNumber(number);
            }
        }
    }

    function formatDateInput(input) {
        const date = new Date(input.value);
        if (!isNaN(date.getTime())) {
            // يمكن إضافة تنسيق مخصص للتاريخ هنا
        }
    }

    function performSearch(input) {
        const query = input.value.trim();
        const target = input.dataset.target;
        
        if (query.length < 2) return;
        
        // تنفيذ البحث حسب الهدف المحدد
        if (target) {
            const targetElement = document.querySelector(target);
            if (targetElement) {
                // تنفيذ البحث في العنصر المحدد
                searchInElement(targetElement, query);
            }
        }
    }

    function searchInElement(element, query) {
        const items = element.querySelectorAll('[data-searchable]');
        
        items.forEach(function(item) {
            const text = item.textContent.toLowerCase();
            const searchQuery = query.toLowerCase();
            
            if (text.includes(searchQuery)) {
                item.style.display = '';
            } else {
                item.style.display = 'none';
            }
        });
    }

    // تصدير الوظائف للاستخدام الخارجي
    window.FormComponent = {
        validateForm: validateForm,
        validateField: validateField,
        submitFormAjax: submitFormAjax
    };

})();
