<?php
/**
 * Template موحد للفلاتر
 */
?>

<div class="modal fade" id="filtersModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-light">
                <h5 class="modal-title">
                    <i class="mdi mdi-filter-variant me-2"></i>
                    فلاتر البحث المتقدم
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            
            <form id="filtersForm" onsubmit="return DataTable.applyFilters(event)">
                <div class="modal-body">
                    <div class="row g-3">
                        
                        <?php foreach ($filters_config as $filter): ?>
                            <div class="col-md-<?= $filter['col_size'] ?? 6 ?>">
                                <label for="<?= $filter['name'] ?>" class="form-label">
                                    <?php if (isset($filter['icon'])): ?>
                                        <i class="<?= $filter['icon'] ?> me-1"></i>
                                    <?php endif; ?>
                                    <?= $filter['label'] ?>
                                </label>
                                
                                <?php
                                $current_value = $filters[$filter['name']] ?? '';
                                
                                switch ($filter['type']):
                                    case 'text':
                                    case 'search':
                                        ?>
                                        <input type="text" 
                                               class="form-control" 
                                               id="<?= $filter['name'] ?>" 
                                               name="<?= $filter['name'] ?>"
                                               placeholder="<?= $filter['placeholder'] ?? '' ?>"
                                               value="<?= htmlspecialchars($current_value) ?>">
                                        <?php
                                        break;
                                        
                                    case 'select':
                                        ?>
                                        <select class="form-select" 
                                                id="<?= $filter['name'] ?>" 
                                                name="<?= $filter['name'] ?>">
                                            <option value=""><?= $filter['placeholder'] ?? 'اختر...' ?></option>
                                            <?php foreach ($filter['options'] as $value => $text): ?>
                                                <option value="<?= $value ?>" 
                                                        <?= $current_value == $value ? 'selected' : '' ?>>
                                                    <?= $text ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <?php
                                        break;
                                        
                                    case 'date':
                                        ?>
                                        <input type="date" 
                                               class="form-control" 
                                               id="<?= $filter['name'] ?>" 
                                               name="<?= $filter['name'] ?>"
                                               value="<?= $current_value ?>">
                                        <?php
                                        break;
                                        
                                    case 'daterange':
                                        ?>
                                        <div class="row">
                                            <div class="col-6">
                                                <input type="date" 
                                                       class="form-control" 
                                                       id="<?= $filter['name'] ?>_from" 
                                                       name="<?= $filter['name'] ?>_from"
                                                       placeholder="من"
                                                       value="<?= $filters[$filter['name'] . '_from'] ?? '' ?>">
                                            </div>
                                            <div class="col-6">
                                                <input type="date" 
                                                       class="form-control" 
                                                       id="<?= $filter['name'] ?>_to" 
                                                       name="<?= $filter['name'] ?>_to"
                                                       placeholder="إلى"
                                                       value="<?= $filters[$filter['name'] . '_to'] ?? '' ?>">
                                            </div>
                                        </div>
                                        <?php
                                        break;
                                        
                                    case 'number':
                                        ?>
                                        <input type="number" 
                                               class="form-control" 
                                               id="<?= $filter['name'] ?>" 
                                               name="<?= $filter['name'] ?>"
                                               placeholder="<?= $filter['placeholder'] ?? '' ?>"
                                               value="<?= $current_value ?>"
                                               <?= isset($filter['min']) ? 'min="' . $filter['min'] . '"' : '' ?>
                                               <?= isset($filter['max']) ? 'max="' . $filter['max'] . '"' : '' ?>>
                                        <?php
                                        break;
                                        
                                    case 'checkbox':
                                        ?>
                                        <div class="form-check">
                                            <input class="form-check-input" 
                                                   type="checkbox" 
                                                   id="<?= $filter['name'] ?>" 
                                                   name="<?= $filter['name'] ?>"
                                                   value="1"
                                                   <?= $current_value ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="<?= $filter['name'] ?>">
                                                <?= $filter['checkbox_label'] ?? $filter['label'] ?>
                                            </label>
                                        </div>
                                        <?php
                                        break;
                                        
                                    case 'radio':
                                        ?>
                                        <?php foreach ($filter['options'] as $value => $text): ?>
                                            <div class="form-check">
                                                <input class="form-check-input" 
                                                       type="radio" 
                                                       id="<?= $filter['name'] ?>_<?= $value ?>" 
                                                       name="<?= $filter['name'] ?>"
                                                       value="<?= $value ?>"
                                                       <?= $current_value == $value ? 'checked' : '' ?>>
                                                <label class="form-check-label" for="<?= $filter['name'] ?>_<?= $value ?>">
                                                    <?= $text ?>
                                                </label>
                                            </div>
                                        <?php endforeach; ?>
                                        <?php
                                        break;
                                        
                                endswitch;
                                ?>
                                
                                <?php if (isset($filter['help_text'])): ?>
                                    <div class="form-text"><?= $filter['help_text'] ?></div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                        
                    </div>
                </div>
                
                <div class="modal-footer bg-light">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        إلغاء
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="DataTable.clearFilters()">
                        <i class="mdi mdi-filter-remove me-1"></i>
                        مسح الفلاتر
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="mdi mdi-filter-check me-1"></i>
                        تطبيق الفلاتر
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
