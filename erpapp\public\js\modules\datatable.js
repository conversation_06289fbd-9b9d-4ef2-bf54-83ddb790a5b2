/**
 * JavaScript موحد لجميع الجداول في النظام
 */

class DataTable {
    constructor() {
        this.config = {};
        this.currentFilters = {};
        this.isLoading = false;
    }

    /**
     * تهيئة الجدول
     */
    static init(config) {
        window.dataTableInstance = new DataTable();
        window.dataTableInstance.config = config;
        window.dataTableInstance.currentFilters = config.filters || {};
        window.dataTableInstance.bindEvents();
        window.dataTableInstance.initializeUI();
    }

    /**
     * ربط الأحداث
     */
    bindEvents() {
        // تنظيف URL من معاملات
        this.cleanURL();
        
        // ربط أحداث الفلاتر
        this.bindFilterEvents();
        
        // ربط أحداث الحذف
        this.bindDeleteEvents();
        
        // ربط أحداث التحديث التلقائي
        this.bindAutoRefresh();
    }

    /**
     * تهيئة واجهة المستخدم
     */
    initializeUI() {
        // تحديث مظهر زر الفلاتر
        this.updateFilterButton();
        
        // إضافة تأثيرات الحركة
        this.addAnimations();
        
        // تحسين تجربة المستخدم
        this.enhanceUX();
    }

    /**
     * تنظيف URL من المعاملات
     */
    cleanURL() {
        if (window.location.search) {
            window.history.replaceState({}, '', window.location.pathname);
        }
    }

    /**
     * الانتقال لصفحة معينة
     */
    static goToPage(page) {
        const instance = window.dataTableInstance;
        if (instance.isLoading) return;

        const newFilters = {...instance.currentFilters, current_page: page};
        instance.applyFiltersInternal(newFilters);
    }

    /**
     * تغيير عدد العناصر لكل صفحة
     */
    static changePerPage(perPage) {
        const instance = window.dataTableInstance;
        if (instance.isLoading) return;

        const newFilters = {...instance.currentFilters, per_page: perPage, current_page: 1};
        instance.applyFiltersInternal(newFilters);
    }

    /**
     * تطبيق الفلاتر من النموذج
     */
    static applyFilters(event) {
        event.preventDefault();
        
        const instance = window.dataTableInstance;
        if (instance.isLoading) return false;

        const formData = new FormData(document.getElementById('filtersForm'));
        const filters = Object.fromEntries(formData.entries());
        
        // إضافة current_page = 1 عند تطبيق فلاتر جديدة
        filters.current_page = 1;
        filters.per_page = instance.currentFilters.per_page || 20;

        instance.applyFiltersInternal(filters);
        
        // إغلاق النافذة المنبثقة
        const modal = bootstrap.Modal.getInstance(document.getElementById('filtersModal'));
        if (modal) {
            modal.hide();
        }
        
        return false;
    }

    /**
     * مسح الفلاتر
     */
    static clearFilters() {
        const instance = window.dataTableInstance;
        if (instance.isLoading) return;

        // مسح النموذج
        const form = document.getElementById('filtersForm');
        if (form) {
            form.reset();
        }

        // تطبيق فلاتر فارغة
        const defaultFilters = {
            search: '',
            per_page: instance.currentFilters.per_page || 20,
            current_page: 1
        };

        instance.applyFiltersInternal(defaultFilters);
    }

    /**
     * تطبيق الفلاتر داخلياً
     */
    applyFiltersInternal(filters) {
        this.showLoading();
        this.currentFilters = filters;

        // إرسال الطلب
        fetch(this.getApplyFiltersURL(), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: this.buildFormData(filters)
        })
        .then(response => {
            if (response.ok) {
                // إعادة تحميل الصفحة للحصول على البيانات المحدثة
                window.location.reload();
            } else {
                throw new Error('Network response was not ok');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            this.hideLoading();
            this.showError('حدث خطأ أثناء تطبيق الفلاتر');
        });
    }

    /**
     * بناء بيانات النموذج
     */
    buildFormData(filters) {
        const params = new URLSearchParams();
        for (const [key, value] of Object.entries(filters)) {
            params.append(key, value);
        }
        return params;
    }

    /**
     * الحصول على رابط تطبيق الفلاتر
     */
    getApplyFiltersURL() {
        const baseUrl = window.location.origin + window.location.pathname.replace(/\/+$/, '');
        return baseUrl.replace(/\/[^\/]*$/, '') + '/apply-filters';
    }

    /**
     * إظهار مؤشر التحميل
     */
    showLoading() {
        this.isLoading = true;
        const tableContainer = document.querySelector('.table-responsive');
        if (tableContainer) {
            tableContainer.classList.add('table-loading');
        }

        // إضافة spinner
        if (!document.querySelector('.loading-spinner')) {
            const spinner = document.createElement('div');
            spinner.className = 'loading-spinner';
            spinner.innerHTML = '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">جاري التحميل...</span></div>';
            tableContainer.parentNode.insertBefore(spinner, tableContainer.nextSibling);
        }
    }

    /**
     * إخفاء مؤشر التحميل
     */
    hideLoading() {
        this.isLoading = false;
        const tableContainer = document.querySelector('.table-responsive');
        if (tableContainer) {
            tableContainer.classList.remove('table-loading');
        }

        const spinner = document.querySelector('.loading-spinner');
        if (spinner) {
            spinner.remove();
        }
    }

    /**
     * عرض رسالة خطأ
     */
    showError(message) {
        // يمكن تحسين هذا باستخدام toast notifications
        alert(message);
    }

    /**
     * تحديث مظهر زر الفلاتر
     */
    updateFilterButton() {
        const filterButton = document.querySelector('[data-bs-target="#filtersModal"]');
        if (!filterButton) return;

        const badge = filterButton.querySelector('.badge');
        const activeFiltersCount = this.countActiveFilters();

        if (activeFiltersCount > 0) {
            if (badge) {
                badge.textContent = activeFiltersCount;
            }
            filterButton.classList.add('btn-primary');
            filterButton.classList.remove('btn-outline-primary');
        } else {
            if (badge) {
                badge.remove();
            }
            filterButton.classList.remove('btn-primary');
            filterButton.classList.add('btn-outline-primary');
        }
    }

    /**
     * عد الفلاتر النشطة
     */
    countActiveFilters() {
        let count = 0;
        const excludeKeys = ['per_page'];

        for (const [key, value] of Object.entries(this.currentFilters)) {
            if (excludeKeys.includes(key)) continue;
            if (key === 'current_page' && value <= 1) continue;
            if (value && value !== '' && value !== '0') {
                count++;
            }
        }

        return count;
    }

    /**
     * ربط أحداث الفلاتر
     */
    bindFilterEvents() {
        // فتح نافذة الفلاتر
        const filtersModal = document.getElementById('filtersModal');
        if (filtersModal) {
            filtersModal.addEventListener('shown.bs.modal', function() {
                const searchInput = document.getElementById('search');
                if (searchInput) {
                    searchInput.focus();
                }
            });
        }
    }

    /**
     * ربط أحداث الحذف
     */
    bindDeleteEvents() {
        // تأكيد الحذف
        window.confirmDelete = (id) => {
            const deleteForm = document.getElementById('deleteForm');
            if (deleteForm) {
                const baseUrl = window.location.pathname.replace(/\/+$/, '');
                deleteForm.action = baseUrl + '/' + id + '/delete';
                
                const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
                modal.show();
            }
        };
    }

    /**
     * ربط التحديث التلقائي
     */
    bindAutoRefresh() {
        // يمكن إضافة تحديث تلقائي كل فترة معينة إذا لزم الأمر
        // setInterval(() => {
        //     this.refreshData();
        // }, 30000); // كل 30 ثانية
    }

    /**
     * إضافة تأثيرات الحركة
     */
    addAnimations() {
        // إضافة تأثيرات للصفوف
        const rows = document.querySelectorAll('.table tbody tr');
        rows.forEach((row, index) => {
            row.style.animationDelay = (index * 0.05) + 's';
            row.classList.add('fade-in');
        });
    }

    /**
     * تحسين تجربة المستخدم
     */
    enhanceUX() {
        // إضافة تأثيرات hover للأزرار
        const buttons = document.querySelectorAll('.btn-sm');
        buttons.forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-1px)';
                this.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
            });

            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'none';
            });
        });

        // تحسين تجربة الجدول على الأجهزة المحمولة
        this.enhanceMobileExperience();
    }

    /**
     * تحسين التجربة على الأجهزة المحمولة
     */
    enhanceMobileExperience() {
        if (window.innerWidth <= 768) {
            // إضافة تحسينات للأجهزة المحمولة
            const table = document.querySelector('.table');
            if (table) {
                table.classList.add('table-sm');
            }
        }
    }
}

// تصدير الكلاس للاستخدام العام
window.DataTable = DataTable;
