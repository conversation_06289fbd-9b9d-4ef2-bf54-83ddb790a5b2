<?php
$title = 'إضافة مجموعة موردين جديدة';
$breadcrumb = [
    ['title' => 'الرئيسية', 'url' => '/'],
    ['title' => 'المشتريات', 'url' => '/purchases'],
    ['title' => 'مجموعات الموردين', 'url' => '/purchases/supplier-groups'],
    ['title' => 'إضافة جديدة', 'url' => '']
];
?>

<div class="container-fluid">
    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <?php foreach ($breadcrumb as $item): ?>
                            <?php if ($item['url']): ?>
                                <li class="breadcrumb-item"><a href="<?= base_url($item['url']) ?>"><?= $item['title'] ?></a></li>
                            <?php else: ?>
                                <li class="breadcrumb-item active"><?= $item['title'] ?></li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </ol>
                </div>
                <h4 class="page-title"><?= $title ?></h4>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">معلومات المجموعة</h4>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('purchases/supplier-groups/store') ?>" method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name_ar" class="form-label">اسم المجموعة بالعربية <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name_ar" name="name_ar" 
                                           value="<?= $_POST['name_ar'] ?? '' ?>" required>
                                    <div class="form-text">مثال: موردين الإلكترونيات، موردين المواد الغذائية</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name_en" class="form-label">اسم المجموعة بالإنجليزية</label>
                                    <input type="text" class="form-control" id="name_en" name="name_en" 
                                           value="<?= $_POST['name_en'] ?? '' ?>">
                                    <div class="form-text">اختياري - مثال: Electronics Suppliers</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="text-end">
                                    <a href="<?= base_url('purchases/supplier-groups') ?>" class="btn btn-light me-2">إلغاء</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="mdi mdi-content-save me-1"></i> حفظ المجموعة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Focus on first input
document.getElementById('name_ar').focus();

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const nameAr = document.getElementById('name_ar').value.trim();
    
    if (!nameAr) {
        e.preventDefault();
        alert('يرجى إدخال اسم المجموعة بالعربية');
        document.getElementById('name_ar').focus();
        return false;
    }
});
</script>
