<?php

namespace App\Modules\Inventory\Models;

use App\Core\Model;

class Stock extends Model
{
    protected $table = 'inventory_stock';
    protected $primaryKey = 'stock_id';
    
    protected $fillable = [
        'company_id',
        'module_code',
        'product_id',
        'warehouse_id',
        'location_id',
        'quantity_on_hand',
        'quantity_reserved',
        'quantity_available',
        'average_cost',
        'last_cost',
        'last_movement_date',
        'expiry_date',
        'batch_number',
        'serial_numbers',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'quantity_on_hand' => 'decimal:3',
        'quantity_reserved' => 'decimal:3',
        'quantity_available' => 'decimal:3',
        'average_cost' => 'decimal:2',
        'last_cost' => 'decimal:2',
        'serial_numbers' => 'array'
    ];

    protected $dates = [
        'last_movement_date',
        'expiry_date',
        'created_at',
        'updated_at'
    ];

    // العلاقات
    public function company()
    {
        return $this->belongsTo('App\Models\Company', 'company_id', 'CompanyID');
    }

    public function product()
    {
        return $this->belongsTo('App\Modules\Inventory\Models\Product', 'product_id', 'product_id');
    }

    public function warehouse()
    {
        return $this->belongsTo('App\Modules\Inventory\Models\Warehouse', 'warehouse_id', 'warehouse_id');
    }

    public function location()
    {
        return $this->belongsTo('App\Modules\Inventory\Models\Location', 'location_id', 'location_id');
    }

    public function createdBy()
    {
        return $this->belongsTo('App\Models\User', 'created_by', 'UserID');
    }

    public function updatedBy()
    {
        return $this->belongsTo('App\Models\User', 'updated_by', 'UserID');
    }

    public function movements()
    {
        return $this->hasMany('App\Modules\Inventory\Models\Movement', 'product_id', 'product_id')
                    ->where('warehouse_id', $this->warehouse_id);
    }

    // الدوال المساعدة
    public function updateAvailableQuantity()
    {
        $this->quantity_available = $this->quantity_on_hand - $this->quantity_reserved;
        $this->save();
    }

    public function reserveQuantity($quantity)
    {
        if ($this->quantity_available >= $quantity) {
            $this->quantity_reserved += $quantity;
            $this->updateAvailableQuantity();
            return true;
        }
        return false;
    }

    public function releaseReservedQuantity($quantity)
    {
        $releaseAmount = min($quantity, $this->quantity_reserved);
        $this->quantity_reserved -= $releaseAmount;
        $this->updateAvailableQuantity();
        return $releaseAmount;
    }

    public function addStock($quantity, $cost = null)
    {
        $oldQuantity = $this->quantity_on_hand;
        $oldValue = $oldQuantity * $this->average_cost;
        
        $this->quantity_on_hand += $quantity;
        
        if ($cost !== null) {
            $newValue = $quantity * $cost;
            $totalValue = $oldValue + $newValue;
            $this->average_cost = $this->quantity_on_hand > 0 ? $totalValue / $this->quantity_on_hand : 0;
            $this->last_cost = $cost;
        }
        
        $this->updateAvailableQuantity();
        $this->last_movement_date = now();
        $this->save();
    }

    public function removeStock($quantity)
    {
        if ($this->quantity_available >= $quantity) {
            $this->quantity_on_hand -= $quantity;
            $this->updateAvailableQuantity();
            $this->last_movement_date = now();
            $this->save();
            return true;
        }
        return false;
    }

    public function isLowStock()
    {
        return $this->quantity_on_hand <= $this->product->reorder_point;
    }

    public function isOutOfStock()
    {
        return $this->quantity_on_hand <= 0;
    }

    public function isExpiringSoon($days = 30)
    {
        if (!$this->expiry_date) {
            return false;
        }
        return $this->expiry_date->diffInDays(now()) <= $days;
    }

    public function isExpired()
    {
        if (!$this->expiry_date) {
            return false;
        }
        return $this->expiry_date->isPast();
    }

    public function getStockValue()
    {
        return $this->quantity_on_hand * $this->average_cost;
    }

    public function getStockStatus()
    {
        if ($this->isOutOfStock()) {
            return 'out_of_stock';
        } elseif ($this->isLowStock()) {
            return 'low_stock';
        } elseif ($this->isExpired()) {
            return 'expired';
        } elseif ($this->isExpiringSoon()) {
            return 'expiring_soon';
        } else {
            return 'normal';
        }
    }

    public function getStockStatusText()
    {
        $status = $this->getStockStatus();
        $statusTexts = [
            'out_of_stock' => 'نفد المخزون',
            'low_stock' => 'مخزون منخفض',
            'expired' => 'منتهي الصلاحية',
            'expiring_soon' => 'ينتهي قريباً',
            'normal' => 'طبيعي'
        ];
        
        return $statusTexts[$status] ?? 'غير محدد';
    }

    public function getStockStatusColor()
    {
        $status = $this->getStockStatus();
        $colors = [
            'out_of_stock' => 'danger',
            'low_stock' => 'warning',
            'expired' => 'danger',
            'expiring_soon' => 'warning',
            'normal' => 'success'
        ];
        
        return $colors[$status] ?? 'secondary';
    }

    // النطاقات (Scopes)
    public function scopeByCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    public function scopeByWarehouse($query, $warehouseId)
    {
        return $query->where('warehouse_id', $warehouseId);
    }

    public function scopeByProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    public function scopeByLocation($query, $locationId)
    {
        return $query->where('location_id', $locationId);
    }

    public function scopeLowStock($query)
    {
        return $query->whereRaw('quantity_on_hand <= (SELECT reorder_point FROM inventory_products WHERE inventory_products.product_id = inventory_stock.product_id)');
    }

    public function scopeOutOfStock($query)
    {
        return $query->where('quantity_on_hand', '<=', 0);
    }

    public function scopeExpiringSoon($query, $days = 30)
    {
        return $query->whereNotNull('expiry_date')
                    ->where('expiry_date', '<=', now()->addDays($days));
    }

    public function scopeExpired($query)
    {
        return $query->whereNotNull('expiry_date')
                    ->where('expiry_date', '<', now());
    }

    public function scopeWithBatch($query, $batchNumber)
    {
        return $query->where('batch_number', $batchNumber);
    }

    public function scopeSearch($query, $search)
    {
        return $query->whereHas('product', function($q) use ($search) {
            $q->where('product_code', 'LIKE', "%{$search}%")
              ->orWhere('product_name_ar', 'LIKE', "%{$search}%")
              ->orWhere('product_name_en', 'LIKE', "%{$search}%")
              ->orWhere('barcode', 'LIKE', "%{$search}%");
        })->orWhere('batch_number', 'LIKE', "%{$search}%");
    }

    // الأحداث
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->company_id = session('company_id');
            $model->module_code = 'inventory';
            $model->created_by = auth()->id();
            
            // حساب الكمية المتاحة
            $model->quantity_available = $model->quantity_on_hand - $model->quantity_reserved;
        });

        static::updating(function ($model) {
            $model->updated_by = auth()->id();
            
            // إعادة حساب الكمية المتاحة
            $model->quantity_available = $model->quantity_on_hand - $model->quantity_reserved;
        });
    }

    // دوال إضافية للتقارير
    public static function getTotalStockValue($companyId = null, $warehouseId = null)
    {
        $query = self::query();
        
        if ($companyId) {
            $query->where('company_id', $companyId);
        }
        
        if ($warehouseId) {
            $query->where('warehouse_id', $warehouseId);
        }
        
        return $query->sum(\DB::raw('quantity_on_hand * average_cost'));
    }

    public static function getLowStockCount($companyId = null, $warehouseId = null)
    {
        $query = self::lowStock();
        
        if ($companyId) {
            $query->where('company_id', $companyId);
        }
        
        if ($warehouseId) {
            $query->where('warehouse_id', $warehouseId);
        }
        
        return $query->count();
    }

    public static function getOutOfStockCount($companyId = null, $warehouseId = null)
    {
        $query = self::outOfStock();
        
        if ($companyId) {
            $query->where('company_id', $companyId);
        }
        
        if ($warehouseId) {
            $query->where('warehouse_id', $warehouseId);
        }
        
        return $query->count();
    }
}
