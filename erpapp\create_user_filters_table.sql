-- إنشاء جدول فلاتر المستخدمين المحفوظة
CREATE TABLE user_filters (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,                       -- المستخدم
    company_id INT NOT NULL,                    -- الشركة
    
    -- معلومات الفلتر
    page_name VARCHAR(100) NOT NULL,            -- اسم الصفحة (suppliers, customers, products, etc.)
    filter_name VARCHAR(100),                   -- اسم الفلتر (اختياري - للفلاتر المحفوظة)
    
    -- بيانات الفلتر (JSON)
    filter_data JSON NOT NULL,                  -- بيانات الفلتر بصيغة JSON
    
    -- إعدادات الفلتر
    is_default BOOLEAN DEFAULT FALSE,           -- هل هو الفلتر الافتراضي للصفحة
    is_auto_apply BOOLEAN DEFAULT TRUE,         -- هل يطبق تلقائياً عند دخول الصفحة
    
    -- معلومات إضافية
    description TEXT,                           -- وصف الفلتر
    usage_count INT DEFAULT 0,                  -- عدد مرات الاستخدام
    last_used_at DATETIME,                      -- آخر استخدام
    
    -- النظام
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- فهارس للبحث السريع
    UNIQUE KEY unique_user_page_default (user_id, company_id, page_name, is_default),
    INDEX idx_user_company (user_id, company_id),
    INDEX idx_page_name (page_name),
    INDEX idx_last_used (last_used_at),
    INDEX idx_usage_count (usage_count)
);

-- مثال على البيانات المحفوظة:
-- INSERT INTO user_filters (user_id, company_id, page_name, filter_data, is_default, is_auto_apply, usage_count, last_used_at) 
-- VALUES (1, 1, 'suppliers', '{"search":"أحمد","status":"active","group_id":"2","per_page":"20"}', TRUE, TRUE, 5, NOW());

-- مثال على فلتر مسمى محفوظ:
-- INSERT INTO user_filters (user_id, company_id, page_name, filter_name, filter_data, description, is_default, is_auto_apply, usage_count, last_used_at) 
-- VALUES (1, 1, 'suppliers', 'الموردين النشطين', '{"status":"active","per_page":"50"}', 'عرض جميع الموردين النشطين فقط', FALSE, FALSE, 3, NOW());
