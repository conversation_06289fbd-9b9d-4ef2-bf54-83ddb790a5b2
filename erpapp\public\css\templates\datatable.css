/**
 * CSS موحد لـ DataTable Templates
 * متوافق مع النظام الحالي
 */

/* ===== Page Title Box ===== */
.page-title-box {
    margin-bottom: 1.5rem;
    padding: 1rem 0;
    border-bottom: 1px solid #f1f3f4;
}

.page-title-box h4 {
    color: #495057;
    font-weight: 600;
    margin: 0;
}

.page-title-right {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

/* ===== Table Styles ===== */
.table-responsive {
    border-radius: 0.375rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 0;
}

.table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    padding: 12px 15px;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    white-space: nowrap;
}

.table td {
    vertical-align: middle;
    padding: 12px 15px;
    border-bottom: 1px solid #f1f3f4;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transition: background-color 0.2s ease;
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* ===== Empty State ===== */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state .mdi {
    font-size: 4rem;
    opacity: 0.5;
    margin-bottom: 1rem;
    display: block;
}

.empty-state p {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
}

/* ===== Action Buttons ===== */
.btn-group .btn {
    border-radius: 0.25rem;
    margin-right: 2px;
    transition: all 0.2s ease-in-out;
}

.btn-group .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
}

/* ===== Badge Styles ===== */
.badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
    border-radius: 0.375rem;
    font-weight: 500;
}

.badge.bg-success {
    background-color: #28a745 !important;
}

.badge.bg-danger {
    background-color: #dc3545 !important;
}

.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #212529 !important;
}

.badge.bg-info {
    background-color: #17a2b8 !important;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
}

/* ===== Toolbar Styles ===== */
.card-body .row.mb-2 {
    align-items: center;
    margin-bottom: 1.5rem !important;
}

.form-select-sm {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    min-width: 120px;
}

/* ===== Filter Button ===== */
.btn-outline-primary {
    border-color: #727cf5;
    color: #727cf5;
}

.btn-outline-primary:hover,
.btn-outline-primary.active,
.btn-primary {
    background-color: #727cf5;
    border-color: #727cf5;
    color: white;
}

.btn .badge {
    margin-left: 0.5rem;
}

/* ===== Loading States ===== */
.table-loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading-spinner {
    text-align: center;
    padding: 20px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
}

.spinner-border {
    width: 2rem;
    height: 2rem;
}

/* ===== Pagination Styles ===== */
.pagination {
    margin: 0;
    justify-content: center;
}

.pagination .page-link {
    color: #727cf5;
    border-color: #dee2e6;
    padding: 0.5rem 0.75rem;
}

.pagination .page-item.active .page-link {
    background-color: #727cf5;
    border-color: #727cf5;
}

.pagination .page-link:hover {
    color: #5a6acf;
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

.dataTables_info {
    color: #6c757d;
    font-size: 0.875rem;
    padding: 0.5rem 0;
}

/* ===== Card Enhancements ===== */
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    border-radius: 0.5rem;
}

.card-body {
    padding: 1.5rem;
}

/* ===== Link Styles ===== */
.table a.text-body {
    color: #495057 !important;
    text-decoration: none;
    font-weight: 500;
}

.table a.text-body:hover {
    color: #727cf5 !important;
    text-decoration: underline;
}

/* ===== Text Utilities ===== */
.text-muted {
    color: #6c757d !important;
}

.fw-bold {
    font-weight: 600 !important;
}

/* ===== Responsive Adjustments ===== */
@media (max-width: 768px) {
    .page-title-box {
        text-align: center;
    }
    
    .page-title-box .page-title-right {
        justify-content: center;
        margin-top: 1rem;
    }
    
    .table th,
    .table td {
        padding: 8px 10px;
        font-size: 0.8rem;
    }
    
    .btn-group .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .badge {
        font-size: 0.7rem;
        padding: 0.25em 0.5em;
    }
    
    .card-body .row.mb-2 {
        flex-direction: column;
        gap: 1rem;
    }
    
    .card-body .row.mb-2 .col-sm-5,
    .card-body .row.mb-2 .col-sm-7 {
        text-align: center;
    }
}

/* ===== Animation Classes ===== */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* ===== Hover Effects ===== */
.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* ===== Status Indicators ===== */
.status-active {
    color: #28a745;
}

.status-inactive {
    color: #6c757d;
}

.status-pending {
    color: #ffc107;
}

.status-error {
    color: #dc3545;
}

/* ===== Custom Scrollbar ===== */
.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
