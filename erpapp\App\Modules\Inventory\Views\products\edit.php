<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="page-title">
                    <i class="fas fa-edit"></i> <?= __('تعديل المنتج') ?>
                </h1>
                <div>
                    <a href="<?= base_url('inventory/products/' . $product['product_id']) ?>" class="btn btn-info me-2">
                        <i class="fas fa-eye me-1"></i> <?= __('عرض المنتج') ?>
                    </a>
                    <a href="<?= base_url('inventory/products') ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> <?= __('العودة للمنتجات') ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php
    // عرض رسائل الخطأ
    $error = flash('product_error');
    if ($error) {
        echo '<script>
            document.addEventListener("DOMContentLoaded", function() {
                if (typeof toastr !== "undefined") {
                    toastr.error("' . addslashes($error['message']) . '", "' . __('خطأ') . '");
                }
            });
        </script>';
    }
    ?>

    <div class="row">
        <div class="col-lg-10 mx-auto">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-box me-2"></i> <?= __('تعديل بيانات المنتج') ?>: <?= htmlspecialchars($product['product_name_ar']) ?>
                    </h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('inventory/products/' . $product['product_id'] . '/update') ?>" method="post" enctype="multipart/form-data" id="product-form">
                        
                        <!-- تبويبات النموذج -->
                        <ul class="nav nav-tabs mb-4" id="productTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="basic-info-tab" data-bs-toggle="tab" data-bs-target="#basic-info" type="button" role="tab">
                                    <i class="fas fa-info-circle me-1"></i> <?= __('المعلومات الأساسية') ?>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="pricing-tab" data-bs-toggle="tab" data-bs-target="#pricing" type="button" role="tab">
                                    <i class="fas fa-dollar-sign me-1"></i> <?= __('الأسعار') ?>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="inventory-tab" data-bs-toggle="tab" data-bs-target="#inventory" type="button" role="tab">
                                    <i class="fas fa-warehouse me-1"></i> <?= __('إعدادات المخزون') ?>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="additional-info-tab" data-bs-toggle="tab" data-bs-target="#additional-info" type="button" role="tab">
                                    <i class="fas fa-cog me-1"></i> <?= __('معلومات إضافية') ?>
                                </button>
                            </li>
                        </ul>

                        <!-- محتوى التبويبات -->
                        <div class="tab-content" id="productTabsContent">
                            <!-- تبويب المعلومات الأساسية -->
                            <div class="tab-pane fade show active" id="basic-info" role="tabpanel">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="product_code" class="form-label"><?= __('كود المنتج') ?> <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="product_code" name="product_code" 
                                               value="<?= htmlspecialchars($product['product_code']) ?>" required>
                                        <div class="form-text"><?= __('كود فريد للمنتج') ?></div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="barcode" class="form-label"><?= __('الباركود') ?></label>
                                        <input type="text" class="form-control" id="barcode" name="barcode" 
                                               value="<?= htmlspecialchars($product['barcode'] ?? '') ?>">
                                        <div class="form-text"><?= __('باركود المنتج (اختياري)') ?></div>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="product_name_ar" class="form-label"><?= __('اسم المنتج (عربي)') ?> <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="product_name_ar" name="product_name_ar" 
                                               value="<?= htmlspecialchars($product['product_name_ar']) ?>" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="product_name_en" class="form-label"><?= __('اسم المنتج (إنجليزي)') ?></label>
                                        <input type="text" class="form-control" id="product_name_en" name="product_name_en" 
                                               value="<?= htmlspecialchars($product['product_name_en'] ?? '') ?>">
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="description_ar" class="form-label"><?= __('الوصف (عربي)') ?></label>
                                        <textarea class="form-control" id="description_ar" name="description_ar" rows="3"><?= htmlspecialchars($product['description_ar'] ?? '') ?></textarea>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="description_en" class="form-label"><?= __('الوصف (إنجليزي)') ?></label>
                                        <textarea class="form-control" id="description_en" name="description_en" rows="3"><?= htmlspecialchars($product['description_en'] ?? '') ?></textarea>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label for="category_id" class="form-label"><?= __('الفئة') ?> <span class="text-danger">*</span></label>
                                        <select class="form-select" id="category_id" name="category_id" required>
                                            <option value=""><?= __('اختر الفئة') ?></option>
                                            <?php foreach ($categories as $category): ?>
                                                <option value="<?= $category['category_id'] ?>" 
                                                        <?= $product['category_id'] == $category['category_id'] ? 'selected' : '' ?>>
                                                    <?= htmlspecialchars($category['category_name_ar']) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="unit_id" class="form-label"><?= __('وحدة القياس') ?> <span class="text-danger">*</span></label>
                                        <select class="form-select" id="unit_id" name="unit_id" required>
                                            <option value=""><?= __('اختر الوحدة') ?></option>
                                            <?php foreach ($units as $unit): ?>
                                                <option value="<?= $unit['unit_id'] ?>" 
                                                        <?= $product['unit_id'] == $unit['unit_id'] ? 'selected' : '' ?>>
                                                    <?= htmlspecialchars($unit['unit_name_ar']) ?> (<?= htmlspecialchars($unit['unit_symbol_ar']) ?>)
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="product_type" class="form-label"><?= __('نوع المنتج') ?> <span class="text-danger">*</span></label>
                                        <select class="form-select" id="product_type" name="product_type" required>
                                            <option value="product" <?= $product['product_type'] == 'product' ? 'selected' : '' ?>><?= __('منتج') ?></option>
                                            <option value="service" <?= $product['product_type'] == 'service' ? 'selected' : '' ?>><?= __('خدمة') ?></option>
                                            <option value="digital" <?= $product['product_type'] == 'digital' ? 'selected' : '' ?>><?= __('رقمي') ?></option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- تبويب الأسعار -->
                            <div class="tab-pane fade" id="pricing" role="tabpanel">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="cost_price" class="form-label"><?= __('سعر التكلفة') ?> <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="cost_price" name="cost_price" 
                                                   step="0.01" min="0" value="<?= $product['cost_price'] ?>" required>
                                            <span class="input-group-text"><?= __('ر.س') ?></span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="selling_price" class="form-label"><?= __('سعر البيع') ?> <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="selling_price" name="selling_price" 
                                                   step="0.01" min="0" value="<?= $product['selling_price'] ?>" required>
                                            <span class="input-group-text"><?= __('ر.س') ?></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="tax_rate" class="form-label"><?= __('معدل الضريبة (%)') ?></label>
                                        <input type="number" class="form-control" id="tax_rate" name="tax_rate" 
                                               step="0.01" min="0" max="100" value="<?= $product['tax_rate'] ?>">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label"><?= __('هامش الربح') ?></label>
                                        <div class="form-control-plaintext" id="profit_margin">
                                            <?php
                                            $margin = $product['cost_price'] > 0 ? 
                                                (($product['selling_price'] - $product['cost_price']) / $product['cost_price'] * 100) : 0;
                                            echo number_format($margin, 2) . '%';
                                            ?>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- تبويب إعدادات المخزون -->
                            <div class="tab-pane fade" id="inventory" role="tabpanel">
                                <div class="row mb-3">
                                    <div class="col-md-12">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="track_inventory" name="track_inventory" 
                                                   value="1" <?= $product['track_inventory'] ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="track_inventory">
                                                <?= __('تتبع المخزون') ?>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label for="min_stock_level" class="form-label"><?= __('الحد الأدنى للمخزون') ?></label>
                                        <input type="number" class="form-control" id="min_stock_level" name="min_stock_level" 
                                               step="0.01" min="0" value="<?= $product['min_stock_level'] ?>">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="max_stock_level" class="form-label"><?= __('الحد الأقصى للمخزون') ?></label>
                                        <input type="number" class="form-control" id="max_stock_level" name="max_stock_level" 
                                               step="0.01" min="0" value="<?= $product['max_stock_level'] ?>">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="reorder_point" class="form-label"><?= __('نقطة إعادة الطلب') ?></label>
                                        <input type="number" class="form-control" id="reorder_point" name="reorder_point" 
                                               step="0.01" min="0" value="<?= $product['reorder_point'] ?>">
                                    </div>
                                </div>
                            </div>

                            <!-- تبويب معلومات إضافية -->
                            <div class="tab-pane fade" id="additional-info" role="tabpanel">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="weight" class="form-label"><?= __('الوزن (كجم)') ?></label>
                                        <input type="number" class="form-control" id="weight" name="weight" 
                                               step="0.001" min="0" value="<?= $product['weight'] ?>">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="dimensions" class="form-label"><?= __('الأبعاد') ?></label>
                                        <input type="text" class="form-control" id="dimensions" name="dimensions" 
                                               value="<?= htmlspecialchars($product['dimensions'] ?? '') ?>" 
                                               placeholder="<?= __('الطول × العرض × الارتفاع') ?>">
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-12">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                                   value="1" <?= $product['is_active'] ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="is_active">
                                                <?= __('منتج نشط') ?>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <!-- أزرار الحفظ والإلغاء -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="submit" class="btn btn-warning btn-lg">
                                            <i class="fas fa-save me-2"></i> <?= __('حفظ التعديلات') ?>
                                        </button>
                                        <button type="reset" class="btn btn-outline-secondary btn-lg ms-2">
                                            <i class="fas fa-undo me-2"></i> <?= __('إعادة تعيين') ?>
                                        </button>
                                    </div>
                                    <div>
                                        <a href="<?= base_url('inventory/products/' . $product['product_id']) ?>" class="btn btn-outline-info btn-lg me-2">
                                            <i class="fas fa-eye me-2"></i> <?= __('عرض المنتج') ?>
                                        </a>
                                        <a href="<?= base_url('inventory/products') ?>" class="btn btn-outline-danger btn-lg">
                                            <i class="fas fa-times me-2"></i> <?= __('إلغاء') ?>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // حساب هامش الربح تلقائياً
    function calculateProfitMargin() {
        const costPrice = parseFloat(document.getElementById('cost_price').value) || 0;
        const sellingPrice = parseFloat(document.getElementById('selling_price').value) || 0;

        if (costPrice > 0) {
            const margin = ((sellingPrice - costPrice) / costPrice * 100).toFixed(2);
            document.getElementById('profit_margin').textContent = margin + '%';
        } else {
            document.getElementById('profit_margin').textContent = '0%';
        }
    }

    // ربط الأحداث
    document.getElementById('cost_price').addEventListener('input', calculateProfitMargin);
    document.getElementById('selling_price').addEventListener('input', calculateProfitMargin);

    // التحقق من صحة النموذج
    const form = document.getElementById('product-form');
    form.addEventListener('submit', function(e) {
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;
        
        requiredFields.forEach(function(field) {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            if (typeof toastr !== 'undefined') {
                toastr.error('<?= __('يرجى ملء جميع الحقول المطلوبة') ?>', '<?= __('خطأ في النموذج') ?>');
            }
        }
    });
    
    // تأكيد التعديل
    form.addEventListener('submit', function(e) {
        if (!confirm('<?= __('هل أنت متأكد من حفظ التعديلات؟') ?>')) {
            e.preventDefault();
        }
    });
});
</script>

<style>
.required::after {
    content: " *";
    color: #dc3545;
    font-weight: bold;
}

.form-check-input:checked {
    background-color: #198754;
    border-color: #198754;
}

.is-invalid {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.form-control:focus, .form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}
</style>
