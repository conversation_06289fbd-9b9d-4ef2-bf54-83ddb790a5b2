<?php
/**
 * اختبار التصميم النظيف للجدول
 */

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>التصميم النظيف للجدول - مثل الصورة</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdn.jsdelivr.net/npm/@mdi/font@6.9.96/css/materialdesignicons.min.css' rel='stylesheet'>";
echo "<link href='public/css/modules/purchases.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";

echo "<div class='container-fluid mt-4'>";
echo "<h2>🎨 التصميم النظيف للجدول - مطابق للصورة!</h2>";

echo "<div class='alert alert-success mb-4'>";
echo "<h5>✅ التحسينات المطبقة:</h5>";
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<ul>";
echo "<li><strong>خلفية نظيفة:</strong> أبيض بدون ظلال</li>";
echo "<li><strong>رأس الجدول:</strong> رمادي فاتح #f8f9fa</li>";
echo "<li><strong>حدود خفيفة:</strong> #f1f3f4 بدلاً من الداكنة</li>";
echo "<li><strong>خط واضح:</strong> 14px للمحتوى، 13px للرأس</li>";
echo "</ul>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<ul>";
echo "<li><strong>أيقونات مخفية:</strong> تظهر عند التمرير فقط</li>";
echo "<li><strong>ألوان هادئة:</strong> مثل الصورة تماماً</li>";
echo "<li><strong>تأثيرات خفيفة:</strong> بدون مبالغة</li>";
echo "<li><strong>تصميم متجاوب:</strong> يعمل على جميع الأجهزة</li>";
echo "</ul>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='card'>";
echo "<div class='card-body p-0'>";
echo "<div class='table-responsive'>";
echo "<table class='table table-hover mb-0'>";
echo "<thead>";
echo "<tr>";
echo "<th data-sortable='true' data-column='0'>الإجراء</th>";
echo "<th data-sortable='true' data-column='1'>الحالة</th>";
echo "<th data-sortable='true' data-column='2'>المبلغ</th>";
echo "<th data-sortable='true' data-column='3'>التاريخ</th>";
echo "<th data-sortable='true' data-column='4'>الطلب</th>";
echo "<th data-sortable='true' data-column='5'>العميل</th>";
echo "<th data-sortable='true' data-column='6'>الفاتورة</th>";
echo "</tr>";
echo "</thead>";
echo "<tbody>";

// بيانات تجريبية مثل الصورة
$data = [
    ['⋯ ⬇', 'Due', '1,210.00 ر.س', 'February, 2024 08', '#4844326', 'Fushiguro Megumi', 'INV-#4844326'],
    ['⋯ ⬇', 'Refunded', '1,210.00 ر.س', 'February, 2024 24', '#4844342', 'Fushiguro Megumi', 'INV-#4844342'],
    ['⋯ ⬇', 'Cancelled', '1,210.00 ر.س', 'February, 2024 15', '#4844333', 'Gojo Satoru', 'INV-#4844333'],
    ['⋯ ⬇', 'Paid', '2,210.00 ر.س', 'February, 2024 16', '#4144334', 'Nanami', 'INV-#4144334'],
    ['⋯ ⬇', 'Paid', '1,740.00 ر.س', 'February, 2024 10', '#3544328', 'Inumaki Toge', 'INV-#3544328'],
    ['⋯ ⬇', 'Paid', '1,210.00 ر.س', 'February, 2024 12', '#3744330', 'Itadori Yuuji', 'INV-#3744330'],
    ['⋯ ⬇', 'Paid', '1,210.00 ر.س', 'February, 2024 07', '#6944324', 'Kugisaki Nobara', 'INV-#6944324'],
    ['⋯ ⬇', 'Due', '1,210.00 ر.س', 'February, 2024 22', '#4744340', 'Kugisaki Nobara', 'INV-#4744340'],
];

foreach ($data as $row) {
    echo "<tr>";
    echo "<td>{$row[0]}</td>";
    
    // الحالة مع الألوان المناسبة
    $statusClass = '';
    switch ($row[1]) {
        case 'Paid':
            $statusClass = 'bg-success';
            break;
        case 'Due':
            $statusClass = 'bg-warning';
            break;
        case 'Cancelled':
            $statusClass = 'bg-danger';
            break;
        case 'Refunded':
            $statusClass = 'bg-info';
            break;
    }
    echo "<td><span class='badge {$statusClass}'>{$row[1]}</span></td>";
    
    echo "<td class='text-end'>{$row[2]}</td>";
    echo "<td class='text-muted'>{$row[3]}</td>";
    echo "<td><a href='#'>{$row[4]}</a></td>";
    echo "<td>{$row[5]}</td>";
    echo "<td><a href='#'>{$row[6]}</a></td>";
    echo "</tr>";
}

echo "</tbody>";
echo "</table>";
echo "</div>";
echo "</div>";
echo "</div>";

// Pagination مثل الصورة
echo "<div class='d-flex justify-content-between align-items-center mt-3'>";
echo "<div class='datatable-info'>";
echo "Showing <strong>1</strong> to <strong>8</strong> of <strong>23</strong> items";
echo "</div>";
echo "<nav>";
echo "<ul class='pagination'>";
echo "<li class='page-item disabled'>";
echo "<a class='page-link' href='#' aria-label='Previous'>‹</a>";
echo "</li>";
echo "<li class='page-item'><a class='page-link' href='#'>3</a></li>";
echo "<li class='page-item'><a class='page-link' href='#'>2</a></li>";
echo "<li class='page-item active'><a class='page-link' href='#'>1</a></li>";
echo "<li class='page-item'><a class='page-link' href='#'>›</a></li>";
echo "<li class='page-item disabled'>";
echo "<a class='page-link' href='#' aria-label='Next'>»</a>";
echo "</li>";
echo "</ul>";
echo "</nav>";
echo "<div class='text-muted small'>";
echo "Show all items out of 23-8 showing";
echo "</div>";
echo "</div>";

echo "<div class='alert alert-info mt-4'>";
echo "<h5>🎯 الميزات الجديدة:</h5>";
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h6>التصميم:</h6>";
echo "<ul>";
echo "<li>✅ خلفية بيضاء نظيفة</li>";
echo "<li>✅ حدود خفيفة وهادئة</li>";
echo "<li>✅ ألوان متناسقة</li>";
echo "<li>✅ خطوط واضحة ومقروءة</li>";
echo "</ul>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<h6>التفاعل:</h6>";
echo "<ul>";
echo "<li>✅ أيقونات فرز مخفية</li>";
echo "<li>✅ تظهر عند التمرير</li>";
echo "<li>✅ تثبت عند الفرز</li>";
echo "<li>✅ تأثيرات سلسة</li>";
echo "</ul>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='text-center mt-4'>";
echo "<a href='purchases/suppliers' class='btn btn-primary me-2'>";
echo "<i class='mdi mdi-account-group'></i> اختبار مع الموردين";
echo "</a>";
echo "<a href='purchases/supplier-groups' class='btn btn-success'>";
echo "<i class='mdi mdi-folder-account'></i> اختبار مع المجموعات";
echo "</a>";
echo "</div>";

echo "</div>";

echo "<script>";
echo "// إضافة أيقونات الفرز";
echo "document.querySelectorAll('th[data-sortable=\"true\"]').forEach((th, index) => {";
echo "    th.style.cursor = 'pointer';";
echo "    th.style.position = 'relative';";
echo "    th.style.paddingRight = '40px';";
echo "    th.innerHTML += ' <span class=\"sort-icon\"><i class=\"mdi mdi-sort text-muted\"></i></span>';";
echo "});";
echo "</script>";

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>";
echo "</body>";
echo "</html>";
?>
