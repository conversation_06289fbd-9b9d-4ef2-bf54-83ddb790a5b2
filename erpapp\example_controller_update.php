<?php
/**
 * مثال على كيفية تحديث Controller ليستخدم النظام الجديد
 * 
 * في SupplierController@index، بدلاً من:
 * view('Purchases::suppliers/index', $data);
 * 
 * سيصبح:
 */

// في نهاية دالة index() في SupplierController
public function index()
{
    // ... كل الكود الموجود يبقى كما هو ...
    
    // بدلاً من:
    // view('Purchases::suppliers/index', $data);
    
    // سيصبح:
    view('Purchases::suppliers/index_new', $data);
    
    // أو يمكن استخدام النظام مباشرة:
    /*
    render_datatable_page([
        'title' => 'الموردين',
        'module' => 'purchases',
        'entity' => 'suppliers',
        'data' => $suppliers,
        'columns' => $this->getSupplierColumns(),
        'stats' => $this->getSupplierStats($stats),
        'pagination' => $pagination,
        'filters' => $applied_filters,
        'breadcrumb' => [
            ['title' => 'المشتريات', 'url' => base_url('purchases')],
            ['title' => 'الموردين', 'active' => true]
        ],
        'actions' => $this->getSupplierActions(),
        'filters_config' => $this->getSupplierFiltersConfig($supplierGroups),
        'empty_state' => $this->getSupplierEmptyState()
    ]);
    */
}

/**
 * إعداد أعمدة الموردين
 */
private function getSupplierColumns()
{
    return [
        [
            'field' => 'entity_number',
            'title' => 'الرقم',
            'type' => 'text',
            'width' => '120px'
        ],
        [
            'field' => 'G_name_ar',
            'title' => 'اسم المورد',
            'type' => 'link',
            'url' => 'purchases/suppliers/{entity_number}',
            'subtitle_field' => 'G_name_en'
        ],
        [
            'field' => 'S_company_name',
            'title' => 'اسم الشركة',
            'type' => 'text'
        ],
        [
            'field' => 'group_name',
            'title' => 'المجموعة',
            'type' => 'badge'
        ],
        [
            'field' => 'G_phone',
            'title' => 'الهاتف',
            'type' => 'text'
        ],
        [
            'field' => 'G_status',
            'title' => 'الحالة',
            'type' => 'badge',
            'status_config' => [
                'classes' => [
                    'active' => 'success',
                    'inactive' => 'secondary',
                    'suspended' => 'warning'
                ],
                'texts' => [
                    'active' => 'نشط',
                    'inactive' => 'غير نشط',
                    'suspended' => 'معلق'
                ]
            ]
        ],
        [
            'field' => 'actions',
            'title' => 'الإجراءات',
            'type' => 'actions',
            'width' => '125px',
            'buttons' => [
                [
                    'type' => 'link',
                    'url' => 'purchases/suppliers/{entity_number}',
                    'class' => 'btn-primary',
                    'icon' => 'mdi mdi-eye',
                    'title' => 'عرض'
                ],
                [
                    'type' => 'link',
                    'url' => 'purchases/suppliers/{entity_number}/edit',
                    'class' => 'btn-success',
                    'icon' => 'mdi mdi-pencil',
                    'title' => 'تعديل'
                ],
                [
                    'type' => 'button',
                    'class' => 'btn-danger',
                    'icon' => 'mdi mdi-delete',
                    'title' => 'حذف',
                    'onclick' => 'confirmDelete({entity_number})'
                ]
            ]
        ]
    ];
}

/**
 * إعداد إجراءات الموردين
 */
private function getSupplierActions()
{
    return [
        [
            'type' => 'primary',
            'url' => 'purchases/suppliers/create',
            'icon' => 'mdi mdi-plus-circle',
            'text' => 'إضافة مورد جديد'
        ]
    ];
}

/**
 * إعداد فلاتر الموردين
 */
private function getSupplierFiltersConfig($supplierGroups)
{
    return [
        [
            'name' => 'search',
            'type' => 'search',
            'label' => 'البحث في الموردين',
            'placeholder' => 'ابحث بالاسم أو اسم الشركة...',
            'icon' => 'mdi mdi-magnify',
            'col_size' => 6,
            'help' => 'البحث في الاسم العربي، الإنجليزي، أو اسم الشركة'
        ],
        [
            'name' => 'status',
            'type' => 'select',
            'label' => 'حالة المورد',
            'placeholder' => 'جميع الحالات',
            'icon' => 'mdi mdi-check-circle',
            'col_size' => 3,
            'options' => [
                'active' => 'نشط',
                'inactive' => 'غير نشط',
                'suspended' => 'معلق'
            ]
        ],
        [
            'name' => 'group_id',
            'type' => 'select',
            'label' => 'مجموعة الموردين',
            'placeholder' => 'جميع المجموعات',
            'icon' => 'mdi mdi-folder-multiple',
            'col_size' => 3,
            'options' => array_column($supplierGroups, 'name_ar', 'group_number')
        ]
    ];
}

/**
 * إعداد إحصائيات الموردين
 */
private function getSupplierStats($stats)
{
    return [
        [
            'title' => 'إجمالي الموردين',
            'value' => $stats['total_suppliers'] ?? 0,
            'icon' => 'mdi mdi-truck',
            'color' => 'muted'
        ],
        [
            'title' => 'الموردين النشطين',
            'value' => $stats['active_suppliers'] ?? 0,
            'icon' => 'mdi mdi-check-circle',
            'color' => 'success'
        ]
    ];
}

/**
 * إعداد Empty State للموردين
 */
private function getSupplierEmptyState()
{
    return [
        'icon' => 'mdi mdi-truck-outline',
        'message' => 'لا توجد موردين',
        'action' => [
            'url' => 'purchases/suppliers/create',
            'text' => 'إضافة مورد جديد'
        ]
    ];
}

/**
 * الفوائد من هذا النهج:
 * 
 * 1. عدم التكرار: 
 *    - ملف واحد بدلاً من 600+ سطر لكل صفحة
 *    - إعدادات بسيطة لكل جدول جديد
 * 
 * 2. سهولة الصيانة:
 *    - تعديل واحد يؤثر على جميع الجداول
 *    - إضافة ميزة جديدة تظهر في كل مكان
 * 
 * 3. التوافق الكامل:
 *    - يستخدم css_helper.php الموجود
 *    - يستخدم filter_helper.php الموجود
 *    - يحمل من loader.php
 *    - نفس التصميم والوظائف
 * 
 * 4. المرونة:
 *    - إعداد الأعمدة بسهولة
 *    - تخصيص الفلاتر لكل صفحة
 *    - إضافة إجراءات مخصصة
 */
?>
