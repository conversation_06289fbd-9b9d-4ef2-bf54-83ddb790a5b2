<?php
namespace App\Modules\Purchases\Controllers;

use App\Modules\Purchases\Models\Supplier;
use App\Modules\Purchases\Models\SupplierGroup;
use Exception;
use PDO;

/**
 * SupplierController - متحكم الموردين
 */
class SupplierController
{
    /**
     * Route parameters
     */
    protected $params = [];

    /**
     * Supplier model
     */
    protected $supplierModel;

    /**
     * SupplierGroup model
     */
    protected $supplierGroupModel;

    /**
     * Constructor
     */
    public function __construct($params = [])
    {
        $this->params = $params;
        $this->supplierModel = new Supplier();
        $this->supplierGroupModel = new SupplierGroup();

        // التحقق من تسجيل الدخول
        if (!is_logged_in()) {
            redirect(base_url('login'));
        }

        // التحقق من وجود شركة حالية
        $user = current_user();
        if (!$user || !$user['current_company_id']) {
            flash('supplier_error', 'يجب تحديد شركة حالية للوصول إلى الموردين', 'warning');
            redirect(base_url('companies'));
        }
    }

    /**
     * عرض قائمة الموردين
     */
    public function index()
    {
        $company_id = current_user()['current_company_id'];
        
        // الحصول على الفلاتر
        $filters = [];

        if (!empty($_GET['search'])) {
            $filters['search'] = $_GET['search'];
        }

        if (!empty($_GET['status'])) {
            $filters['status'] = $_GET['status'];
        }

        if (!empty($_GET['group_id'])) {
            $filters['group_id'] = $_GET['group_id'];
        }

        // الحصول على الموردين
        $suppliers = $this->supplierModel->getByCompany($company_id, $filters);

        // الحصول على مجموعات الموردين للفلتر
        $supplierGroups = $this->supplierGroupModel->getForSelect($company_id);

        // الحصول على الإحصائيات
        $stats = $this->supplierModel->getStats($company_id);

        // عرض الصفحة
        $data = [
            'title' => 'الموردين',
            'suppliers' => $suppliers,
            'supplierGroups' => $supplierGroups,
            'stats' => $stats,
            'filters' => [
                'search' => $_GET['search'] ?? '',
                'status' => $_GET['status'] ?? '',
                'group_id' => $_GET['group_id'] ?? ''
            ],
            'breadcrumb' => [
                ['title' => 'المشتريات', 'url' => base_url('purchases')],
                ['title' => 'الموردين', 'active' => true]
            ]
        ];

        view('Purchases::suppliers/index', $data);
    }

    /**
     * عرض صفحة إضافة مورد جديد
     */
    public function create()
    {
        $company_id = current_user()['current_company_id'];
        
        // الحصول على مجموعات الموردين
        $supplierGroups = $this->supplierGroupModel->getForSelect($company_id);

        $data = [
            'title' => 'إضافة مورد جديد',
            'supplierGroups' => $supplierGroups,
            'breadcrumb' => [
                ['title' => 'المشتريات', 'url' => base_url('purchases')],
                ['title' => 'الموردين', 'url' => base_url('purchases/suppliers')],
                ['title' => 'إضافة مورد', 'active' => true]
            ]
        ];

        view('Purchases::suppliers/create', $data);
    }

    /**
     * حفظ مورد جديد
     */
    public function store()
    {
        try {
            // التحقق من صحة البيانات
            $validatedData = $this->validateSupplierData($_POST);

            // إضافة معلومات المستخدم والشركة
            $company_id = current_user()['current_company_id'];
            $user_id = current_user()['UserID'];
            $validatedData['company_id'] = $company_id;
            $validatedData['created_by'] = $user_id;

            // بدء المعاملة
            $this->supplierModel->db->beginTransaction();

            // إنشاء المورد
            $supplierNumber = $this->supplierModel->create($validatedData);

            if ($supplierNumber) {
                // الحصول على entity_id للمورد الجديد
                $supplier = $this->supplierModel->getByNumber($supplierNumber, $company_id);
                $entity_id = $supplier['id'];

                // حفظ العناوين
                if (!empty($_POST['addresses'])) {
                    $this->saveAddresses($entity_id, $company_id, $_POST['addresses'], $_POST['default_address'] ?? 0, $user_id);
                }

                // حفظ الحسابات البنكية
                if (!empty($_POST['bank_accounts'])) {
                    $this->saveBankAccounts($entity_id, $company_id, $_POST['bank_accounts'], $_POST['default_bank_account'] ?? 0, $user_id);
                }

                // تأكيد المعاملة
                $this->supplierModel->db->commit();

                flash('success', 'تم إنشاء المورد بنجاح مع جميع البيانات');
                redirect('/purchases/suppliers/' . $supplierNumber);
            } else {
                $this->supplierModel->db->rollback();
                flash('error', 'حدث خطأ أثناء إنشاء المورد');
                redirect('/purchases/suppliers/create');
            }

        } catch (Exception $e) {
            $this->supplierModel->db->rollback();
            flash('error', $e->getMessage());
            redirect('/purchases/suppliers/create');
        }
    }

    /**
     * عرض تفاصيل مورد
     */
    public function show()
    {
        $entity_number = $this->params['id'];
        $company_id = current_user()['current_company_id'];

        $supplier = $this->supplierModel->getByNumber($entity_number, $company_id);

        if (!$supplier) {
            flash('error', 'المورد غير موجود');
            redirect('/purchases/suppliers');
        }

        $data = [
            'title' => $supplier['G_name_ar'],
            'supplier' => $supplier,
            'breadcrumb' => [
                ['title' => 'المشتريات', 'url' => base_url('purchases')],
                ['title' => 'الموردين', 'url' => base_url('purchases/suppliers')],
                ['title' => $supplier['G_name_ar'], 'active' => true]
            ]
        ];

        view('Purchases::suppliers/show', $data);
    }

    /**
     * عرض صفحة تعديل مورد
     */
    public function edit()
    {
        $entity_number = $this->params['id'];
        $company_id = current_user()['current_company_id'];

        $supplier = $this->supplierModel->getByNumber($entity_number, $company_id);

        if (!$supplier) {
            flash('error', 'المورد غير موجود');
            redirect('/purchases/suppliers');
        }

        // الحصول على مجموعات الموردين
        $supplierGroups = $this->supplierGroupModel->getForSelect($company_id);

        $data = [
            'title' => 'تعديل المورد - ' . $supplier['G_name_ar'],
            'supplier' => $supplier,
            'supplierGroups' => $supplierGroups,
            'breadcrumb' => [
                ['title' => 'المشتريات', 'url' => base_url('purchases')],
                ['title' => 'الموردين', 'url' => base_url('purchases/suppliers')],
                ['title' => 'تعديل المورد', 'active' => true]
            ]
        ];

        view('Purchases::suppliers/edit', $data);
    }

    /**
     * تحديث مورد
     */
    public function update()
    {
        try {
            $entity_number = $this->params['id'];
            $company_id = current_user()['current_company_id'];

            // التحقق من وجود المورد
            $supplier = $this->supplierModel->getByNumber($entity_number, $company_id);
            if (!$supplier) {
                flash('error', 'المورد غير موجود');
                redirect('/purchases/suppliers');
            }

            // التحقق من صحة البيانات
            $validatedData = $this->validateSupplierData($_POST);
            $validatedData['updated_by'] = current_user()['UserID'];

            // تحديث المورد
            $result = $this->supplierModel->update($entity_number, $validatedData, $company_id);

            if ($result) {
                flash('success', 'تم تحديث المورد بنجاح');
                redirect('/purchases/suppliers/' . $entity_number);
            } else {
                flash('error', 'حدث خطأ أثناء تحديث المورد');
                redirect('/purchases/suppliers/' . $entity_number . '/edit');
            }

        } catch (Exception $e) {
            flash('error', $e->getMessage());
            redirect('/purchases/suppliers/' . $this->params['id'] . '/edit');
        }
    }

    /**
     * حذف مورد
     */
    public function delete()
    {
        try {
            $entity_number = $this->params['id'];
            $company_id = current_user()['current_company_id'];

            // التحقق من وجود المورد
            $supplier = $this->supplierModel->getByNumber($entity_number, $company_id);
            if (!$supplier) {
                flash('error', 'المورد غير موجود');
                redirect('/purchases/suppliers');
            }

            // حذف المورد
            $result = $this->supplierModel->delete($entity_number, $company_id);

            if ($result) {
                flash('success', 'تم حذف المورد بنجاح');
            } else {
                flash('error', 'حدث خطأ أثناء حذف المورد');
            }

        } catch (Exception $e) {
            flash('error', $e->getMessage());
        }

        redirect('/purchases/suppliers');
    }

    /**
     * التحقق من صحة بيانات المورد
     */
    private function validateSupplierData($data)
    {
        $errors = [];

        // التحقق من الاسم العربي
        if (empty($data['G_name_ar'])) {
            $errors[] = 'اسم المورد بالعربية مطلوب';
        }

        // التحقق من البريد الإلكتروني
        if (!empty($data['S_email']) && !filter_var($data['S_email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'البريد الإلكتروني غير صحيح';
        }

        // التحقق من الموقع الإلكتروني
        if (!empty($data['G_website']) && !filter_var($data['G_website'], FILTER_VALIDATE_URL)) {
            $errors[] = 'الموقع الإلكتروني غير صحيح';
        }

        if (!empty($errors)) {
            throw new Exception(implode('<br>', $errors));
        }

        // إعداد البيانات
        return [
            'group_id' => !empty($data['group_id']) ? (int)$data['group_id'] : null,
            'G_name_ar' => trim($data['G_name_ar']),
            'G_name_en' => trim($data['G_name_en'] ?? ''),
            'G_phone' => trim($data['G_phone'] ?? ''),
            'G_mobile' => trim($data['G_mobile'] ?? ''),
            'G_website' => trim($data['G_website'] ?? ''),
            'G_notes' => trim($data['G_notes'] ?? ''),
            'G_status' => $data['G_status'] ?? 'active',
            'S_company_name' => trim($data['S_company_name'] ?? ''),
            'S_contact_person' => trim($data['S_contact_person'] ?? ''),
            'S_email' => trim($data['S_email'] ?? ''),
            'S_tax_number' => trim($data['S_tax_number'] ?? ''),
            'S_commercial_register' => trim($data['S_commercial_register'] ?? ''),
            'S_payment_terms' => !empty($data['S_payment_terms']) ? (int)$data['S_payment_terms'] : 30,
            'S_credit_limit' => !empty($data['S_credit_limit']) ? (float)$data['S_credit_limit'] : 0,
            'S_discount_rate' => !empty($data['S_discount_rate']) ? (float)$data['S_discount_rate'] : 0,
            'S_delivery_time' => !empty($data['S_delivery_time']) ? (int)$data['S_delivery_time'] : null,
            'S_minimum_order' => !empty($data['S_minimum_order']) ? (float)$data['S_minimum_order'] : null,
            'S_currency' => $data['S_currency'] ?? 'SAR',
            'S_rating' => $data['S_rating'] ?? 'C'
        ];
    }

    /**
     * حفظ عناوين المورد
     */
    private function saveAddresses($entity_id, $company_id, $addresses, $default_index, $user_id)
    {
        $sql = "INSERT INTO entity_addresses (
                    entity_id, company_id, address_type, address_label, address_line1,
                    city, state_province, postal_code, country, phone, is_default,
                    created_by, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

        $stmt = $this->supplierModel->db->prepare($sql);

        foreach ($addresses as $index => $address) {
            if (empty($address['address_type']) || empty($address['address_label']) || empty($address['address_line1'])) {
                continue; // تخطي العناوين غير المكتملة
            }

            $is_default = ($index == $default_index) ? 1 : 0;

            $stmt->execute([
                $entity_id,
                $company_id,
                $address['address_type'],
                $address['address_label'],
                $address['address_line1'],
                $address['city'],
                $address['state_province'] ?? null,
                $address['postal_code'] ?? null,
                $address['country'] ?? 'Saudi Arabia',
                $address['phone'] ?? null,
                $is_default,
                $user_id
            ]);
        }
    }

    /**
     * حفظ الحسابات البنكية للمورد
     */
    private function saveBankAccounts($entity_id, $company_id, $bank_accounts, $default_index, $user_id)
    {
        $sql = "INSERT INTO entity_bank_accounts (
                    entity_id, company_id, bank_name, account_number, account_name,
                    iban, currency, is_default, created_by, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

        $stmt = $this->supplierModel->db->prepare($sql);

        foreach ($bank_accounts as $index => $account) {
            if (empty($account['bank_name']) || empty($account['account_number']) || empty($account['account_name'])) {
                continue; // تخطي الحسابات غير المكتملة
            }

            $is_default = ($index == $default_index) ? 1 : 0;

            $stmt->execute([
                $entity_id,
                $company_id,
                $account['bank_name'],
                $account['account_number'],
                $account['account_name'],
                $account['iban'] ?? null,
                $account['currency'] ?? 'SAR',
                $is_default,
                $user_id
            ]);
        }
    }
}
