<?php
namespace App\Modules\Purchases\Controllers;

use App\Core\Controller;
use App\Modules\Purchases\Models\Supplier;
use App\Modules\Purchases\Models\SupplierGroup;

/**
 * SupplierController - متحكم الموردين
 */
class SupplierController extends Controller
{
    protected $supplierModel;
    protected $supplierGroupModel;

    public function __construct()
    {
        parent::__construct();
        $this->supplierModel = new Supplier();
        $this->supplierGroupModel = new SupplierGroup();
    }

    /**
     * عرض قائمة الموردين
     */
    public function index()
    {
        $company_id = current_user()['current_company_id'];
        
        // الحصول على الفلاتر
        $filters = [
            'search' => $_GET['search'] ?? '',
            'status' => $_GET['status'] ?? '',
            'group_id' => $_GET['group_id'] ?? ''
        ];

        // الحصول على الموردين
        $suppliers = $this->supplierModel->getByCompany($company_id, $filters);

        // الحصول على مجموعات الموردين للفلتر
        $supplierGroups = $this->supplierGroupModel->getForSelect($company_id);

        // الحصول على الإحصائيات
        $stats = $this->supplierModel->getStats($company_id);

        // عرض الصفحة
        $this->view('Purchases::suppliers/index', [
            'suppliers' => $suppliers,
            'supplierGroups' => $supplierGroups,
            'stats' => $stats,
            'filters' => $filters
        ]);
    }

    /**
     * عرض صفحة إضافة مورد جديد
     */
    public function create()
    {
        $company_id = current_user()['current_company_id'];
        
        // الحصول على مجموعات الموردين
        $supplierGroups = $this->supplierGroupModel->getForSelect($company_id);

        $this->view('Purchases::suppliers/create', [
            'supplierGroups' => $supplierGroups
        ]);
    }

    /**
     * حفظ مورد جديد
     */
    public function store()
    {
        try {
            // التحقق من صحة البيانات
            $validatedData = $this->validateSupplierData($_POST);
            
            // إضافة معلومات المستخدم والشركة
            $validatedData['company_id'] = current_user()['current_company_id'];
            $validatedData['created_by'] = current_user()['UserID'];

            // إنشاء المورد
            $supplierNumber = $this->supplierModel->create($validatedData);

            if ($supplierNumber) {
                flash('success', 'تم إنشاء المورد بنجاح');
                redirect('/purchases/suppliers/' . $supplierNumber);
            } else {
                flash('error', 'حدث خطأ أثناء إنشاء المورد');
                redirect('/purchases/suppliers/create');
            }

        } catch (Exception $e) {
            flash('error', $e->getMessage());
            redirect('/purchases/suppliers/create');
        }
    }

    /**
     * عرض تفاصيل مورد
     */
    public function show()
    {
        $entity_number = $this->params['id'];
        $company_id = current_user()['current_company_id'];

        $supplier = $this->supplierModel->getByNumber($entity_number, $company_id);

        if (!$supplier) {
            flash('error', 'المورد غير موجود');
            redirect('/purchases/suppliers');
        }

        $this->view('Purchases::suppliers/show', [
            'supplier' => $supplier
        ]);
    }

    /**
     * عرض صفحة تعديل مورد
     */
    public function edit()
    {
        $entity_number = $this->params['id'];
        $company_id = current_user()['current_company_id'];

        $supplier = $this->supplierModel->getByNumber($entity_number, $company_id);

        if (!$supplier) {
            flash('error', 'المورد غير موجود');
            redirect('/purchases/suppliers');
        }

        // الحصول على مجموعات الموردين
        $supplierGroups = $this->supplierGroupModel->getForSelect($company_id);

        $this->view('Purchases::suppliers/edit', [
            'supplier' => $supplier,
            'supplierGroups' => $supplierGroups
        ]);
    }

    /**
     * تحديث مورد
     */
    public function update()
    {
        try {
            $entity_number = $this->params['id'];
            $company_id = current_user()['current_company_id'];

            // التحقق من وجود المورد
            $supplier = $this->supplierModel->getByNumber($entity_number, $company_id);
            if (!$supplier) {
                flash('error', 'المورد غير موجود');
                redirect('/purchases/suppliers');
            }

            // التحقق من صحة البيانات
            $validatedData = $this->validateSupplierData($_POST);
            $validatedData['updated_by'] = current_user()['UserID'];

            // تحديث المورد
            $result = $this->supplierModel->update($entity_number, $validatedData, $company_id);

            if ($result) {
                flash('success', 'تم تحديث المورد بنجاح');
                redirect('/purchases/suppliers/' . $entity_number);
            } else {
                flash('error', 'حدث خطأ أثناء تحديث المورد');
                redirect('/purchases/suppliers/' . $entity_number . '/edit');
            }

        } catch (Exception $e) {
            flash('error', $e->getMessage());
            redirect('/purchases/suppliers/' . $this->params['id'] . '/edit');
        }
    }

    /**
     * حذف مورد
     */
    public function delete()
    {
        try {
            $entity_number = $this->params['id'];
            $company_id = current_user()['current_company_id'];

            // التحقق من وجود المورد
            $supplier = $this->supplierModel->getByNumber($entity_number, $company_id);
            if (!$supplier) {
                flash('error', 'المورد غير موجود');
                redirect('/purchases/suppliers');
            }

            // حذف المورد
            $result = $this->supplierModel->delete($entity_number, $company_id);

            if ($result) {
                flash('success', 'تم حذف المورد بنجاح');
            } else {
                flash('error', 'حدث خطأ أثناء حذف المورد');
            }

        } catch (Exception $e) {
            flash('error', $e->getMessage());
        }

        redirect('/purchases/suppliers');
    }

    /**
     * التحقق من صحة بيانات المورد
     */
    private function validateSupplierData($data)
    {
        $errors = [];

        // التحقق من الاسم العربي
        if (empty($data['G_name_ar'])) {
            $errors[] = 'اسم المورد بالعربية مطلوب';
        }

        // التحقق من البريد الإلكتروني
        if (!empty($data['S_email']) && !filter_var($data['S_email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'البريد الإلكتروني غير صحيح';
        }

        // التحقق من الموقع الإلكتروني
        if (!empty($data['G_website']) && !filter_var($data['G_website'], FILTER_VALIDATE_URL)) {
            $errors[] = 'الموقع الإلكتروني غير صحيح';
        }

        if (!empty($errors)) {
            throw new Exception(implode('<br>', $errors));
        }

        // إعداد البيانات
        return [
            'group_id' => !empty($data['group_id']) ? (int)$data['group_id'] : null,
            'G_name_ar' => trim($data['G_name_ar']),
            'G_name_en' => trim($data['G_name_en'] ?? ''),
            'G_phone' => trim($data['G_phone'] ?? ''),
            'G_mobile' => trim($data['G_mobile'] ?? ''),
            'G_website' => trim($data['G_website'] ?? ''),
            'G_notes' => trim($data['G_notes'] ?? ''),
            'G_status' => $data['G_status'] ?? 'active',
            'S_company_name' => trim($data['S_company_name'] ?? ''),
            'S_contact_person' => trim($data['S_contact_person'] ?? ''),
            'S_email' => trim($data['S_email'] ?? ''),
            'S_tax_number' => trim($data['S_tax_number'] ?? ''),
            'S_commercial_register' => trim($data['S_commercial_register'] ?? ''),
            'S_payment_terms' => !empty($data['S_payment_terms']) ? (int)$data['S_payment_terms'] : 30,
            'S_credit_limit' => !empty($data['S_credit_limit']) ? (float)$data['S_credit_limit'] : 0,
            'S_discount_rate' => !empty($data['S_discount_rate']) ? (float)$data['S_discount_rate'] : 0,
            'S_delivery_time' => !empty($data['S_delivery_time']) ? (int)$data['S_delivery_time'] : null,
            'S_minimum_order' => !empty($data['S_minimum_order']) ? (float)$data['S_minimum_order'] : null,
            'S_currency' => $data['S_currency'] ?? 'SAR',
            'S_rating' => $data['S_rating'] ?? 'C'
        ];
    }
}
