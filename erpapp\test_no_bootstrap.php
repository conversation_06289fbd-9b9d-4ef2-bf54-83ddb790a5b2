<?php
/**
 * اختبار النظام بدون Bootstrap
 */

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>نظام ERP بدون Bootstrap - No Bootstrap System</title>";

// CSS Files - بدون Bootstrap
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>";
echo "<link href='public/css/core/variables.css' rel='stylesheet'>";
echo "<link href='public/css/core/base.css' rel='stylesheet'>";
echo "<link href='public/css/components/buttons.css' rel='stylesheet'>";
echo "<link href='public/css/components/forms.css' rel='stylesheet'>";
echo "<link href='public/css/components/tables.css' rel='stylesheet'>";

echo "</head>";
echo "<body class='dark-theme'>";

echo "<div class='container-fluid mt-4'>";
echo "<h2><i class='fas fa-rocket text-primary'></i> نظام ERP بدون Bootstrap!</h2>";

echo "<div class='alert alert-success mb-4'>";
echo "<h5><i class='fas fa-check-circle'></i> المميزات الجديدة:</h5>";
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<ul>";
echo "<li><strong>🚀 أداء أفضل:</strong> بدون Bootstrap الثقيل</li>";
echo "<li><strong>🎨 تحكم كامل:</strong> CSS مخصص 100%</li>";
echo "<li><strong>🌙 ثيم داكن مثالي:</strong> بدون تعارض</li>";
echo "<li><strong>📱 متجاوب:</strong> responsive design</li>";
echo "</ul>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<ul>";
echo "<li><strong>⚡ سرعة تحميل:</strong> ملفات أصغر</li>";
echo "<li><strong>🔧 سهولة التخصيص:</strong> متغيرات موحدة</li>";
echo "<li><strong>🌍 دعم RTL:</strong> عربي مثالي</li>";
echo "<li><strong>🎯 لا تعارض:</strong> أنماط نظيفة</li>";
echo "</ul>";
echo "</div>";
echo "</div>";
echo "</div>";

// أزرار الاختبار
echo "<div class='text-center mb-4'>";
echo "<button id='themeToggle' class='btn btn-warning me-2'>";
echo "<i class='fas fa-sun'></i> تبديل إلى الثيم الفاتح";
echo "</button>";
echo "<button class='btn btn-primary me-2'>";
echo "<i class='fas fa-plus'></i> إضافة جديد";
echo "</button>";
echo "<button class='btn btn-success me-2'>";
echo "<i class='fas fa-download'></i> تصدير";
echo "</button>";
echo "<button class='btn btn-outline-secondary'>";
echo "<i class='fas fa-filter'></i> فلترة";
echo "</button>";
echo "</div>";

// نموذج بحث
echo "<div class='card mb-4'>";
echo "<div class='card-body'>";
echo "<div class='row'>";
echo "<div class='col-md-4'>";
echo "<div class='form-group'>";
echo "<label class='form-label'>البحث</label>";
echo "<div class='search-input'>";
echo "<input type='text' class='form-control' placeholder='ابحث عن مورد...'>";
echo "<i class='fas fa-search search-icon'></i>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "<div class='col-md-3'>";
echo "<div class='form-group'>";
echo "<label class='form-label'>المجموعة</label>";
echo "<select class='form-select'>";
echo "<option>جميع المجموعات</option>";
echo "<option>موردين محليين</option>";
echo "<option>موردين دوليين</option>";
echo "</select>";
echo "</div>";
echo "</div>";
echo "<div class='col-md-3'>";
echo "<div class='form-group'>";
echo "<label class='form-label'>الحالة</label>";
echo "<select class='form-select'>";
echo "<option>جميع الحالات</option>";
echo "<option>نشط</option>";
echo "<option>غير نشط</option>";
echo "</select>";
echo "</div>";
echo "</div>";
echo "<div class='col-md-2'>";
echo "<div class='form-group'>";
echo "<label class='form-label'>&nbsp;</label>";
echo "<button class='btn btn-primary d-block' style='width: 100%;'>";
echo "<i class='fas fa-search'></i> بحث";
echo "</button>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

// الجدول
echo "<div class='card'>";
echo "<div class='card-body p-0'>";
echo "<div class='table-responsive'>";
echo "<table class='table table-hover'>";
echo "<thead>";
echo "<tr>";
echo "<th data-sortable='true' data-column='0'>الإجراء</th>";
echo "<th data-sortable='true' data-column='1'>الحالة</th>";
echo "<th data-sortable='true' data-column='2'>المبلغ</th>";
echo "<th data-sortable='true' data-column='3'>التاريخ</th>";
echo "<th data-sortable='true' data-column='4'>الطلب</th>";
echo "<th data-sortable='true' data-column='5'>العميل</th>";
echo "<th data-sortable='true' data-column='6'>الفاتورة</th>";
echo "</tr>";
echo "</thead>";
echo "<tbody>";

// بيانات تجريبية
$data = [
    ['⋯ ⬇', 'Due', '1,210.00 ر.س', 'February, 2024 08', '#4844326', 'أحمد محمد', 'INV-#4844326'],
    ['⋯ ⬇', 'Refunded', '1,210.00 ر.س', 'February, 2024 24', '#4844342', 'سارة أحمد', 'INV-#4844342'],
    ['⋯ ⬇', 'Cancelled', '1,210.00 ر.س', 'February, 2024 15', '#4844333', 'محمد علي', 'INV-#4844333'],
    ['⋯ ⬇', 'Paid', '2,210.00 ر.س', 'February, 2024 16', '#4144334', 'فاطمة خالد', 'INV-#4144334'],
    ['⋯ ⬇', 'Paid', '1,740.00 ر.س', 'February, 2024 10', '#3544328', 'عبدالله سعد', 'INV-#3544328'],
    ['⋯ ⬇', 'Paid', '1,210.00 ر.س', 'February, 2024 12', '#3744330', 'نورا حسن', 'INV-#3744330'],
    ['⋯ ⬇', 'Paid', '1,210.00 ر.س', 'February, 2024 07', '#6944324', 'خالد عمر', 'INV-#6944324'],
    ['⋯ ⬇', 'Due', '1,210.00 ر.س', 'February, 2024 22', '#4744340', 'ليلى محمود', 'INV-#4744340'],
];

foreach ($data as $row) {
    echo "<tr>";
    echo "<td>";
    echo "<div class='table-actions'>";
    echo "<button class='btn btn-sm btn-outline-primary'><i class='fas fa-eye'></i></button>";
    echo "<button class='btn btn-sm btn-outline-warning'><i class='fas fa-edit'></i></button>";
    echo "<button class='btn btn-sm btn-outline-danger'><i class='fas fa-trash'></i></button>";
    echo "</div>";
    echo "</td>";
    
    // الحالة مع الألوان
    $statusClass = '';
    switch ($row[1]) {
        case 'Paid':
            $statusClass = 'badge-success';
            break;
        case 'Due':
            $statusClass = 'badge-warning';
            break;
        case 'Cancelled':
            $statusClass = 'badge-danger';
            break;
        case 'Refunded':
            $statusClass = 'badge-info';
            break;
    }
    echo "<td><span class='badge {$statusClass}'>{$row[1]}</span></td>";
    
    echo "<td class='text-end'>{$row[2]}</td>";
    echo "<td class='text-muted'>{$row[3]}</td>";
    echo "<td><a href='#'>{$row[4]}</a></td>";
    echo "<td>{$row[5]}</td>";
    echo "<td><a href='#'>{$row[6]}</a></td>";
    echo "</tr>";
}

echo "</tbody>";
echo "</table>";
echo "</div>";
echo "</div>";
echo "</div>";

// Pagination
echo "<div class='d-flex justify-content-between align-items-center mt-4'>";
echo "<div class='datatable-info'>";
echo "عرض <strong>1</strong> إلى <strong>8</strong> من <strong>23</strong> عنصر";
echo "</div>";
echo "<nav>";
echo "<ul class='pagination'>";
echo "<li class='page-item disabled'>";
echo "<a class='page-link' href='#'><i class='fas fa-chevron-right'></i></a>";
echo "</li>";
echo "<li class='page-item'><a class='page-link' href='#'>3</a></li>";
echo "<li class='page-item'><a class='page-link' href='#'>2</a></li>";
echo "<li class='page-item active'><a class='page-link' href='#'>1</a></li>";
echo "<li class='page-item'><a class='page-link' href='#'><i class='fas fa-chevron-left'></i></a></li>";
echo "</ul>";
echo "</nav>";
echo "<div class='text-muted small'>";
echo "عرض جميع العناصر من 23-8";
echo "</div>";
echo "</div>";

echo "<div class='alert alert-info mt-4'>";
echo "<h5><i class='fas fa-info-circle'></i> مقارنة الأنظمة:</h5>";
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h6>❌ مع Bootstrap:</h6>";
echo "<ul>";
echo "<li>حجم كبير (150KB+)</li>";
echo "<li>تعارض مع الثيم الداكن</li>";
echo "<li>صعوبة التخصيص</li>";
echo "<li>أنماط غير مرغوبة</li>";
echo "</ul>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<h6>✅ بدون Bootstrap:</h6>";
echo "<ul>";
echo "<li>حجم صغير (30KB)</li>";
echo "<li>ثيم داكن مثالي</li>";
echo "<li>تخصيص كامل</li>";
echo "<li>أنماط نظيفة</li>";
echo "</ul>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "</div>";

echo "<script>";
echo "// إضافة أيقونات الفرز";
echo "document.querySelectorAll('th[data-sortable=\"true\"]').forEach((th, index) => {";
echo "    th.style.cursor = 'pointer';";
echo "    th.style.position = 'relative';";
echo "    th.innerHTML += ' <span class=\"sort-icon\"><i class=\"fas fa-sort\"></i></span>';";
echo "});";

echo "// تبديل الثيم";
echo "document.getElementById('themeToggle').addEventListener('click', function() {";
echo "    const body = document.body;";
echo "    const isDark = body.classList.contains('dark-theme');";
echo "    ";
echo "    if (isDark) {";
echo "        body.classList.remove('dark-theme');";
echo "        this.innerHTML = '<i class=\"fas fa-moon\"></i> تبديل إلى الثيم الداكن';";
echo "        this.className = 'btn btn-primary me-2';";
echo "    } else {";
echo "        body.classList.add('dark-theme');";
echo "        this.innerHTML = '<i class=\"fas fa-sun\"></i> تبديل إلى الثيم الفاتح';";
echo "        this.className = 'btn btn-warning me-2';";
echo "    }";
echo "});";

echo "// محاكاة الفرز";
echo "document.querySelectorAll('th[data-sortable=\"true\"]').forEach(th => {";
echo "    th.addEventListener('click', function() {";
echo "        document.querySelectorAll('th[data-sortable=\"true\"]').forEach(header => {";
echo "            header.classList.remove('sorted-asc', 'sorted-desc');";
echo "            const icon = header.querySelector('.sort-icon i');";
echo "            if (icon) icon.className = 'fas fa-sort';";
echo "        });";
echo "        ";
echo "        const icon = this.querySelector('.sort-icon i');";
echo "        if (icon.classList.contains('fa-sort')) {";
echo "            icon.className = 'fas fa-sort-up';";
echo "            this.classList.add('sorted-asc');";
echo "        } else if (icon.classList.contains('fa-sort-up')) {";
echo "            icon.className = 'fas fa-sort-down';";
echo "            this.classList.remove('sorted-asc');";
echo "            this.classList.add('sorted-desc');";
echo "        } else {";
echo "            icon.className = 'fas fa-sort';";
echo "            this.classList.remove('sorted-desc');";
echo "        }";
echo "    });";
echo "});";
echo "</script>";

echo "</body>";
echo "</html>";
?>
