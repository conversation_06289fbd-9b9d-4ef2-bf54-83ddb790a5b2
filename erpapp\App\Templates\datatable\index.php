<?php
/**
 * Template موحد لصفحات الجداول
 * يستخدم في جميع الوحدات لتجنب التكرار
 */

// التحقق من وجود البيانات المطلوبة
$required_vars = ['title', 'data', 'columns', 'module_name', 'entity_name'];
foreach ($required_vars as $var) {
    if (!isset($$var)) {
        throw new Exception("Missing required variable: $var");
    }
}

// إعداد المتغيرات الافتراضية
$breadcrumb = $breadcrumb ?? [];
$stats = $stats ?? [];
$filters_config = $filters_config ?? [];
$actions = $actions ?? [];
$pagination = $pagination ?? [];
$filters = $filters ?? [];

// تضمين ملفات CSS و JS
$css_files = [
    'modules/datatable.css',
    'modules/filters.css',
    'modules/pagination.css'
];

$js_files = [
    'modules/datatable.js',
    'modules/filters.js',
    'modules/pagination.js'
];
?>

<!DOCTYPE html>
<html>
<head>
    <title><?= $title ?></title>
    
    <!-- CSS Files -->
    <?php foreach ($css_files as $css_file): ?>
        <link rel="stylesheet" href="<?= base_url('public/css/' . $css_file) ?>">
    <?php endforeach; ?>
    
    <!-- Custom CSS للوحدة -->
    <?php if (isset($custom_css)): ?>
        <?php foreach ($custom_css as $css_file): ?>
            <link rel="stylesheet" href="<?= base_url('public/css/' . $css_file) ?>">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body>

<div class="container-fluid">
    
    <!-- Breadcrumb -->
    <?php if (!empty($breadcrumb)): ?>
        <?php include __DIR__ . '/../Components/breadcrumb.php'; ?>
    <?php endif; ?>
    
    <!-- Page Header -->
    <div class="row">
        <div class="col-12">
            <?php include __DIR__ . '/../Components/page-header.php'; ?>
        </div>
    </div>
    
    <!-- Stats Cards -->
    <?php if (!empty($stats)): ?>
        <div class="row mb-3">
            <?php include __DIR__ . '/../Components/stats-cards.php'; ?>
        </div>
    <?php endif; ?>
    
    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    
                    <!-- Toolbar -->
                    <div class="row mb-2">
                        <div class="col-sm-5">
                            <!-- Actions (Add, Export, etc.) -->
                            <?php if (!empty($actions)): ?>
                                <?php include __DIR__ . '/actions.php'; ?>
                            <?php endif; ?>
                        </div>
                        <div class="col-sm-7">
                            <div class="text-sm-end">
                                <div class="row g-2">
                                    
                                    <!-- Per Page Selector -->
                                    <div class="col-auto">
                                        <div class="d-inline-block">
                                            <select class="form-select form-select-sm" 
                                                    onchange="DataTable.changePerPage(this.value)"
                                                    data-module="<?= $module_name ?>"
                                                    data-entity="<?= $entity_name ?>">
                                                <option value="10" <?= ($filters['per_page'] ?? 20) == 10 ? 'selected' : '' ?>>10 عناصر</option>
                                                <option value="20" <?= ($filters['per_page'] ?? 20) == 20 ? 'selected' : '' ?>>20 عنصر</option>
                                                <option value="50" <?= ($filters['per_page'] ?? 20) == 50 ? 'selected' : '' ?>>50 عنصر</option>
                                                <option value="100" <?= ($filters['per_page'] ?? 20) == 100 ? 'selected' : '' ?>>100 عنصر</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <!-- Filters Button -->
                                    <?php if (!empty($filters_config)): ?>
                                        <div class="col-auto">
                                            <button type="button" class="btn btn-outline-primary btn-sm"
                                                    data-toggle="modal" data-target="#filtersModal">
                                                <i class="mdi mdi-filter-variant me-1"></i> فلاتر
                                                <?php
                                                $activeFilters = count_active_filters_with_saved_check($entity_name, $filters);
                                                if ($activeFilters > 0): ?>
                                                    <span class="badge bg-danger ms-1"><?= $activeFilters ?></span>
                                                <?php endif; ?>
                                            </button>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <!-- Clear Filters Button -->
                                    <?php if (!empty($filters_config) && $activeFilters > 0): ?>
                                        <div class="col-auto">
                                            <a href="<?= base_url($module_name . '/' . $entity_name . '/clear-filters') ?>" 
                                               class="btn btn-outline-secondary btn-sm">
                                                <i class="mdi mdi-filter-remove me-1"></i> مسح الفلاتر
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                    
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Data Table -->
                    <div class="table-responsive" id="dataTableContainer">
                        <?php include __DIR__ . '/table.php'; ?>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if (!empty($pagination) && $pagination['total_pages'] > 1): ?>
                        <div class="row mt-3">
                            <div class="col-sm-6">
                                <div class="dataTables_info count-info">
                                    عرض <?= $pagination['start_item'] ?> إلى <?= $pagination['end_item'] ?> 
                                    من أصل <?= $pagination['total_items'] ?> عنصر
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="dataTables_paginate paging_simple_numbers float-end pagination-container">
                                    <?php include __DIR__ . '/pagination.php'; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                </div>
            </div>
        </div>
    </div>
    
</div>

<!-- Filters Modal -->
<?php if (!empty($filters_config)): ?>
    <?php include __DIR__ . '/filters.php'; ?>
<?php endif; ?>

<!-- Delete Confirmation Modal -->
<div class="modal confirmation-modal" id="deleteModal" tabindex="-1" style="display: none;">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="modal-close" data-dismiss="modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من حذف هذا العنصر؟ لا يمكن التراجع عن هذا الإجراء.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" class="d-inline">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript Files -->
<?php foreach ($js_files as $js_file): ?>
    <script src="<?= base_url('public/js/' . $js_file) ?>"></script>
<?php endforeach; ?>

<!-- Custom JS للوحدة -->
<?php if (isset($custom_js)): ?>
    <?php foreach ($custom_js as $js_file): ?>
        <script src="<?= base_url('public/js/' . $js_file) ?>"></script>
    <?php endforeach; ?>
<?php endif; ?>

<!-- Initialize DataTable -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    DataTable.init({
        module: '<?= $module_name ?>',
        entity: '<?= $entity_name ?>',
        filters: <?= json_encode($filters) ?>,
        columns: <?= json_encode($columns) ?>
    });
});
</script>

<!-- Custom JavaScript للصفحة -->
<?php if (isset($custom_script)): ?>
    <script><?= $custom_script ?></script>
<?php endif; ?>

</body>
</html>
