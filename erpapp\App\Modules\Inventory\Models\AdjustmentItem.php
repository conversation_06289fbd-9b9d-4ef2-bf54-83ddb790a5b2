<?php

namespace App\Modules\Inventory\Models;

use App\Core\Model;

class AdjustmentItem extends Model
{
    protected $table = 'inventory_adjustment_items';
    protected $primaryKey = 'adjustment_item_id';
    
    protected $fillable = [
        'company_id',
        'module_code',
        'adjustment_id',
        'product_id',
        'current_quantity',
        'adjusted_quantity',
        'difference_quantity',
        'unit_cost',
        'total_cost',
        'notes',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'current_quantity' => 'decimal:3',
        'adjusted_quantity' => 'decimal:3',
        'difference_quantity' => 'decimal:3',
        'unit_cost' => 'decimal:2',
        'total_cost' => 'decimal:2'
    ];

    protected $dates = [
        'created_at',
        'updated_at'
    ];

    // العلاقات
    public function company()
    {
        return $this->belongsTo('App\Models\Company', 'company_id', 'CompanyID');
    }

    public function adjustment()
    {
        return $this->belongsTo('App\Modules\Inventory\Models\Adjustment', 'adjustment_id', 'adjustment_id');
    }

    public function product()
    {
        return $this->belongsTo('App\Modules\Inventory\Models\Product', 'product_id', 'product_id');
    }

    public function createdBy()
    {
        return $this->belongsTo('App\Models\User', 'created_by', 'UserID');
    }

    public function updatedBy()
    {
        return $this->belongsTo('App\Models\User', 'updated_by', 'UserID');
    }

    // الدوال المساعدة
    public function isIncrease()
    {
        return $this->difference_quantity > 0;
    }

    public function isDecrease()
    {
        return $this->difference_quantity < 0;
    }

    public function getAdjustmentTypeText()
    {
        if ($this->isIncrease()) {
            return 'زيادة';
        } elseif ($this->isDecrease()) {
            return 'نقص';
        } else {
            return 'بدون تغيير';
        }
    }

    public function getAdjustmentTypeColor()
    {
        if ($this->isIncrease()) {
            return 'success';
        } elseif ($this->isDecrease()) {
            return 'danger';
        } else {
            return 'secondary';
        }
    }

    public function getAdjustmentIcon()
    {
        if ($this->isIncrease()) {
            return 'fas fa-plus-circle';
        } elseif ($this->isDecrease()) {
            return 'fas fa-minus-circle';
        } else {
            return 'fas fa-equals';
        }
    }

    public function getAbsoluteDifference()
    {
        return abs($this->difference_quantity);
    }

    public function getFormattedDifference()
    {
        if ($this->difference_quantity > 0) {
            return '+' . number_format($this->difference_quantity, 3);
        } elseif ($this->difference_quantity < 0) {
            return number_format($this->difference_quantity, 3);
        } else {
            return '0.000';
        }
    }

    public function applyToStock()
    {
        if ($this->difference_quantity == 0) {
            return false;
        }

        $stock = Stock::where('company_id', $this->company_id)
                     ->where('product_id', $this->product_id)
                     ->where('warehouse_id', $this->adjustment->warehouse_id)
                     ->first();

        if (!$stock) {
            // إنشاء سجل مخزون جديد إذا لم يكن موجوداً
            $stock = new Stock([
                'company_id' => $this->company_id,
                'product_id' => $this->product_id,
                'warehouse_id' => $this->adjustment->warehouse_id,
                'quantity_on_hand' => 0,
                'quantity_reserved' => 0,
                'quantity_available' => 0,
                'average_cost' => 0,
                'last_cost' => 0
            ]);
        }

        if ($this->isIncrease()) {
            $stock->addStock($this->difference_quantity, $this->unit_cost);
        } elseif ($this->isDecrease()) {
            $stock->removeStock(abs($this->difference_quantity));
        }

        return true;
    }

    // النطاقات (Scopes)
    public function scopeByCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    public function scopeByAdjustment($query, $adjustmentId)
    {
        return $query->where('adjustment_id', $adjustmentId);
    }

    public function scopeByProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    public function scopeIncreases($query)
    {
        return $query->where('difference_quantity', '>', 0);
    }

    public function scopeDecreases($query)
    {
        return $query->where('difference_quantity', '<', 0);
    }

    public function scopeNoChange($query)
    {
        return $query->where('difference_quantity', '=', 0);
    }

    // التحقق من الصحة
    public static function getValidationRules($id = null)
    {
        return [
            'adjustment_id' => 'required|exists:inventory_adjustments,adjustment_id',
            'product_id' => 'required|exists:inventory_products,product_id',
            'current_quantity' => 'required|numeric|min:0',
            'adjusted_quantity' => 'required|numeric|min:0',
            'unit_cost' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string|max:1000'
        ];
    }

    public static function getValidationMessages()
    {
        return [
            'adjustment_id.required' => 'التسوية مطلوبة',
            'adjustment_id.exists' => 'التسوية المحددة غير موجودة',
            'product_id.required' => 'المنتج مطلوب',
            'product_id.exists' => 'المنتج المحدد غير موجود',
            'current_quantity.required' => 'الكمية الحالية مطلوبة',
            'current_quantity.numeric' => 'الكمية الحالية يجب أن تكون رقماً',
            'current_quantity.min' => 'الكمية الحالية يجب أن تكون أكبر من أو تساوي صفر',
            'adjusted_quantity.required' => 'الكمية المعدلة مطلوبة',
            'adjusted_quantity.numeric' => 'الكمية المعدلة يجب أن تكون رقماً',
            'adjusted_quantity.min' => 'الكمية المعدلة يجب أن تكون أكبر من أو تساوي صفر',
            'unit_cost.numeric' => 'تكلفة الوحدة يجب أن تكون رقماً',
            'unit_cost.min' => 'تكلفة الوحدة يجب أن تكون أكبر من أو تساوي صفر'
        ];
    }

    // الأحداث
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->company_id = session('company_id');
            $model->module_code = 'inventory';
            $model->created_by = auth()->id();
            
            // حساب الفرق والتكلفة الإجمالية
            $model->difference_quantity = $model->adjusted_quantity - $model->current_quantity;
            
            if ($model->unit_cost && $model->difference_quantity != 0) {
                $model->total_cost = abs($model->difference_quantity) * $model->unit_cost;
            } else {
                $model->total_cost = 0;
            }
        });

        static::updating(function ($model) {
            $model->updated_by = auth()->id();
            
            // إعادة حساب الفرق والتكلفة الإجمالية
            $model->difference_quantity = $model->adjusted_quantity - $model->current_quantity;
            
            if ($model->unit_cost && $model->difference_quantity != 0) {
                $model->total_cost = abs($model->difference_quantity) * $model->unit_cost;
            } else {
                $model->total_cost = 0;
            }
        });

        static::saved(function ($model) {
            // إعادة حساب إجماليات التسوية
            if ($model->adjustment) {
                $model->adjustment->calculateTotals();
            }
        });

        static::deleted(function ($model) {
            // إعادة حساب إجماليات التسوية
            if ($model->adjustment) {
                $model->adjustment->calculateTotals();
            }
        });
    }

    // دوال مساعدة إضافية
    public function getVariancePercentage()
    {
        if ($this->current_quantity == 0) {
            return $this->adjusted_quantity > 0 ? 100 : 0;
        }
        
        return round((($this->adjusted_quantity - $this->current_quantity) / $this->current_quantity) * 100, 2);
    }

    public function isSignificantVariance($threshold = 10)
    {
        return abs($this->getVariancePercentage()) >= $threshold;
    }

    public function getCurrentStock()
    {
        return Stock::where('company_id', $this->company_id)
                   ->where('product_id', $this->product_id)
                   ->where('warehouse_id', $this->adjustment->warehouse_id)
                   ->first();
    }

    public function validateCurrentQuantity()
    {
        $stock = $this->getCurrentStock();
        
        if (!$stock) {
            return $this->current_quantity == 0;
        }
        
        return $this->current_quantity == $stock->quantity_on_hand;
    }

    public function updateFromCurrentStock()
    {
        $stock = $this->getCurrentStock();
        
        if ($stock) {
            $this->current_quantity = $stock->quantity_on_hand;
            $this->difference_quantity = $this->adjusted_quantity - $this->current_quantity;
            
            if ($this->unit_cost && $this->difference_quantity != 0) {
                $this->total_cost = abs($this->difference_quantity) * $this->unit_cost;
            } else {
                $this->total_cost = 0;
            }
            
            $this->save();
        }
    }

    // دوال للتقارير
    public static function getVarianceReport($adjustmentId)
    {
        return self::where('adjustment_id', $adjustmentId)
                  ->with(['product', 'product.unit'])
                  ->get()
                  ->map(function ($item) {
                      return [
                          'product_code' => $item->product->product_code,
                          'product_name' => $item->product->getDisplayName(),
                          'unit_name' => $item->product->unit->getDisplayName(),
                          'current_quantity' => $item->current_quantity,
                          'adjusted_quantity' => $item->adjusted_quantity,
                          'difference_quantity' => $item->difference_quantity,
                          'variance_percentage' => $item->getVariancePercentage(),
                          'unit_cost' => $item->unit_cost,
                          'total_cost' => $item->total_cost,
                          'adjustment_type' => $item->getAdjustmentTypeText()
                      ];
                  });
    }
}
