<?php
/**
 * اختبار ميزة الفرز
 */

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>ميزة الفرز - JavaScript فقط</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdn.jsdelivr.net/npm/@mdi/font@6.9.96/css/materialdesignicons.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";

echo "<div class='container mt-5'>";
echo "<h2>🔄 ميزة الفرز الجديدة - JavaScript فقط!</h2>";

echo "<div class='alert alert-success'>";
echo "<h5>✅ الميزات المضافة:</h5>";
echo "<ul>";
echo "<li><strong>فرز تفاعلي:</strong> النقر على رأس العمود لفرز البيانات</li>";
echo "<li><strong>أيقونات ديناميكية:</strong> تظهر اتجاه الفرز (تصاعدي/تنازلي)</li>";
echo "<li><strong>دعم أنواع البيانات:</strong> نص، أرقام، تواريخ</li>";
echo "<li><strong>دعم العربية:</strong> فرز صحيح للنصوص العربية</li>";
echo "<li><strong>بدون قاعدة بيانات:</strong> فرز محلي بـ JavaScript فقط</li>";
echo "</ul>";
echo "</div>";

echo "<div class='alert alert-info'>";
echo "<h5>🔧 كيفية الاستخدام:</h5>";
echo "<ol>";
echo "<li><strong>إضافة خاصية الفرز للعمود:</strong></li>";
echo "<pre><code>";
echo "[\n";
echo "    'field' => 'entity_number',\n";
echo "    'title' => 'الرقم',\n";
echo "    'sortable' => true,        // تفعيل الفرز\n";
echo "    'data_type' => 'number'    // نوع البيانات\n";
echo "]";
echo "</code></pre>";

echo "<li><strong>أنواع البيانات المدعومة:</strong></li>";
echo "<ul>";
echo "<li><code>text</code> - نصوص (افتراضي)</li>";
echo "<li><code>number</code> - أرقام</li>";
echo "<li><code>date</code> - تواريخ</li>";
echo "</ul>";

echo "<li><strong>النقر على رأس العمود:</strong> يتم الفرز تلقائياً</li>";
echo "<li><strong>النقر مرة أخرى:</strong> يعكس اتجاه الفرز</li>";
echo "</ol>";
echo "</div>";

echo "<div class='alert alert-warning'>";
echo "<h5>🎯 الأعمدة القابلة للفرز:</h5>";
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h6>صفحة الموردين:</h6>";
echo "<ul class='list-unstyled'>";
echo "<li>✅ الرقم (number)</li>";
echo "<li>✅ اسم المورد (text)</li>";
echo "<li>✅ اسم الشركة (text)</li>";
echo "<li>✅ المجموعة (text)</li>";
echo "<li>✅ الهاتف (text)</li>";
echo "<li>✅ الحالة (text)</li>";
echo "</ul>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<h6>صفحة مجموعات الموردين:</h6>";
echo "<ul class='list-unstyled'>";
echo "<li>✅ الرقم (number)</li>";
echo "<li>✅ اسم المجموعة (text)</li>";
echo "<li>✅ النوع (text)</li>";
echo "<li>✅ تاريخ الإنشاء (date)</li>";
echo "</ul>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='alert alert-primary'>";
echo "<h5>⚡ المزايا:</h5>";
echo "<ul>";
echo "<li><strong>سرعة فائقة:</strong> فرز فوري بدون انتظار الخادم</li>";
echo "<li><strong>لا يؤثر على الفلاتر:</strong> الفرز محلي فقط</li>";
echo "<li><strong>سهولة الاستخدام:</strong> نقرة واحدة للفرز</li>";
echo "<li><strong>أيقونات واضحة:</strong> تظهر اتجاه الفرز الحالي</li>";
echo "<li><strong>دعم شامل:</strong> جميع أنواع البيانات</li>";
echo "<li><strong>متوافق مع النظام:</strong> يعمل مع النظام الموحد</li>";
echo "</ul>";
echo "</div>";

echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<div class='card'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h6>🔄 أيقونات الفرز</h6>";
echo "</div>";
echo "<div class='card-body'>";
echo "<ul class='list-unstyled'>";
echo "<li><i class='mdi mdi-sort text-muted'></i> غير مفروز</li>";
echo "<li><i class='mdi mdi-sort-ascending text-primary'></i> تصاعدي (A-Z, 1-9)</li>";
echo "<li><i class='mdi mdi-sort-descending text-primary'></i> تنازلي (Z-A, 9-1)</li>";
echo "</ul>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<div class='card'>";
echo "<div class='card-header bg-success text-white'>";
echo "<h6>📝 مثال على الكود</h6>";
echo "</div>";
echo "<div class='card-body'>";
echo "<pre><code class='small'>";
echo "// في ملف index.php\n";
echo "[\n";
echo "  'field' => 'name_ar',\n";
echo "  'title' => 'الاسم',\n";
echo "  'sortable' => true,\n";
echo "  'data_type' => 'text'\n";
echo "]";
echo "</code></pre>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='text-center mt-4'>";
echo "<a href='purchases/suppliers' class='btn btn-primary btn-lg me-2'>";
echo "<i class='mdi mdi-truck me-1'></i> اختبار فرز الموردين";
echo "</a>";
echo "<a href='purchases/supplier-groups' class='btn btn-success btn-lg'>";
echo "<i class='mdi mdi-folder-multiple me-1'></i> اختبار فرز المجموعات";
echo "</a>";
echo "</div>";

echo "<div class='alert alert-light mt-4'>";
echo "<h6>💡 نصائح للاستخدام:</h6>";
echo "<ul class='mb-0'>";
echo "<li>انقر على رأس أي عمود لفرز البيانات</li>";
echo "<li>انقر مرة أخرى لعكس اتجاه الفرز</li>";
echo "<li>الفرز يعمل على البيانات المعروضة في الصفحة الحالية فقط</li>";
echo "<li>الفلاتر والبحث لا يتأثران بالفرز</li>";
echo "</ul>";
echo "</div>";

echo "</div>";

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>";
echo "</body>";
echo "</html>";
?>
