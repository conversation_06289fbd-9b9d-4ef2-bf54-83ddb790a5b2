<?php
namespace App\System\Subscriptions\Controllers;

use App\System\Subscriptions\Models\Subscription;
use App\System\Subscriptions\Models\SubscriptionPlan;
use App\System\Subscriptions\Services\CompanyService;

/**
 * متحكم الاشتراكات
 */
class SubscriptionController {
    /**
     * معلمات المسار
     *
     * @var array
     */
    protected $params = [];

    /**
     * نموذج الاشتراك
     *
     * @var Subscription
     */
    protected $subscriptionModel;

    /**
     * نموذج خطة الاشتراك
     *
     * @var SubscriptionPlan
     */
    protected $planModel;

    /**
     * خدمة الشركات
     *
     * @var CompanyService
     */
    protected $companyService;

    /**
     * اتصال قاعدة البيانات
     *
     * @var \PDO
     */
    protected $db;

    /**
     * المُنشئ
     *
     * @param array $params معلمات المسار
     */
    public function __construct($params = []) {
        global $db;
        $this->params = $params;
        $this->db = $db;
        $this->subscriptionModel = new Subscription();
        $this->planModel = new SubscriptionPlan();
        $this->companyService = new CompanyService();

        // التحقق من تسجيل الدخول
        if (!is_logged_in()) {
            redirect(base_url('login'));
        }
    }

    /**
     * عرض قائمة الاشتراكات
     *
     * @return void
     */
    public function index() {
        $user_id = current_user_id();

        // الحصول على الفلاتر من قاعدة البيانات (متوافق مع سياستنا)
        $default_filters = [
            'company_id' => '',
            'status' => '',
            'plan_id' => '',
            'date_from' => '',
            'date_to' => '',
            'per_page' => 20,
            'current_page' => 1
        ];

        // الحصول على الفلاتر مع دمج الفلاتر المحفوظة
        $applied_filters = prepare_filters('subscriptions', $default_filters);

        $company_filter = $applied_filters['company_id'] ?? '';
        $status_filter = $applied_filters['status'] ?? '';
        $plan_filter = $applied_filters['plan_id'] ?? '';
        $date_from = $applied_filters['date_from'] ?? '';
        $date_to = $applied_filters['date_to'] ?? '';

        // الحصول على جميع اشتراكات الشركات التي يملكها المستخدم مع الفلترة
        $subscriptions = $this->subscriptionModel->getAllUserOwnedCompaniesSubscriptions($user_id, [
            'company_id' => $company_filter,
            'status' => $status_filter,
            'plan_id' => $plan_filter,
            'date_from' => $date_from,
            'date_to' => $date_to
        ]);

        // الحصول على قائمة الشركات التي يملكها المستخدم للفلترة
        $user_companies = $this->companyService->getUserOwnedCompanies($user_id);

        // الحصول على قائمة خطط الاشتراك للفلترة
        $plans = $this->planModel->getAllActivePlans();

        view('Subscriptions::index', [
            'title' => __('الاشتراكات'),
            'subscriptions' => $subscriptions,
            'user_companies' => $user_companies,
            'plans' => $plans,
            'filters' => [
                'company_id' => $company_filter,
                'status' => $status_filter,
                'plan_id' => $plan_filter,
                'date_from' => $date_from,
                'date_to' => $date_to
            ]
        ]);
    }

    /**
     * عرض صفحة خطط الاشتراك
     *
     * @return void
     */
    public function plans() {
        $user_id = current_user_id();

        // الحصول على معرف الشركة من الفلاتر المحفوظة (متوافق مع سياستنا)
        $filters = prepare_filters('subscription_plans', ['company_id' => '']);
        $selected_company_id = $filters['company_id'] ?: null;

        // الحصول على خطط الاشتراك النشطة
        $plans = $this->planModel->getAllActivePlans();

        // الحصول على الشركات التي يملكها المستخدم
        $user_companies = $this->companyService->getUserOwnedCompanies($user_id);

        // التحقق من أن الشركة المحددة تنتمي للمستخدم
        if ($selected_company_id) {
            $company_exists = false;
            foreach ($user_companies as $company) {
                if ($company['CompanyID'] == $selected_company_id) {
                    $company_exists = true;
                    break;
                }
            }

            // إذا لم تكن الشركة تنتمي للمستخدم، قم بإلغاء التحديد
            if (!$company_exists) {
                $selected_company_id = null;
            }
        }

        view('Subscriptions::plans', [
            'title' => __('خطط الاشتراك'),
            'plans' => $plans,
            'user_companies' => $user_companies,
            'selected_company_id' => $selected_company_id
        ]);
    }

    /**
     * عرض صفحة مقارنة خطط الاشتراك
     *
     * @return void
     */
    public function comparePlans() {
        $user_id = current_user_id();

        // الحصول على خطط الاشتراك النشطة
        $plans = $this->planModel->getAllActivePlans();

        // الحصول على الشركات التي يملكها المستخدم
        $user_companies = $this->companyService->getUserOwnedCompanies($user_id);

        // التحقق من وجود شركات للمستخدم
        if (empty($user_companies)) {
            flash('subscription_error', 'يجب إنشاء شركة أولاً قبل الاشتراك', 'danger');
            redirect(base_url('companies'));
        }

        view('Subscriptions::compare', [
            'title' => __('مقارنة خطط الاشتراك'),
            'plans' => $plans,
            'user_companies' => $user_companies
        ]);
    }

    /**
     * عرض تفاصيل خطة اشتراك
     *
     * @return void
     */
    public function showPlan() {
        $plan_id = $this->params['id'] ?? null;

        if (!$plan_id) {
            flash('subscription_error', 'خطة الاشتراك غير موجودة', 'danger');
            redirect(base_url('subscriptions/plans'));
        }

        // الحصول على تفاصيل خطة الاشتراك
        $plan = $this->planModel->getPlanById($plan_id);

        if (!$plan) {
            flash('subscription_error', 'خطة الاشتراك غير موجودة', 'danger');
            redirect(base_url('subscriptions/plans'));
        }

        // الحصول على ميزات خطة الاشتراك
        $features = $this->planModel->getPlanFeatures($plan_id);

        view('Subscriptions::plan_details', [
            'title' => $plan['plan_name_ar'],
            'plan' => $plan,
            'features' => $features
        ]);
    }

    /**
     * اشتراك في خطة
     *
     * @return void
     */
    public function subscribe() {
        // التحقق من أن الطلب هو POST
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect('/erpapp/subscriptions/plans');
        }

        // التحقق من CSRF token
        if (!csrf_check($_POST['csrf_token'] ?? '')) {
            flash('subscription_error', 'خطأ في التحقق من الأمان. يرجى المحاولة مرة أخرى.', 'danger');
            redirect('/erpapp/subscriptions/plans');
        }

        $plan_id = $_POST['plan_id'] ?? null;
        $company_id = $_POST['company_id'] ?? null;
        $billing_cycle = $_POST['billing_cycle'] ?? 'monthly';
        $payment_method = $_POST['payment_method'] ?? 'credit_card';

        if (!$plan_id || !$company_id) {
            flash('subscription_error', 'بيانات غير صالحة. يرجى المحاولة مرة أخرى.', 'danger');
            redirect('/erpapp/subscriptions/plans');
        }

        // الحصول على تفاصيل خطة الاشتراك
        $plan = $this->planModel->getPlanById($plan_id);

        if (!$plan) {
            flash('subscription_error', 'خطة الاشتراك غير موجودة', 'danger');
            redirect('/erpapp/subscriptions/plans');
        }

        // حساب تاريخ البدء والانتهاء
        $start_date = date('Y-m-d H:i:s');
        $end_date = $billing_cycle === 'yearly'
            ? date('Y-m-d H:i:s', strtotime('+1 year'))
            : date('Y-m-d H:i:s', strtotime('+1 month'));

        // إنشاء الاشتراك
        $subscription_data = [
            'company_id' => $company_id,
            'plan_id' => $plan_id,
            'start_date' => $start_date,
            'end_date' => $end_date,
            'status' => 'pending', // حالة الاشتراك معلقة حتى يتم الدفع
            'is_auto_renew' => isset($_POST['auto_renew']) ? 1 : 0,
            'billing_cycle' => $billing_cycle,
            'payment_method' => $payment_method
        ];

        $subscription_id = $this->subscriptionModel->createSubscription($subscription_data);

        if ($subscription_id) {
            // توجيه المستخدم إلى صفحة الدفع
            redirect('/erpapp/payments/checkout/' . $subscription_id);
        } else {
            flash('subscription_error', 'حدث خطأ أثناء الاشتراك. يرجى المحاولة مرة أخرى.', 'danger');
            redirect('/erpapp/subscriptions/plans');
        }
    }

    /**
     * عرض صفحة الدفع
     *
     * @return void
     */
    public function checkout() {
        $subscription_id = $this->params['id'] ?? null;

        if (!$subscription_id) {
            flash('payment_error', 'معرف الاشتراك غير صالح', 'danger');
            redirect('/erpapp/subscriptions/plans');
        }

        // الحصول على تفاصيل الاشتراك
        $subscription = $this->subscriptionModel->getById($subscription_id);

        if (!$subscription) {
            flash('payment_error', 'الاشتراك غير موجود', 'danger');
            redirect('/erpapp/subscriptions/plans');
        }

        // الحصول على تفاصيل خطة الاشتراك
        $plan = $this->planModel->getPlanById($subscription['plan_id']);

        // الحصول على تفاصيل الدفع
        $payment = $this->subscriptionModel->getPaymentBySubscriptionId($subscription_id);

        // الحصول على معلومات الشركة
        $company = $this->companyService->getCompanyBasicInfo($subscription['company_id']);

        view('Subscriptions::checkout', [
            'title' => __('إتمام عملية الدفع'),
            'subscription' => $subscription,
            'plan' => $plan,
            'payment' => $payment,
            'company' => $company
        ]);
    }



    /**
     * معالجة الدفع
     *
     * @return void
     */
    public function processPayment() {
        // التحقق من أن الطلب هو POST
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect('/erpapp/subscriptions/plans');
        }

        // التحقق من CSRF token
        if (!csrf_check($_POST['csrf_token'] ?? '')) {
            flash('payment_error', 'خطأ في التحقق من الأمان. يرجى المحاولة مرة أخرى.', 'danger');
            redirect('/erpapp/subscriptions/plans');
        }

        $subscription_id = $_POST['subscription_id'] ?? null;
        $payment_id = $_POST['payment_id'] ?? null;

        if (!$subscription_id || !$payment_id) {
            flash('payment_error', 'بيانات غير صالحة. يرجى المحاولة مرة أخرى.', 'danger');
            redirect('/erpapp/subscriptions/plans');
        }

        // التحقق من بيانات الدفع
        $cardNumber = $_POST['cardNumber'] ?? '';
        $expiryDate = $_POST['expiryDate'] ?? '';
        $cvv = $_POST['cvv'] ?? '';
        $cardName = $_POST['cardName'] ?? '';
        $payment_method = $_POST['payment_method'] ?? 'credit_card';

        // التحقق من بيانات البطاقة إذا كانت طريقة الدفع هي بطاقة ائتمان أو مدى
        if (($payment_method === 'credit_card' || $payment_method === 'mada') &&
            (empty($cardNumber) || empty($expiryDate) || empty($cvv) || empty($cardName))) {
            flash('payment_error', 'يرجى إدخال جميع بيانات البطاقة المطلوبة.', 'danger');
            redirect('/erpapp/payments/checkout/' . $subscription_id);
        }

        // الحصول على معلومات الاشتراك والخطة
        $subscription = $this->subscriptionModel->getById($subscription_id);
        $plan = $this->planModel->getPlanById($subscription['plan_id']);

        // الحصول على معلومات المستخدم
        $user = current_user();

        // تهيئة نموذج بوابة الدفع
        $paymentGateway = new \App\System\Subscriptions\Models\PaymentGateway();

        if ($payment_method === 'credit_card' || $payment_method === 'mada') {
            // التحقق من رقم البطاقة (يجب أن يكون 16 رقم بدون مسافات)
            $cardNumberClean = str_replace(' ', '', $cardNumber);
            if (strlen($cardNumberClean) !== 16 || !is_numeric($cardNumberClean)) {
                flash('payment_error', 'رقم البطاقة غير صالح. يجب أن يتكون من 16 رقم.', 'danger');
                redirect('/erpapp/payments/checkout/' . $subscription_id);
            }

            // التحقق من تاريخ الانتهاء (يجب أن يكون بتنسيق MM/YY)
            if (!preg_match('/^(0[1-9]|1[0-2])\/([0-9]{2})$/', $expiryDate)) {
                flash('payment_error', 'تاريخ انتهاء البطاقة غير صالح. يجب أن يكون بتنسيق MM/YY.', 'danger');
                redirect('/erpapp/payments/checkout/' . $subscription_id);
            }

            // التحقق من رمز الأمان (يجب أن يكون 3 أرقام)
            if (strlen($cvv) !== 3 || !is_numeric($cvv)) {
                flash('payment_error', 'رمز الأمان غير صالح. يجب أن يتكون من 3 أرقام.', 'danger');
                redirect('/erpapp/payments/checkout/' . $subscription_id);
            }

            // التحقق من اسم حامل البطاقة
            if (strlen($cardName) < 3) {
                flash('payment_error', 'اسم حامل البطاقة غير صالح.', 'danger');
                redirect('/erpapp/payments/checkout/' . $subscription_id);
            }

            // إعداد بيانات الدفع
            $amount = $subscription['billing_cycle'] === 'yearly' ? $plan['price_yearly'] : $plan['price_monthly'];

            // إنشاء رقم مرجعي فريد للدفع (لن نستخدمه في قاعدة البيانات)
            $referenceNo = 'SUB-' . date('Ymd') . '-' . $subscription_id;

            // تحضير بيانات الدفع لإرسالها إلى بوابة الدفع
            $paymentData = [
                'title' => 'اشتراك في خطة ' . $plan['plan_name_ar'],
                'first_name' => $user['FirstName'],
                'last_name' => $user['LastName'],
                'phone' => $user['PhoneNumber'],
                'email' => $user['Email'],
                'product_name' => 'اشتراك في خطة ' . $plan['plan_name_ar'] . ' (' . ($subscription['billing_cycle'] === 'yearly' ? 'سنوي' : 'شهري') . ')',
                'amount' => $amount,
                'reference_no' => $referenceNo,
                'address' => '',
                'city' => '',
                'state' => '',
                'postal_code' => ''
            ];

            try {
                // إنشاء معاملة دفع جديدة
                $paymentResult = $paymentGateway->createPayment($paymentData);

                // تسجيل نتيجة إنشاء معاملة الدفع للتشخيص
                error_log('PayTabs Payment Result: ' . json_encode($paymentResult));

                // طباعة الاستجابة كاملة للتشخيص
                error_log('استجابة PayTabs كاملة: ' . json_encode($paymentResult));

                // التحقق من نجاح الطلب
                if (isset($paymentResult['redirect_url']) && !empty($paymentResult['redirect_url'])) {
                    // تحديث معرف المعاملة في قاعدة البيانات
                    $transactionId = $paymentResult['tran_ref'] ?? ($paymentResult['transaction_id'] ?? null);

                    $stmt = $this->db->prepare("
                        UPDATE payments
                        SET transaction_id = :transaction_id
                        WHERE payment_id = :payment_id
                    ");
                    $stmt->bindParam(':payment_id', $payment_id);
                    $stmt->bindParam(':transaction_id', $transactionId);
                    $stmt->execute();

                    // طباعة عنوان URL للتشخيص
                    error_log('توجيه المستخدم إلى: ' . $paymentResult['redirect_url']);

                    // إعادة توجيه المستخدم إلى صفحة الدفع الخاصة ببوابة الدفع
                    redirect($paymentResult['redirect_url']);
                    return;
                }
                // التحقق من وجود رابط الدفع (صيغة قديمة)
                else if (isset($paymentResult['payment_url']) && !empty($paymentResult['payment_url'])) {
                    // تحديث معرف المعاملة في قاعدة البيانات
                    $transactionId = $paymentResult['p_id'] ?? ($paymentResult['transaction_id'] ?? null);

                    $stmt = $this->db->prepare("
                        UPDATE payments
                        SET transaction_id = :transaction_id
                        WHERE payment_id = :payment_id
                    ");
                    $stmt->bindParam(':payment_id', $payment_id);
                    $stmt->bindParam(':transaction_id', $transactionId);
                    $stmt->execute();

                    // طباعة عنوان URL للتشخيص
                    error_log('توجيه المستخدم إلى: ' . $paymentResult['payment_url']);

                    // إعادة توجيه المستخدم إلى صفحة الدفع الخاصة ببوابة الدفع
                    redirect($paymentResult['payment_url']);
                    return;
                }
                // التحقق من وجود عنوان URL في مكان آخر في الاستجابة
                else if (isset($paymentResult['redirect_url']) && !empty($paymentResult['redirect_url'])) {
                    // تحديث معرف المعاملة في قاعدة البيانات
                    $transactionId = $paymentResult['tran_ref'] ?? ($paymentResult['transaction_id'] ?? null);

                    $stmt = $this->db->prepare("
                        UPDATE payments
                        SET transaction_id = :transaction_id
                        WHERE payment_id = :payment_id
                    ");
                    $stmt->bindParam(':payment_id', $payment_id);
                    $stmt->bindParam(':transaction_id', $transactionId);
                    $stmt->execute();

                    // طباعة عنوان URL للتشخيص
                    error_log('توجيه المستخدم إلى: ' . $paymentResult['redirect_url']);

                    // إعادة توجيه المستخدم إلى صفحة الدفع الخاصة ببوابة الدفع
                    redirect($paymentResult['redirect_url']);
                    return;
                }
                else {
                    // حدث خطأ في إنشاء معاملة الدفع
                    $errorMessage = isset($paymentResult['message']) ? $paymentResult['message'] :
                                   (isset($paymentResult['error']) ? $paymentResult['error'] :
                                   'حدث خطأ أثناء الاتصال ببوابة الدفع. يرجى المحاولة مرة أخرى.');

                    // إضافة معلومات إضافية للخطأ 401 (خطأ في المصادقة)
                    if (isset($paymentResult['http_code']) && $paymentResult['http_code'] == 401) {
                        $errorMessage = 'خطأ في المصادقة مع بوابة الدفع. يرجى التحقق من مفاتيح API.';

                        // تسجيل معلومات إضافية للتشخيص
                        error_log('خطأ مصادقة PayTabs (401): ' . json_encode($paymentResult));
                        error_log('مفاتيح API لـ PayTabs: profile_id=' . $paymentGateway->apiKeys['profile_id'] . ', server_key=' . substr($paymentGateway->apiKeys['server_key'], 0, 5) . '...');
                    }

                    // إضافة معلومات إضافية للأخطاء الأخرى
                    if (isset($paymentResult['response_text'])) {
                        error_log('نص استجابة PayTabs: ' . $paymentResult['response_text']);

                        // محاولة تحليل نص الاستجابة إذا كان JSON
                        $responseJson = json_decode($paymentResult['response_text'], true);
                        if ($responseJson && isset($responseJson['message'])) {
                            $errorMessage = $responseJson['message'];
                            error_log('رسالة خطأ PayTabs من JSON: ' . $errorMessage);
                        }
                    }

                    error_log('خطأ دفع PayTabs: ' . $errorMessage);
                    error_log('نتيجة دفع PayTabs: ' . json_encode($paymentResult));

                    flash('payment_error', $errorMessage, 'danger');
                    redirect('/erpapp/payments/checkout/' . $subscription_id);
                    return;
                }
            } catch (\Exception $e) {
                // حدث خطأ أثناء الاتصال ببوابة الدفع
                flash('payment_error', 'حدث خطأ أثناء الاتصال ببوابة الدفع: ' . $e->getMessage(), 'danger');
                redirect('/erpapp/payments/checkout/' . $subscription_id);
                return;
            }
        } elseif ($payment_method === 'bank_transfer') {
            // في حالة التحويل البنكي، نحتاج إلى تحديث حالة الدفع إلى معلق
            $paymentGateway->updatePaymentStatus($payment_id, 'pending');

            flash('payment_pending', 'تم استلام طلب التحويل البنكي. سيتم تفعيل الاشتراك بعد التأكد من التحويل.', 'warning');
            redirect('/erpapp/subscriptions');
            return;
        } else {
            flash('payment_error', 'طريقة الدفع غير مدعومة.', 'danger');
            redirect('/erpapp/payments/checkout/' . $subscription_id);
            return;
        }

        try {
            // بدء المعاملة
            $this->db->beginTransaction();

            // تحديث حالة الدفع
            $stmt = $this->db->prepare("
                UPDATE payments
                SET status = 'completed', payment_date = NOW()
                WHERE payment_id = :payment_id
            ");
            $stmt->bindParam(':payment_id', $payment_id);
            $stmt->execute();

            // تحديث حالة الاشتراك
            $this->subscriptionModel->updateSubscriptionStatus($subscription_id, 'active');

            // الحصول على معلومات الاشتراك
            $company_id = $subscription['company_id'];

            // تحديث حالة الشركة
            $stmt = $this->db->prepare("
                UPDATE companies
                SET CompanyStatus = 'Active'
                WHERE CompanyID = :company_id
            ");
            $stmt->bindParam(':company_id', $company_id);
            $stmt->execute();

            // إنشاء رقم فاتورة
            $invoiceNumber = 'INV-' . date('Ymd') . '-' . $subscription_id;

            // تحديث معلومات الفاتورة
            $stmt = $this->db->prepare("
                UPDATE payments
                SET invoice_number = :invoice_number
                WHERE payment_id = :payment_id
            ");
            $stmt->bindParam(':invoice_number', $invoiceNumber);
            $stmt->bindParam(':payment_id', $payment_id);
            $stmt->execute();

            // إنهاء المعاملة
            $this->db->commit();

            // إرسال إشعار بنجاح الدفع
            // في بيئة الإنتاج، هنا سيتم إرسال بريد إلكتروني بتفاصيل الفاتورة

            flash('subscription_success', 'تم الدفع بنجاح وتفعيل الاشتراك. رقم الفاتورة: ' . $invoiceNumber, 'success');
            redirect('/erpapp/subscriptions');

        } catch (\Exception $e) {
            // التراجع عن المعاملة في حالة حدوث خطأ
            $this->db->rollBack();
            flash('payment_error', 'حدث خطأ أثناء معالجة الدفع: ' . $e->getMessage(), 'danger');
            redirect('/erpapp/payments/checkout/' . $subscription_id);
        }
    }

    /**
     * معالجة استجابة بوابة الدفع
     *
     * @return void
     */
    public function paymentCallback() {
        // الحصول على معرف المعاملة من بوابة الدفع (استثناء مسموح للدفع)
        $paymentReference = $_GET['payment_reference'] ?? null;

        if (!$paymentReference) {
            flash('payment_error', 'معرف المعاملة غير صالح.', 'danger');
            redirect('/erpapp/subscriptions');
            return;
        }

        // تهيئة نموذج بوابة الدفع
        $paymentGateway = new \App\System\Subscriptions\Models\PaymentGateway();

        // التحقق من حالة الدفع
        $paymentResult = $paymentGateway->verifyPayment($paymentReference);

        // الحصول على معرف الاشتراك من رقم المرجع
        $referenceNo = $paymentResult['reference_no'] ?? '';
        preg_match('/SUB-\d{8}-(\d+)/', $referenceNo, $matches);
        $subscription_id = $matches[1] ?? null;

        if (!$subscription_id) {
            flash('payment_error', 'معرف الاشتراك غير صالح.', 'danger');
            redirect('/erpapp/subscriptions');
            return;
        }

        // معالجة استجابة بوابة الدفع
        if ($paymentGateway->processPaymentResponse($paymentResult, $subscription_id)) {
            // تم الدفع بنجاح
            flash('subscription_success', 'تم الدفع بنجاح وتفعيل الاشتراك.', 'success');
        } else {
            // فشل الدفع
            flash('payment_error', 'فشلت عملية الدفع. يرجى المحاولة مرة أخرى.', 'danger');
        }

        redirect('/erpapp/subscriptions');
    }

    /**
     * إلغاء اشتراك
     *
     * @return void
     */
    public function cancel() {
        $subscription_id = $this->params['id'] ?? null;

        if (!$subscription_id) {
            flash('subscription_error', 'الاشتراك غير موجود', 'danger');
            redirect(base_url('subscriptions'));
        }

        // التحقق من أن الطلب هو POST
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect(base_url('subscriptions'));
        }

        // التحقق من CSRF token
        if (!csrf_check($_POST['csrf_token'] ?? '')) {
            flash('subscription_error', 'خطأ في التحقق من الأمان. يرجى المحاولة مرة أخرى.', 'danger');
            redirect(base_url('subscriptions'));
        }

        // إلغاء الاشتراك
        if ($this->subscriptionModel->cancelSubscription($subscription_id)) {
            flash('subscription_success', 'تم إلغاء الاشتراك بنجاح', 'success');
        } else {
            flash('subscription_error', 'حدث خطأ أثناء إلغاء الاشتراك. يرجى المحاولة مرة أخرى.', 'danger');
        }

        redirect(base_url('subscriptions'));
    }

    /**
     * تحديث حالة التجديد التلقائي
     *
     * @return void
     */
    public function toggleAutoRenew() {
        $subscription_id = $this->params['id'] ?? null;

        if (!$subscription_id) {
            flash('subscription_error', 'الاشتراك غير موجود', 'danger');
            redirect($_SERVER['HTTP_REFERER'] ?? base_url('companies'));
        }

        // التحقق من أن الطلب هو POST
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect($_SERVER['HTTP_REFERER'] ?? base_url('companies'));
        }

        // التحقق من CSRF token
        if (!csrf_check($_POST['csrf_token'] ?? '')) {
            flash('subscription_error', 'خطأ في التحقق من الأمان. يرجى المحاولة مرة أخرى.', 'danger');
            redirect($_SERVER['HTTP_REFERER'] ?? base_url('companies'));
        }

        $user_id = current_user_id();

        // التحقق من صلاحية المستخدم للاشتراك
        $stmt = $this->db->prepare("
            SELECT s.*, c.OwnerID
            FROM subscriptions s
            JOIN companies c ON s.company_id = c.CompanyID
            WHERE s.subscription_id = :subscription_id
        ");
        $stmt->bindParam(':subscription_id', $subscription_id);
        $stmt->execute();

        $subscription = $stmt->fetch(\PDO::FETCH_ASSOC);

        if (!$subscription || $subscription['OwnerID'] != $user_id) {
            flash('subscription_error', 'ليس لديك صلاحية لتعديل هذا الاشتراك', 'danger');
            redirect($_SERVER['HTTP_REFERER'] ?? base_url('companies'));
        }

        // التحقق من أن الاشتراك نشط
        if ($subscription['status'] !== 'active') {
            flash('subscription_error', 'لا يمكن تعديل التجديد التلقائي للاشتراكات غير النشطة', 'warning');
            redirect($_SERVER['HTTP_REFERER'] ?? base_url('companies'));
        }

        // تحديث حالة التجديد التلقائي
        $new_auto_renew = $subscription['is_auto_renew'] ? 0 : 1;

        if ($this->subscriptionModel->updateAutoRenew($subscription_id, $new_auto_renew)) {
            $message = $new_auto_renew ? 'تم تفعيل التجديد التلقائي بنجاح' : 'تم إيقاف التجديد التلقائي بنجاح';
            flash('subscription_success', $message, 'success');
        } else {
            flash('subscription_error', 'حدث خطأ أثناء تحديث التجديد التلقائي. يرجى المحاولة مرة أخرى.', 'danger');
        }

        redirect($_SERVER['HTTP_REFERER'] ?? base_url('companies'));
    }

    /**
     * تطبيق فلاتر الاشتراكات (POST form submission)
     */
    public function applyFilters()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect(base_url('subscriptions'));
            return;
        }

        // الحصول على بيانات الفلاتر من POST
        $filter_data = [
            'company_id' => $_POST['company_id'] ?? '',
            'status' => $_POST['status'] ?? '',
            'plan_id' => $_POST['plan_id'] ?? '',
            'date_from' => $_POST['date_from'] ?? '',
            'date_to' => $_POST['date_to'] ?? '',
            'per_page' => $_POST['per_page'] ?? 20,
            'current_page' => $_POST['current_page'] ?? 1
        ];

        // حفظ الفلاتر
        $success = save_filters_from_form('subscriptions', $filter_data);

        if ($success) {
            flash('success', 'تم تطبيق الفلاتر بنجاح');
        } else {
            flash('error', 'فشل في حفظ الفلاتر');
        }

        // إعادة توجيه إلى صفحة الاشتراكات
        redirect(base_url('subscriptions'));
    }

    /**
     * مسح فلاتر الاشتراكات
     */
    public function clearFilters()
    {
        // مسح الفلاتر المحفوظة للمستخدم
        clear_page_filters('subscriptions');

        // إعادة توجيه إلى صفحة الاشتراكات بدون فلاتر (URL نظيف)
        redirect(base_url('subscriptions'));
    }
}
