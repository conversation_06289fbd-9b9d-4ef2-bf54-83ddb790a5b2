<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="page-title">
                    <i class="fas fa-box"></i> <?= htmlspecialchars($product['product_name_ar']) ?>
                </h1>
                <div>
                    <?php if (canEdit('products')): ?>
                    <a href="<?= base_url('inventory/products/' . $product['product_id'] . '/edit') ?>" class="btn btn-warning me-2">
                        <i class="fas fa-edit me-1"></i> <?= __('تعديل المنتج') ?>
                    </a>
                    <?php endif; ?>
                    <a href="<?= base_url('inventory/products') ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> <?= __('العودة للمنتجات') ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- معلومات المنتج الأساسية -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i> <?= __('معلومات المنتج') ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted"><?= __('كود المنتج') ?></label>
                            <div class="fw-bold">
                                <span class="badge bg-secondary fs-6"><?= htmlspecialchars($product['product_code']) ?></span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted"><?= __('نوع المنتج') ?></label>
                            <div class="fw-bold">
                                <?php
                                $typeLabels = [
                                    'product' => ['text' => 'منتج', 'class' => 'primary'],
                                    'service' => ['text' => 'خدمة', 'class' => 'info'],
                                    'digital' => ['text' => 'رقمي', 'class' => 'warning']
                                ];
                                $type = $typeLabels[$product['product_type']] ?? ['text' => $product['product_type'], 'class' => 'secondary'];
                                ?>
                                <span class="badge bg-<?= $type['class'] ?> fs-6"><?= $type['text'] ?></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted"><?= __('اسم المنتج (عربي)') ?></label>
                            <div class="fw-bold fs-5"><?= htmlspecialchars($product['product_name_ar']) ?></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted"><?= __('اسم المنتج (إنجليزي)') ?></label>
                            <div class="fw-bold fs-5">
                                <?= !empty($product['product_name_en']) ? htmlspecialchars($product['product_name_en']) : '<span class="text-muted">غير محدد</span>' ?>
                            </div>
                        </div>
                    </div>

                    <?php if (!empty($product['description_ar']) || !empty($product['description_en'])): ?>
                    <div class="row">
                        <?php if (!empty($product['description_ar'])): ?>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted"><?= __('الوصف (عربي)') ?></label>
                            <div class="fw-bold">
                                <?= nl2br(htmlspecialchars($product['description_ar'])) ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <?php if (!empty($product['description_en'])): ?>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted"><?= __('الوصف (إنجليزي)') ?></label>
                            <div class="fw-bold">
                                <?= nl2br(htmlspecialchars($product['description_en'])) ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label text-muted"><?= __('الفئة') ?></label>
                            <div class="fw-bold">
                                <i class="fas fa-folder text-warning me-2"></i>
                                <?= htmlspecialchars($product['category_name_ar'] ?? 'غير محدد') ?>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label text-muted"><?= __('وحدة القياس') ?></label>
                            <div class="fw-bold">
                                <i class="fas fa-ruler text-info me-2"></i>
                                <?= htmlspecialchars($product['unit_name_ar'] ?? 'غير محدد') ?>
                                <?php if (!empty($product['unit_symbol_ar'])): ?>
                                    (<?= htmlspecialchars($product['unit_symbol_ar']) ?>)
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label text-muted"><?= __('الباركود') ?></label>
                            <div class="fw-bold">
                                <?= !empty($product['barcode']) ? htmlspecialchars($product['barcode']) : '<span class="text-muted">غير محدد</span>' ?>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted"><?= __('الحالة') ?></label>
                            <div>
                                <?php if ($product['is_active']): ?>
                                <span class="badge bg-success fs-6">
                                    <i class="fas fa-check-circle me-1"></i> <?= __('نشط') ?>
                                </span>
                                <?php else: ?>
                                <span class="badge bg-danger fs-6">
                                    <i class="fas fa-times-circle me-1"></i> <?= __('غير نشط') ?>
                                </span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted"><?= __('تتبع المخزون') ?></label>
                            <div>
                                <?php if ($product['track_inventory']): ?>
                                <span class="badge bg-info fs-6">
                                    <i class="fas fa-eye me-1"></i> <?= __('يتم تتبعه') ?>
                                </span>
                                <?php else: ?>
                                <span class="badge bg-secondary fs-6">
                                    <i class="fas fa-eye-slash me-1"></i> <?= __('لا يتم تتبعه') ?>
                                </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات الأسعار -->
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-dollar-sign me-2"></i> <?= __('معلومات الأسعار') ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label text-muted"><?= __('سعر التكلفة') ?></label>
                            <div class="fw-bold fs-5 text-danger">
                                <?= number_format($product['cost_price'], 2) ?> <?= __('ر.س') ?>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label text-muted"><?= __('سعر البيع') ?></label>
                            <div class="fw-bold fs-5 text-success">
                                <?= number_format($product['selling_price'], 2) ?> <?= __('ر.س') ?>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label text-muted"><?= __('هامش الربح') ?></label>
                            <div class="fw-bold fs-5 text-primary">
                                <?php
                                $margin = $product['cost_price'] > 0 ? 
                                    (($product['selling_price'] - $product['cost_price']) / $product['cost_price'] * 100) : 0;
                                echo number_format($margin, 2) . '%';
                                ?>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted"><?= __('معدل الضريبة') ?></label>
                            <div class="fw-bold">
                                <?= number_format($product['tax_rate'], 2) ?>%
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted"><?= __('السعر شامل الضريبة') ?></label>
                            <div class="fw-bold">
                                <?php
                                $priceWithTax = $product['selling_price'] * (1 + $product['tax_rate'] / 100);
                                echo number_format($priceWithTax, 2) . ' ' . __('ر.س');
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات المنتج -->
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i> <?= __('إحصائيات المنتج') ?>
                    </h5>
                </div>
                <div class="card-body">
                    <!-- إحصائيات سريعة -->
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="border rounded p-3">
                                <div class="text-primary mb-1">
                                    <i class="fas fa-warehouse fa-2x"></i>
                                </div>
                                <h4 class="mb-0">0</h4>
                                <small class="text-muted"><?= __('المستودعات') ?></small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="border rounded p-3">
                                <div class="text-success mb-1">
                                    <i class="fas fa-layer-group fa-2x"></i>
                                </div>
                                <h4 class="mb-0">0</h4>
                                <small class="text-muted"><?= __('إجمالي الكمية') ?></small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="border rounded p-3">
                                <div class="text-warning mb-1">
                                    <i class="fas fa-dollar-sign fa-2x"></i>
                                </div>
                                <h4 class="mb-0">0</h4>
                                <small class="text-muted"><?= __('إجمالي القيمة') ?></small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="border rounded p-3">
                                <div class="text-info mb-1">
                                    <i class="fas fa-exchange-alt fa-2x"></i>
                                </div>
                                <h4 class="mb-0">0</h4>
                                <small class="text-muted"><?= __('الحركات') ?></small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إعدادات المخزون -->
            <?php if ($product['track_inventory']): ?>
            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i> <?= __('إعدادات المخزون') ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted"><?= __('الحد الأدنى للمخزون') ?></label>
                        <div class="fw-bold text-warning">
                            <?= number_format($product['min_stock_level'], 2) ?>
                        </div>
                    </div>
                    <?php if ($product['max_stock_level']): ?>
                    <div class="mb-3">
                        <label class="form-label text-muted"><?= __('الحد الأقصى للمخزون') ?></label>
                        <div class="fw-bold text-info">
                            <?= number_format($product['max_stock_level'], 2) ?>
                        </div>
                    </div>
                    <?php endif; ?>
                    <div class="mb-3">
                        <label class="form-label text-muted"><?= __('نقطة إعادة الطلب') ?></label>
                        <div class="fw-bold text-danger">
                            <?= number_format($product['reorder_point'], 2) ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- معلومات إضافية -->
            <?php if ($product['weight'] || $product['dimensions']): ?>
            <div class="card mb-4">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info me-2"></i> <?= __('معلومات إضافية') ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($product['weight']): ?>
                    <div class="mb-3">
                        <label class="form-label text-muted"><?= __('الوزن') ?></label>
                        <div class="fw-bold">
                            <i class="fas fa-weight text-primary me-2"></i>
                            <?= number_format($product['weight'], 3) ?> <?= __('كجم') ?>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($product['dimensions']): ?>
                    <div class="mb-3">
                        <label class="form-label text-muted"><?= __('الأبعاد') ?></label>
                        <div class="fw-bold">
                            <i class="fas fa-cube text-info me-2"></i>
                            <?= htmlspecialchars($product['dimensions']) ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- أقسام إضافية -->
    <div class="row">
        <div class="col-12">
            <!-- تبويبات -->
            <ul class="nav nav-tabs" id="productTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="stock-tab" data-bs-toggle="tab" data-bs-target="#stock" type="button" role="tab">
                        <i class="fas fa-boxes me-2"></i> <?= __('المخزون') ?>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="movements-tab" data-bs-toggle="tab" data-bs-target="#movements" type="button" role="tab">
                        <i class="fas fa-exchange-alt me-2"></i> <?= __('الحركات') ?>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="history-tab" data-bs-toggle="tab" data-bs-target="#history" type="button" role="tab">
                        <i class="fas fa-history me-2"></i> <?= __('السجل') ?>
                    </button>
                </li>
            </ul>

            <!-- محتوى التبويبات -->
            <div class="tab-content" id="productTabsContent">
                <!-- تبويب المخزون -->
                <div class="tab-pane fade show active" id="stock" role="tabpanel">
                    <div class="card border-top-0">
                        <div class="card-body">
                            <div class="text-center py-5">
                                <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted"><?= __('لا يوجد مخزون') ?></h5>
                                <p class="text-muted"><?= __('لم يتم إضافة هذا المنتج لأي مستودع بعد') ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تبويب الحركات -->
                <div class="tab-pane fade" id="movements" role="tabpanel">
                    <div class="card border-top-0">
                        <div class="card-body">
                            <div class="text-center py-5">
                                <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted"><?= __('لا توجد حركات') ?></h5>
                                <p class="text-muted"><?= __('لم يتم تسجيل أي حركات لهذا المنتج بعد') ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تبويب السجل -->
                <div class="tab-pane fade" id="history" role="tabpanel">
                    <div class="card border-top-0">
                        <div class="card-body">
                            <div class="text-center py-5">
                                <i class="fas fa-history fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted"><?= __('لا يوجد سجل') ?></h5>
                                <p class="text-muted"><?= __('لم يتم تسجيل أي تغييرات على هذا المنتج بعد') ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.badge.fs-6 {
    font-size: 0.9rem !important;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.nav-tabs .nav-link {
    border-bottom: 3px solid transparent;
    color: #6c757d;
    font-weight: 500;
}

.nav-tabs .nav-link.active {
    border-bottom-color: #0d6efd;
    color: #0d6efd;
    background-color: transparent;
}

.nav-tabs .nav-link:hover {
    border-bottom-color: #0d6efd;
    color: #0d6efd;
}

.border.rounded {
    transition: all 0.3s ease;
}

.border.rounded:hover {
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تفعيل Bootstrap tabs
    var triggerTabList = [].slice.call(document.querySelectorAll('#productTabs button'));
    triggerTabList.forEach(function (triggerEl) {
        var tabTrigger = new bootstrap.Tab(triggerEl);
        
        triggerEl.addEventListener('click', function (event) {
            event.preventDefault();
            tabTrigger.show();
        });
    });
});
</script>
