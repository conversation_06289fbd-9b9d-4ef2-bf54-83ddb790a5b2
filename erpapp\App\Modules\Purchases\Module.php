<?php

namespace App\Modules\Purchases;

use App\Core\Module as BaseModule;

class Module extends BaseModule
{
    protected $name = 'Purchases';
    protected $version = '1.0.0';
    protected $description = 'وحدة المشتريات - إدارة الموردين ومجموعاتهم';

    public function getRoutes()
    {
        return [
            // لوحة تحكم المشتريات
            'purchases' => [
                'controller' => 'PurchaseController',
                'action' => 'dashboard'
            ],

            // مجموعات الموردين
            'purchases/supplier-groups' => [
                'controller' => 'SupplierGroupController',
                'action' => 'index'
            ],
            'purchases/supplier-groups/create' => [
                'controller' => 'SupplierGroupController',
                'action' => 'create'
            ],
            'purchases/supplier-groups/store' => [
                'controller' => 'SupplierGroupController',
                'action' => 'store'
            ],
            'purchases/supplier-groups/{id}' => [
                'controller' => 'SupplierGroupController',
                'action' => 'show'
            ],
            'purchases/supplier-groups/{id}/edit' => [
                'controller' => 'SupplierGroupController',
                'action' => 'edit'
            ],
            'purchases/supplier-groups/{id}/update' => [
                'controller' => 'SupplierGroupController',
                'action' => 'update'
            ],
            'purchases/supplier-groups/{id}/delete' => [
                'controller' => 'SupplierGroupController',
                'action' => 'delete'
            ],

            // الموردين
            'purchases/suppliers' => [
                'controller' => 'SupplierController',
                'action' => 'index'
            ],
            'purchases/suppliers/create' => [
                'controller' => 'SupplierController',
                'action' => 'create'
            ],
            'purchases/suppliers/store' => [
                'controller' => 'SupplierController',
                'action' => 'store'
            ],
            'purchases/suppliers/{id}' => [
                'controller' => 'SupplierController',
                'action' => 'show'
            ],
            'purchases/suppliers/{id}/edit' => [
                'controller' => 'SupplierController',
                'action' => 'edit'
            ],
            'purchases/suppliers/{id}/update' => [
                'controller' => 'SupplierController',
                'action' => 'update'
            ],
            'purchases/suppliers/{id}/delete' => [
                'controller' => 'SupplierController',
                'action' => 'delete'
            ],

            // عناوين الموردين
            'purchases/suppliers/{id}/addresses' => [
                'controller' => 'SupplierController',
                'action' => 'addresses'
            ],
            'purchases/suppliers/{id}/addresses/create' => [
                'controller' => 'SupplierController',
                'action' => 'createAddress'
            ],
            'purchases/suppliers/{id}/addresses/store' => [
                'controller' => 'SupplierController',
                'action' => 'storeAddress'
            ],
            'purchases/suppliers/{id}/addresses/{address_id}/edit' => [
                'controller' => 'SupplierController',
                'action' => 'editAddress'
            ],
            'purchases/suppliers/{id}/addresses/{address_id}/update' => [
                'controller' => 'SupplierController',
                'action' => 'updateAddress'
            ],
            'purchases/suppliers/{id}/addresses/{address_id}/delete' => [
                'controller' => 'SupplierController',
                'action' => 'deleteAddress'
            ],

            // حسابات الموردين البنكية
            'purchases/suppliers/{id}/bank-accounts' => [
                'controller' => 'SupplierController',
                'action' => 'bankAccounts'
            ],
            'purchases/suppliers/{id}/bank-accounts/create' => [
                'controller' => 'SupplierController',
                'action' => 'createBankAccount'
            ],
            'purchases/suppliers/{id}/bank-accounts/store' => [
                'controller' => 'SupplierController',
                'action' => 'storeBankAccount'
            ],
            'purchases/suppliers/{id}/bank-accounts/{account_id}/edit' => [
                'controller' => 'SupplierController',
                'action' => 'editBankAccount'
            ],
            'purchases/suppliers/{id}/bank-accounts/{account_id}/update' => [
                'controller' => 'SupplierController',
                'action' => 'updateBankAccount'
            ],
            'purchases/suppliers/{id}/bank-accounts/{account_id}/delete' => [
                'controller' => 'SupplierController',
                'action' => 'deleteBankAccount'
            ]
        ];
    }

    public function getMenuItems()
    {
        return [
            [
                'title' => 'المشتريات',
                'icon' => 'fas fa-shopping-cart',
                'url' => base_url('purchases'),
                'permission' => 'purchases.view',
                'children' => [
                    [
                        'title' => 'لوحة التحكم',
                        'icon' => 'fas fa-tachometer-alt',
                        'url' => base_url('purchases'),
                        'permission' => 'purchases.view'
                    ],
                    [
                        'title' => 'الموردين',
                        'icon' => 'fas fa-truck',
                        'url' => base_url('purchases/suppliers'),
                        'permission' => 'suppliers.view'
                    ],
                    [
                        'title' => 'مجموعات الموردين',
                        'icon' => 'fas fa-layer-group',
                        'url' => base_url('purchases/supplier-groups'),
                        'permission' => 'supplier_groups.view'
                    ]
                ]
            ]
        ];
    }

    public function getPermissions()
    {
        return [
            'purchases.view' => 'عرض المشتريات',
            'purchases.create' => 'إضافة مشتريات',
            'purchases.edit' => 'تعديل المشتريات',
            'purchases.delete' => 'حذف المشتريات',
            
            'suppliers.view' => 'عرض الموردين',
            'suppliers.create' => 'إضافة موردين',
            'suppliers.edit' => 'تعديل الموردين',
            'suppliers.delete' => 'حذف الموردين',
            
            'supplier_groups.view' => 'عرض مجموعات الموردين',
            'supplier_groups.create' => 'إضافة مجموعات الموردين',
            'supplier_groups.edit' => 'تعديل مجموعات الموردين',
            'supplier_groups.delete' => 'حذف مجموعات الموردين'
        ];
    }
}
