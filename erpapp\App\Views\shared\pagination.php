<?php
/**
 * ملف pagination موحد لجميع الجداول
 * 
 * المتغيرات المطلوبة:
 * - $pagination: معلومات pagination
 * - $filters: الفلاتر الحالية
 * - $base_url: الرابط الأساسي
 * - $filter_options: خيارات الفلاتر (اختياري)
 */

// التأكد من وجود المتغيرات المطلوبة
$pagination = $pagination ?? [];
$filters = $filters ?? [];
$base_url = $base_url ?? '';
$filter_options = $filter_options ?? [];

/**
 * بناء رابط pagination مع الحفاظ على الفلاتر
 */
function buildPaginationUrl($page, $filters, $base_url) {
    $params = ['page' => $page];
    
    // إضافة جميع الفلاتر الموجودة
    foreach ($filters as $key => $value) {
        if (!empty($value) && !in_array($key, ['limit', 'offset'])) {
            $params[$key] = $value;
        }
    }
    
    return $base_url . '?' . http_build_query($params);
}
?>

<!-- Filters Section -->
<div class="row mb-2">
    <div class="col-sm-5">
        <?php if (isset($create_url)): ?>
            <a href="<?= $create_url ?>" class="btn btn-danger mb-2">
                <i class="mdi mdi-plus-circle me-2"></i> <?= $create_button_text ?? 'إضافة جديد' ?>
            </a>
        <?php endif; ?>
    </div>
    <div class="col-sm-7">
        <div class="text-sm-end">
            <form method="GET" class="d-inline-block">
                <div class="row g-2">
                    <!-- عدد العناصر لكل صفحة -->
                    <div class="col-auto">
                        <select name="per_page" class="form-select form-select-sm" onchange="this.form.submit()">
                            <?php 
                            $perPageOptions = $filter_options['per_page_options'] ?? [10, 20, 50, 100];
                            $currentPerPage = $filters['per_page'] ?? 20;
                            foreach ($perPageOptions as $option): ?>
                                <option value="<?= $option ?>" <?= $currentPerPage == $option ? 'selected' : '' ?>>
                                    <?= $option ?> عنصر
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <!-- فلتر الحالة (إذا كان موجود) -->
                    <?php if (isset($filter_options['status_filter']) && $filter_options['status_filter']): ?>
                        <div class="col-auto">
                            <select name="status" class="form-select form-select-sm" onchange="this.form.submit()">
                                <option value="">جميع الحالات</option>
                                <?php foreach ($filter_options['status_options'] ?? [] as $value => $label): ?>
                                    <option value="<?= $value ?>" <?= ($filters['status'] ?? '') === $value ? 'selected' : '' ?>>
                                        <?= $label ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    <?php endif; ?>
                    
                    <!-- فلتر المجموعة (إذا كان موجود) -->
                    <?php if (isset($filter_options['group_filter']) && $filter_options['group_filter']): ?>
                        <div class="col-auto">
                            <select name="group_id" class="form-select form-select-sm" onchange="this.form.submit()">
                                <option value="">جميع المجموعات</option>
                                <?php foreach ($filter_options['groups'] ?? [] as $group): ?>
                                    <option value="<?= $group['group_number'] ?>" 
                                            <?= ($filters['group_id'] ?? '') == $group['group_number'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($group['name_ar']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    <?php endif; ?>
                    
                    <!-- البحث -->
                    <div class="col-auto">
                        <div class="input-group">
                            <input type="text" class="form-control form-control-sm" name="search" 
                                   placeholder="<?= $filter_options['search_placeholder'] ?? 'البحث...' ?>" 
                                   value="<?= htmlspecialchars($filters['search'] ?? '') ?>">
                            <button class="btn btn-primary btn-sm" type="submit">
                                <i class="mdi mdi-magnify"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- الحفاظ على الصفحة الحالية عند الفلترة -->
                    <input type="hidden" name="page" value="1">
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Pagination Info and Controls -->
<?php if (($pagination['total_items'] ?? 0) > 0): ?>
    <div class="row align-items-center mt-3">
        <div class="col-md-6">
            <div class="dataTables_info">
                عرض <?= $pagination['start_item'] ?> إلى <?= $pagination['end_item'] ?> 
                من <?= $pagination['total_items'] ?> عنصر
            </div>
        </div>
        <div class="col-md-6">
            <div class="dataTables_paginate paging_simple_numbers float-end">
                <?php if (($pagination['total_pages'] ?? 0) > 1): ?>
                    <ul class="pagination pagination-rounded mb-0">
                        <!-- Previous Button -->
                        <li class="page-item <?= !($pagination['has_previous'] ?? false) ? 'disabled' : '' ?>">
                            <?php if ($pagination['has_previous'] ?? false): ?>
                                <a class="page-link" href="<?= buildPaginationUrl($pagination['previous_page'], $filters, $base_url) ?>">
                                    السابق
                                </a>
                            <?php else: ?>
                                <span class="page-link">السابق</span>
                            <?php endif; ?>
                        </li>

                        <!-- Page Numbers -->
                        <?php
                        $currentPage = $pagination['current_page'] ?? 1;
                        $totalPages = $pagination['total_pages'] ?? 1;
                        $start = max(1, $currentPage - 2);
                        $end = min($totalPages, $currentPage + 2);
                        
                        // إضافة الصفحة الأولى إذا لم تكن ضمن النطاق
                        if ($start > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="<?= buildPaginationUrl(1, $filters, $base_url) ?>">1</a>
                            </li>
                            <?php if ($start > 2): ?>
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            <?php endif; ?>
                        <?php endif; ?>

                        <!-- الصفحات الحالية -->
                        <?php for ($i = $start; $i <= $end; $i++): ?>
                            <li class="page-item <?= $i == $currentPage ? 'active' : '' ?>">
                                <?php if ($i == $currentPage): ?>
                                    <span class="page-link"><?= $i ?></span>
                                <?php else: ?>
                                    <a class="page-link" href="<?= buildPaginationUrl($i, $filters, $base_url) ?>"><?= $i ?></a>
                                <?php endif; ?>
                            </li>
                        <?php endfor; ?>

                        <!-- إضافة الصفحة الأخيرة إذا لم تكن ضمن النطاق -->
                        <?php if ($end < $totalPages): ?>
                            <?php if ($end < $totalPages - 1): ?>
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            <?php endif; ?>
                            <li class="page-item">
                                <a class="page-link" href="<?= buildPaginationUrl($totalPages, $filters, $base_url) ?>">
                                    <?= $totalPages ?>
                                </a>
                            </li>
                        <?php endif; ?>

                        <!-- Next Button -->
                        <li class="page-item <?= !($pagination['has_next'] ?? false) ? 'disabled' : '' ?>">
                            <?php if ($pagination['has_next'] ?? false): ?>
                                <a class="page-link" href="<?= buildPaginationUrl($pagination['next_page'], $filters, $base_url) ?>">
                                    التالي
                                </a>
                            <?php else: ?>
                                <span class="page-link">التالي</span>
                            <?php endif; ?>
                        </li>
                    </ul>
                <?php endif; ?>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- CSS للتصميم -->
<style>
/* تحسين مظهر pagination */
.dataTables_info {
    color: #6c757d;
    font-size: 0.875rem;
    padding-top: 0.5rem;
}

.pagination-rounded .page-link {
    border-radius: 0.375rem !important;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: #6c757d;
}

.pagination-rounded .page-item.active .page-link {
    background-color: #727cf5;
    border-color: #727cf5;
    color: white;
}

.pagination-rounded .page-link:hover {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    color: #495057;
}

.pagination-rounded .page-item.disabled .page-link {
    color: #adb5bd;
    background-color: #fff;
    border-color: #dee2e6;
}

.form-select-sm, .form-control-sm {
    font-size: 0.875rem;
}

.btn-group .btn {
    border-radius: 0.25rem;
}

.btn-group .btn + .btn {
    margin-left: 2px;
}
</style>
