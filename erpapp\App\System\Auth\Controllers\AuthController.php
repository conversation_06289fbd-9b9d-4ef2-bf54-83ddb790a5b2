<?php
namespace App\System\Auth\Controllers;

use App\System\Auth\Services\AuthService;

/**
 * متحكم المصادقة
 */
class AuthController
{
    /**
     * معلمات المسار
     */
    protected $params = [];

    /**
     * خدمة المصادقة
     */
    protected $authService;

    /**
     * Constructor
     *
     * @param array $params معلمات المسار
     */
    public function __construct($params = [])
    {
        $this->params = $params;
        $this->authService = new AuthService();
    }

    /**
     * تسجيل الدخول
     *
     * @return void
     */
    public function login()
    {
        // إذا كان المستخدم مسجل الدخول بالفعل، قم بتوجيهه إلى لوحة التحكم
        if (is_logged_in()) {
            redirect(base_url('home'));
        }

        // التحقق من إرسال النموذج
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // التحقق من رمز CSRF
            if (!csrf_check($_POST['csrf_token'] ?? '')) {
                flash('error', __('رمز CSRF غير صالح'), 'danger');
                redirect(base_url('login'));
            }

            // الحصول على بيانات النموذج
            $email = $_POST['email'] ?? '';
            $password = $_POST['password'] ?? '';
            $remember = isset($_POST['remember']);

            // تسجيل الدخول
            $result = $this->authService->login($email, $password, $remember);

            if ($result['success']) {
                redirect(base_url('home'));
            } else {
                flash('error', $result['message'], 'danger');
                redirect(base_url('login'));
            }
        }

        // عرض نموذج تسجيل الدخول
        view('Auth::login', [
            'title' => __('تسجيل الدخول')
        ]);
    }

    /**
     * التسجيل
     *
     * @return void
     */
    public function register()
    {
        // إذا كان المستخدم مسجل الدخول بالفعل، قم بتوجيهه إلى لوحة التحكم
        if (is_logged_in()) {
            redirect(base_url('dashboard'));
        }

        // التحقق من إرسال النموذج
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // التحقق من رمز CSRF
            if (!csrf_check($_POST['csrf_token'] ?? '')) {
                flash('error', __('رمز CSRF غير صالح'), 'danger');
                redirect(base_url('register'));
            }

            // الحصول على بيانات النموذج
            $userData = [
                'first_name' => $_POST['first_name'] ?? '',
                'last_name' => $_POST['last_name'] ?? '',
                'username' => $_POST['username'] ?? '',
                'email' => $_POST['email'] ?? '',
                'phone' => $_POST['phone'] ?? '',
                'password' => $_POST['password'] ?? '',
                'password_confirm' => $_POST['password_confirm'] ?? ''
            ];

            // تسجيل المستخدم
            $result = $this->authService->register($userData);

            if ($result['success']) {
                flash('success', $result['message'], 'success');
                redirect(base_url('login'));
            } else {
                $_SESSION['errors'] = $result['errors'] ?? [$result['message']];
                $_SESSION['old'] = $_POST;
                redirect(base_url('register'));
            }
        }

        // عرض نموذج التسجيل
        view('Auth::register', [
            'title' => __('إنشاء حساب جديد'),
            'errors' => $_SESSION['errors'] ?? [],
            'old' => $_SESSION['old'] ?? []
        ]);

        // مسح بيانات الجلسة
        unset($_SESSION['errors']);
        unset($_SESSION['old']);
    }

    /**
     * تسجيل الخروج
     *
     * @return void
     */
    public function logout()
    {
        // تسجيل الخروج
        $this->authService->logout();

        // إعادة التوجيه إلى صفحة تسجيل الدخول
        redirect(base_url('login'));
    }

    /**
     * استعادة كلمة المرور
     *
     * @return void
     */
    public function forgotPassword()
    {
        // إذا كان المستخدم مسجل الدخول بالفعل، قم بتوجيهه إلى لوحة التحكم
        if (is_logged_in()) {
            redirect(base_url('dashboard'));
        }

        // التحقق من إرسال النموذج
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // التحقق من رمز CSRF
            if (!csrf_check($_POST['csrf_token'] ?? '')) {
                flash('error', __('رمز CSRF غير صالح'), 'danger');
                redirect(base_url('forgot-password'));
            }

            // الحصول على بيانات النموذج
            $email = $_POST['email'] ?? '';

            // استعادة كلمة المرور
            $result = $this->authService->forgotPassword($email);

            if ($result['success']) {
                flash('success', $result['message'], 'success');
                redirect(base_url('login'));
            } else {
                flash('error', $result['message'], 'danger');
                redirect(base_url('forgot-password'));
            }
        }

        // عرض نموذج استعادة كلمة المرور
        view('Auth::forgot_password', [
            'title' => __('استعادة كلمة المرور')
        ]);
    }

    /**
     * إعادة تعيين كلمة المرور
     *
     * @return void
     */
    public function resetPassword()
    {
        // إذا كان المستخدم مسجل الدخول بالفعل، قم بتوجيهه إلى لوحة التحكم
        if (is_logged_in()) {
            redirect(base_url('dashboard'));
        }

        // الحصول على الرمز من عنوان URL
        $token = $this->params['token'] ?? '';

        if (empty($token)) {
            flash('error', __('رمز إعادة تعيين كلمة المرور غير صالح'), 'danger');
            redirect(base_url('login'));
        }

        // التحقق من إرسال النموذج
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // التحقق من رمز CSRF
            if (!csrf_check($_POST['csrf_token'] ?? '')) {
                flash('error', __('رمز CSRF غير صالح'), 'danger');
                redirect(base_url('reset-password/' . $token));
            }

            // الحصول على بيانات النموذج
            $password = $_POST['password'] ?? '';
            $password_confirm = $_POST['password_confirm'] ?? '';

            // إعادة تعيين كلمة المرور
            $result = $this->authService->resetPassword($token, $password, $password_confirm);

            if ($result['success']) {
                flash('success', $result['message'], 'success');
                redirect(base_url('login'));
            } else {
                flash('error', $result['message'], 'danger');
                redirect(base_url('reset-password/' . $token));
            }
        }

        // الحصول على معلومات المستخدم
        $reset = $this->authService->getUserByResetToken($token);

        if (!$reset['success']) {
            flash('error', $reset['message'], 'danger');
            redirect(base_url('login'));
        }

        // عرض نموذج إعادة تعيين كلمة المرور
        view('Auth::reset_password', [
            'title' => __('إعادة تعيين كلمة المرور'),
            'token' => $token,
            'email' => $reset['user']['Email']
        ]);
    }
}
