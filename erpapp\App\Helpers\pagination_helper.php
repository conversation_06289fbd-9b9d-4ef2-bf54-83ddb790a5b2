<?php
/**
 * دوال مساعدة بسيطة للـ pagination
 */

/**
 * إعداد pagination بسيط
 */
function setup_pagination($defaultPerPage = 20) {
    $page = max(1, (int)($_GET['page'] ?? 1));
    $perPage = max(10, min(100, (int)($_GET['per_page'] ?? $defaultPerPage)));
    $offset = ($page - 1) * $perPage;
    
    return [
        'page' => $page,
        'per_page' => $perPage,
        'offset' => $offset,
        'limit' => $perPage
    ];
}

/**
 * حساب معلومات pagination
 */
function calculate_pagination($page, $perPage, $totalItems) {
    $totalPages = ceil($totalItems / $perPage);
    
    return [
        'current_page' => $page,
        'per_page' => $perPage,
        'total_items' => $totalItems,
        'total_pages' => $totalPages,
        'has_previous' => $page > 1,
        'has_next' => $page < $totalPages,
        'previous_page' => $page > 1 ? $page - 1 : null,
        'next_page' => $page < $totalPages ? $page + 1 : null,
        'start_item' => $totalItems > 0 ? (($page - 1) * $perPage) + 1 : 0,
        'end_item' => min($page * $perPage, $totalItems)
    ];
}

/**
 * إعداد الفلاتر من الطلب
 */
function setup_filters($paginationData, $additionalFilters = []) {
    $filters = [
        'search' => $_GET['search'] ?? '',
        'per_page' => $paginationData['per_page'],
        'limit' => $paginationData['limit'],
        'offset' => $paginationData['offset']
    ];
    
    // إضافة فلاتر إضافية
    foreach ($additionalFilters as $key => $default) {
        $filters[$key] = $_GET[$key] ?? $default;
    }
    
    return $filters;
}

/**
 * إضافة pagination للـ SQL (للـ Models)
 */
function add_pagination_to_sql($sql, $filters) {
    if (isset($filters['limit']) && isset($filters['offset'])) {
        $limit = (int)$filters['limit'];
        $offset = (int)$filters['offset'];
        $sql .= " LIMIT {$limit} OFFSET {$offset}";
    }
    return $sql;
}

/**
 * إضافة البحث للـ SQL (للـ Models)
 */
function add_search_to_sql($sql, $params, $filters, $searchFields) {
    if (isset($filters['search']) && $filters['search'] !== '') {
        $searchConditions = [];
        $searchTerm = '%' . $filters['search'] . '%';
        
        foreach ($searchFields as $field) {
            $searchConditions[] = "{$field} LIKE ?";
            $params[] = $searchTerm;
        }
        
        if (!empty($searchConditions)) {
            $sql .= " AND (" . implode(' OR ', $searchConditions) . ")";
        }
    }
    
    return [$sql, $params];
}

/**
 * إضافة فلتر الحالة للـ SQL (للـ Models)
 */
function add_status_filter_to_sql($sql, $params, $filters, $statusField = 'status') {
    if (isset($filters['status']) && $filters['status'] !== '') {
        $sql .= " AND {$statusField} = ?";
        $params[] = $filters['status'];
    }
    
    return [$sql, $params];
}

/**
 * إضافة فلتر المجموعة للـ SQL (للـ Models)
 */
function add_group_filter_to_sql($sql, $params, $filters, $groupField = 'group_id') {
    if (isset($filters['group_id']) && $filters['group_id'] !== '') {
        $sql .= " AND {$groupField} = ?";
        $params[] = $filters['group_id'];
    }
    
    return [$sql, $params];
}

/**
 * دالة شاملة لإعداد صفحة index مع pagination
 */
function setup_index_page($model, $company_id, $config = []) {
    // إعداد pagination
    $paginationSetup = setup_pagination($config['default_per_page'] ?? 20);
    
    // إعداد الفلاتر
    $additionalFilters = $config['additional_filters'] ?? [];
    $filters = setup_filters($paginationSetup, $additionalFilters);
    
    // الحصول على البيانات
    $data = $model->getByCompany($company_id, $filters);
    
    // الحصول على العدد الكلي
    $filtersForCount = $filters;
    unset($filtersForCount['limit'], $filtersForCount['offset']);
    $totalItems = $model->getCountByCompany($company_id, $filtersForCount);
    
    // حساب معلومات pagination
    $pagination = calculate_pagination($paginationSetup['page'], $paginationSetup['per_page'], $totalItems);
    
    return [
        'data' => $data,
        'pagination' => $pagination,
        'filters' => $filters
    ];
}
