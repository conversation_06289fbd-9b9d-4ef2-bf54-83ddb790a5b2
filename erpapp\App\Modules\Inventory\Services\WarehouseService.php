<?php
namespace App\Modules\Inventory\Services;

use App\Modules\Inventory\Models\Warehouse;
use PDO;
use Exception;

/**
 * Warehouse Service - خدمة المستودعات
 */
class WarehouseService
{
    /**
     * Warehouse model
     */
    protected $warehouseModel;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->warehouseModel = new Warehouse();
    }

    /**
     * الحصول على إحصائيات المستودعات للوحة التحكم
     */
    public function getDashboardStats($company_id)
    {
        try {
            $stats = $this->warehouseModel->getStats($company_id);

            // إضافة إحصائيات إضافية
            $stats['warehouses_with_stock'] = $this->getWarehousesWithStockCount($company_id);
            $stats['total_capacity'] = $this->getTotalCapacity($company_id);
            $stats['total_usage'] = $this->getTotalUsage($company_id);
            $stats['usage_percentage'] = $this->getOverallUsagePercentage($company_id);

            return $stats;

        } catch (Exception $e) {
            error_log('Error getting warehouse dashboard stats: ' . $e->getMessage());
            return [
                'total_warehouses' => 0,
                'active_warehouses' => 0,
                'warehouses_with_stock' => 0,
                'total_capacity' => 0,
                'total_usage' => 0,
                'usage_percentage' => 0
            ];
        }
    }

    /**
     * الحصول على عدد المستودعات التي تحتوي على مخزون
     */
    private function getWarehousesWithStockCount($company_id)
    {
        try {
            global $db;

            $sql = "SELECT COUNT(DISTINCT w.warehouse_id)
                    FROM inventory_warehouses w
                    INNER JOIN inventory_stock s ON w.warehouse_id = s.warehouse_id
                    WHERE w.company_id = ? AND w.is_active = 1 AND s.quantity_on_hand > 0";

            $stmt = $db->prepare($sql);
            $stmt->execute([$company_id]);
            return $stmt->fetchColumn() ?: 0;

        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * الحصول على إجمالي السعة
     */
    private function getTotalCapacity($company_id)
    {
        try {
            global $db;

            $sql = "SELECT SUM(capacity) FROM inventory_warehouses
                    WHERE company_id = ? AND is_active = 1 AND capacity IS NOT NULL";

            $stmt = $db->prepare($sql);
            $stmt->execute([$company_id]);
            return $stmt->fetchColumn() ?: 0;

        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * الحصول على إجمالي الاستخدام
     */
    private function getTotalUsage($company_id)
    {
        try {
            global $db;

            $sql = "SELECT SUM(current_usage) FROM inventory_warehouses
                    WHERE company_id = ? AND is_active = 1";

            $stmt = $db->prepare($sql);
            $stmt->execute([$company_id]);
            return $stmt->fetchColumn() ?: 0;

        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * الحصول على نسبة الاستخدام الإجمالية
     */
    private function getOverallUsagePercentage($company_id)
    {
        try {
            $totalCapacity = $this->getTotalCapacity($company_id);
            $totalUsage = $this->getTotalUsage($company_id);

            if ($totalCapacity > 0) {
                return round(($totalUsage / $totalCapacity) * 100, 2);
            }

            return 0;

        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * الحصول على المستودعات القريبة من الامتلاء
     */
    public function getWarehousesNearCapacity($company_id, $threshold = 80)
    {
        try {
            global $db;

            $sql = "SELECT warehouse_id, warehouse_name_ar, capacity, current_usage,
                           ROUND((current_usage / capacity) * 100, 2) as usage_percentage
                    FROM inventory_warehouses
                    WHERE company_id = ? AND is_active = 1
                    AND capacity IS NOT NULL AND capacity > 0
                    AND (current_usage / capacity) * 100 >= ?
                    ORDER BY usage_percentage DESC";

            $stmt = $db->prepare($sql);
            $stmt->execute([$company_id, $threshold]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * تحديث استخدام جميع المستودعات
     */
    public function updateAllWarehousesUsage($company_id)
    {
        try {
            $warehouses = $this->warehouseModel->getByCompany($company_id, ['is_active' => 1]);

            foreach ($warehouses as $warehouse) {
                $this->warehouseModel->updateCurrentUsage($warehouse['warehouse_id'], $company_id);
            }

            return true;

        } catch (Exception $e) {
            error_log('Error updating warehouses usage: ' . $e->getMessage());
            return false;
        }
    }
}