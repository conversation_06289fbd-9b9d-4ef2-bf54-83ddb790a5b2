/* DataTable Component Styles - تصميم موحد للجداول */

/* تحسين مظهر pagination */
.dataTables_info {
    color: #6c757d;
    font-size: 0.875rem;
    padding-top: 0.5rem;
}

.pagination-rounded .page-link {
    border-radius: 0.375rem !important;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: #6c757d;
    transition: all 0.2s ease-in-out;
}

.pagination-rounded .page-item.active .page-link {
    background-color: #727cf5;
    border-color: #727cf5;
    color: white;
    box-shadow: 0 2px 4px rgba(114, 124, 245, 0.3);
}

.pagination-rounded .page-link:hover {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    color: #495057;
    transform: translateY(-1px);
}

.pagination-rounded .page-item.disabled .page-link {
    color: #adb5bd;
    background-color: #fff;
    border-color: #dee2e6;
    cursor: not-allowed;
}

/* تحسين مظهر الجدول */
.table-responsive {
    border-radius: 0.375rem;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05);
}

.table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 1rem 0.75rem;
}

.table td {
    vertical-align: middle;
    padding: 0.875rem 0.75rem;
    border-bottom: 1px solid #f1f3f4;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transition: background-color 0.2s ease-in-out;
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* تحسين مظهر الفلاتر */
.form-select-sm {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-select-sm:focus {
    border-color: #727cf5;
    box-shadow: 0 0 0 0.2rem rgba(114, 124, 245, 0.25);
}

.form-control-sm {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control-sm:focus {
    border-color: #727cf5;
    box-shadow: 0 0 0 0.2rem rgba(114, 124, 245, 0.25);
}

/* تحسين مظهر الأزرار */
.btn-group .btn {
    border-radius: 0.25rem;
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
    transition: all 0.2s ease-in-out;
}

.btn-group .btn + .btn {
    margin-left: 2px;
}

.btn-group .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.25rem;
}

/* تحسين مظهر البطاقات */
.card {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-body {
    padding: 1.5rem;
}

/* تحسين مظهر الـ badges */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    font-weight: 500;
}

.badge.bg-success {
    background-color: #28a745 !important;
}

.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #212529 !important;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
}

/* تحسين مظهر الرسائل الفارغة */
.empty-state {
    padding: 3rem 1rem;
    text-align: center;
}

.empty-state .mdi-48px {
    font-size: 3rem;
    color: #dee2e6;
    margin-bottom: 1rem;
}

.empty-state p {
    color: #6c757d;
    font-size: 1.125rem;
    margin-bottom: 1.5rem;
}

/* تحسين مظهر الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.stats-card h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-card h5 {
    font-size: 0.875rem;
    opacity: 0.8;
    margin-bottom: 0;
}

/* تحسين مظهر البحث */
.search-input-group {
    position: relative;
}

.search-input-group .form-control {
    padding-right: 2.5rem;
}

.search-input-group .search-icon {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    pointer-events: none;
}

/* تحسين مظهر التحميل */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 0.25rem solid #f3f3f3;
    border-top: 0.25rem solid #727cf5;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-group .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .pagination-rounded .page-link {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .dataTables_info {
        text-align: center;
        margin-bottom: 1rem;
    }
    
    .dataTables_paginate {
        text-align: center !important;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* تحسين مظهر الـ tooltips */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: #343a40;
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
}

/* تحسين مظهر الـ modals */
.modal-content {
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid #f1f3f4;
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid #f1f3f4;
    padding: 1.5rem;
}
